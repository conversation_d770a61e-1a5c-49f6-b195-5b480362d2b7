import requests
import json
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib3
import os
import re
from typing import Dict, List, Any

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class SmartHelpInfoBloxMCP(BaseHTTPRequestHandler):
    """InfoBlox MCP with Smart Help and Instant Suggestions"""
    
    def __init__(self, *args, **kwargs):
        self.grid_master = os.getenv("GRID_MASTER_URL", "https://192.168.1.1/wapi/v2.13.1")
        self.username = os.getenv("INFOBLOX_USERNAME", "admin")
        self.password = os.getenv("INFOBLOX_PASSWORD", "infoblox")
        self.network_view = os.getenv("NETWORK_VIEW", "default")
        
        # Create session
        self.session = requests.Session()
        self.session.auth = (self.username, self.password)
        self.session.verify = False
        self.session.headers.update({'Content-Type': 'application/json'})
        
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == "/health":
            self.send_json({"status": "healthy", "grid_master": self.grid_master})
        elif self.path == "/v1/models":
            self.send_json({
                "data": [{
                    "id": "infoblox-assistant",
                    "object": "model",
                    "owned_by": "infoblox-smart"
                }]
            })
        else:
            self.send_json({"status": "ok"})
    
    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        body = self.rfile.read(content_length).decode('utf-8')
        
        if self.path == "/v1/chat/completions":
            try:
                data = json.loads(body)
                messages = data.get("messages", [])
                
                if messages:
                    user_message = messages[-1].get("content", "")
                    response_content = self.process_smart_query(user_message)
                else:
                    response_content = self.get_help_message()
                
                response = {
                    "id": "chatcmpl-" + str(int(datetime.now().timestamp())),
                    "object": "chat.completion",
                    "created": int(datetime.now().timestamp()),
                    "model": "infoblox-assistant",
                    "choices": [{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_content
                        },
                        "finish_reason": "stop"
                    }]
                }
                self.send_json(response)
                
            except Exception as e:
                self.send_json({"error": str(e)})
    
    def process_smart_query(self, query: str) -> str:
        """Process query with smart help"""
        query_lower = query.lower().strip()
        
        # Instant help triggers
        if query_lower in ["?", "help", "h"]:
            return self.get_instant_help()
        
        # Contextual help
        elif query_lower.startswith("help "):
            topic = query_lower[5:]
            return self.get_contextual_help(topic)
        
        # Single word triggers with suggestions
        elif query_lower == "list":
            return self.get_list_suggestions()
        
        elif query_lower == "network" or query_lower == "networks":
            return self.get_network_suggestions()
        
        elif query_lower == "dns":
            return self.get_dns_suggestions()
        
        elif query_lower == "dhcp":
            return self.get_dhcp_suggestions()
        
        elif query_lower == "ip" or query_lower == "ips":
            return self.get_ip_suggestions()
        
        # Partial commands with smart completion
        elif query_lower.startswith("list "):
            return self.suggest_list_operations(query_lower[5:])
        
        elif query_lower.startswith("create "):
            return self.suggest_create_operations(query_lower[7:])
        
        elif query_lower.startswith("find "):
            return self.suggest_find_operations(query_lower[5:])
        
        # TOOL invocation
        elif query.startswith("TOOL:"):
            try:
                tool_data = json.loads(query[5:])
                result = self.execute_tool(tool_data.get("tool"), tool_data.get("params", {}))
                return json.dumps(result, indent=2)
            except:
                return "❌ Invalid TOOL format. Use: TOOL:{\"tool\":\"tool_name\",\"params\":{}}"
        
        # Natural language processing
        else:
            return self.process_natural_language(query)
    
    def get_instant_help(self) -> str:
        """Get instant help menu"""
        return """🚀 **InfoBlox MCP Quick Help**

**🔍 Try these single words for instant suggestions:**
• `list` - See all list operations
• `network` - Network-related tools
• `dns` - DNS management tools
• `dhcp` - DHCP tools
• `ip` - IP address tools

**📝 Start typing for smart suggestions:**
• `list ` → Shows what you can list
• `create ` → Shows what you can create
• `find ` → Shows search options

**🎯 Common Operations:**
• `list all networks` - Show all networks
• `list all networks with extended attributes` - With extattrs
• `audit network 10.0.0.0/24` - Network audit
• `list dns zones` - Show DNS zones
• `find available ips in 10.0.0.0/24` - Free IPs

**💡 Tips:**
• Type `help <topic>` for detailed help
• Use `?` anytime for this menu
• Natural language works for most operations!"""
    
    def get_list_suggestions(self) -> str:
        """Suggestions when user types 'list'"""
        return """📋 **List Operations Available:**

**Networks:**
• `list all networks` - Show all networks
• `list all networks with extended attributes` - Include extattrs
• `list network containers` - Network containers

**DNS:**
• `list dns zones` - All DNS zones  
• `list a records` - A records
• `list host records` - Host records
• `list ptr records` - PTR records
• `list cname records` - CNAME records

**DHCP:**
• `list dhcp ranges` - DHCP ranges
• `list dhcp leases` - Active leases
• `list fixed addresses` - Fixed/reserved IPs

**IP Management:**
• `list ips in network 10.0.0.0/24` - IPs in specific network
• `list used ips` - All used IPs

💡 **Tip:** Just continue typing! Example: `list all networks`"""
    
    def get_network_suggestions(self) -> str:
        """Suggestions for network operations"""
        return """🌐 **Network Operations:**

**Listing:**
• `list all networks` - Show all networks
• `list all networks with extended attributes`
• `list network containers`

**Creating:**
• `create network *********/24 with comment "Test"`

**Searching:**
• `find network 10.0.0.0/24`
• `audit network 10.0.0.0/24` - Detailed audit

**Information:**
• `show network 10.0.0.0/24 details`
• `get network usage for 10.0.0.0/24`

**Direct Tool Examples:**
```
TOOL:{"tool":"list_network","params":{"return_fields":"network,comment,extattrs"}}
TOOL:{"tool":"create_network","params":{"network":"*********/24","comment":"New network"}}
```"""
    
    def get_dns_suggestions(self) -> str:
        """Suggestions for DNS operations"""
        return """🌍 **DNS Operations:**

**Zones:**
• `list dns zones` - All DNS zones
• `list forward zones` - Forward zones only
• `list reverse zones` - Reverse zones only

**Records:**
• `list a records` - A records
• `list host records` - Host records
• `list cname records` - Aliases
• `list ptr records` - Reverse DNS
• `list mx records` - Mail records
• `list txt records` - Text records

**Creating Records:**
• `create host record server.example.com with ip **********`
• `create a record www.example.com pointing to *********`

**Direct Tool Examples:**
```
TOOL:{"tool":"list_zone_auth","params":{"return_fields":"fqdn,comment"}}
TOOL:{"tool":"list_record_host","params":{"zone":"example.com"}}
```"""
    
    def get_dhcp_suggestions(self) -> str:
        """Suggestions for DHCP operations"""
        return """🔄 **DHCP Operations:**

**Ranges:**
• `list dhcp ranges` - All DHCP ranges
• `list dhcp ranges in network 10.0.0.0/24`

**Leases:**
• `list dhcp leases` - Active leases
• `list dhcp leases for mac 00:11:22:33:44:55`

**Fixed Addresses:**
• `list fixed addresses` - Reserved IPs
• `create fixed address ********** for mac 00:11:22:33:44:55`

**Direct Tool Examples:**
```
TOOL:{"tool":"list_range","params":{"network":"10.0.0.0/24"}}
TOOL:{"tool":"list_lease","params":{"address":"10.0.0.*"}}
```"""
    
    def get_ip_suggestions(self) -> str:
        """Suggestions for IP operations"""
        return """🔢 **IP Address Operations:**

**Searching:**
• `search ip **********` - IP information
• `find available ips in 10.0.0.0/24` - Free IPs
• `list ips in network 10.0.0.0/24` - All IPs

**Status:**
• `list used ips in 10.0.0.0/24`
• `list unused ips in 10.0.0.0/24`

**Information:**
• `show ip ********** details`
• `who owns ip **********`

**Direct Tool Examples:**
```
TOOL:{"tool":"list_ipv4address","params":{"network":"10.0.0.0/24","status":"USED"}}
```"""
    
    def suggest_list_operations(self, partial: str) -> str:
        """Suggest completions for 'list ...'"""
        suggestions = {
            "a": ["all networks", "a records", "all dns zones"],
            "n": ["networks", "network containers"],
            "d": ["dns zones", "dhcp ranges", "dhcp leases"],
            "h": ["host records", "dhcp ranges"],
            "i": ["ips in network", "ipv6 networks"],
            "z": ["zones", "zone delegations"],
            "r": ["ranges", "records", "reverse zones"],
            "f": ["fixed addresses", "forward zones"],
            "c": ["cname records", "containers"],
            "p": ["ptr records"],
            "m": ["mx records"],
            "t": ["txt records"]
        }
        
        # Find matching suggestions
        matches = []
        for key, values in suggestions.items():
            if partial.startswith(key):
                matches.extend(values)
        
        if not matches:
            # Show all options
            all_options = []
            for values in suggestions.values():
                all_options.extend(values)
            matches = sorted(set(all_options))
        
        response = f"💡 **Complete your command 'list {partial}...':**\n\n"
        for match in matches[:10]:
            response += f"• `list {match}`\n"
        
        return response
    
    def suggest_create_operations(self, partial: str) -> str:
        """Suggest completions for 'create ...'"""
        return f"""💡 **Complete your command 'create {partial}...':**

• `create network 10.x.x.x/24`
• `create host record hostname.domain.com`
• `create a record name.domain.com`
• `create fixed address 10.x.x.x`
• `create dns zone domain.com`

**Examples:**
```
create network *********/24 with comment "Test network"
create host record server.example.com with ip **********
```"""
    
    def suggest_find_operations(self, partial: str) -> str:
        """Suggest completions for 'find ...'"""
        return f"""💡 **Complete your command 'find {partial}...':**

• `find available ips in 10.x.x.x/24`
• `find network 10.x.x.x`
• `find ip 10.x.x.x`
• `find host hostname`
• `find mac address XX:XX:XX:XX:XX:XX`

**Examples:**
```
find available ips in 10.0.0.0/24
find network containing ip **********
```"""
    
    def get_contextual_help(self, topic: str) -> str:
        """Get help for specific topic"""
        help_topics = {
            "network": self.get_network_suggestions(),
            "networks": self.get_network_suggestions(),
            "dns": self.get_dns_suggestions(),
            "dhcp": self.get_dhcp_suggestions(),
            "ip": self.get_ip_suggestions(),
            "ips": self.get_ip_suggestions(),
            "list": self.get_list_suggestions(),
            "extended": """📎 **Extended Attributes Help:**

Extended attributes (extattrs) are custom fields in InfoBlox.

**To see extended attributes:**
• `list all networks with extended attributes`
• `show network 10.0.0.0/24 with extended attributes`

**In direct tools, use return_fields:**
```
TOOL:{"tool":"list_network","params":{"return_fields":"network,comment,extattrs"}}
```

**Common extended attributes:**
• Building
• Department  
• AWS_AccountId
• Environment
• Owner""",
            "tool": """🔧 **Direct Tool Usage:**

Format: `TOOL:{"tool":"tool_name","params":{...}}`

**Examples:**
```
TOOL:{"tool":"list_network","params":{"max_results":100}}
TOOL:{"tool":"get_network","params":{"ref":"network/ZG5z..."}}
TOOL:{"tool":"create_network","params":{"network":"*********/24"}}
```

**Common Parameters:**
• max_results: Limit results
• return_fields: Specify fields to return
• network_view: Specify view (default: "default")"""
        }
        
        return help_topics.get(topic, f"❓ No specific help for '{topic}'. Try:\n• help\n• help network\n• help dns\n• help dhcp")
    
    def process_natural_language(self, query: str) -> str:
        """Process natural language with enhanced understanding"""
        query_lower = query.lower()
        
        # Common patterns
        patterns = [
            # Networks
            (r'list all networks?\s*(?:with (?:their )?extended attributes?)?', self.handle_list_networks),
            (r'audit network ([\d\.\/]+)', lambda m: self.audit_network(m.group(1))),
            
            # DNS
            (r'list (?:all )?dns zones?', self.handle_list_dns_zones),
            (r'list (?:all )?([a-z]+) records?', lambda m: self.handle_list_records(m.group(1))),
            
            # DHCP
            (r'list (?:all )?dhcp ranges?', self.handle_list_dhcp_ranges),
            (r'list (?:all )?(?:dhcp )?leases?', self.handle_list_leases),
            
            # IP
            (r'search ip ([\d\.]+)', lambda m: self.search_ip(m.group(1))),
            (r'find available ips? in ([\d\.\/]+)', lambda m: self.find_available_ips(m.group(1)))
        ]
        
        for pattern, handler in patterns:
            match = re.search(pattern, query_lower)
            if match:
                if hasattr(handler, '__call__') and handler.__code__.co_argcount > 1:
                    return handler(match)
                else:
                    return handler()
        
        # If no pattern matches, provide suggestions
        return f"""🤔 I didn't understand: **{query}**

**Did you mean one of these?**
• list all networks
• list dns zones
• audit network 10.0.0.0/24
• find available ips in 10.0.0.0/24

**💡 Tips:**
• Type `?` for help
• Type `list` to see list options
• Type `help <topic>` for specific help"""
    
    def handle_list_networks(self) -> str:
        """Handle network listing with proper extended attributes support"""
        # Check if extended attributes requested
        with_extattrs = "extended" in self.path
        
        params = {
            "network_view": self.network_view,
            "max_results": 100
        }
        
        if with_extattrs:
            params["return_fields"] = "network,comment,extattrs,members"
        else:
            params["return_fields"] = "network,comment,network_view"
        
        result = self.execute_tool("list_network", params)
        
        if result.get("success"):
            networks = result["data"]
            
            if with_extattrs:
                response = "📊 **Networks with Extended Attributes:**\n\n"
                for i, net in enumerate(networks[:20], 1):
                    response += f"{i}. **{net.get('network', 'Unknown')}**\n"
                    if net.get('comment'):
                        response += f"   Comment: {net['comment']}\n"
                    
                    extattrs = net.get('extattrs', {})
                    if extattrs:
                        response += "   Extended Attributes:\n"
                        for key, value in extattrs.items():
                            response += f"   • {key}: {value.get('value', 'N/A')}\n"
                    else:
                        response += "   Extended Attributes: None\n"
                    response += "\n"
            else:
                response = f"📊 **Networks in InfoBlox** (View: {self.network_view}):\n\n"
                for i, net in enumerate(networks[:20], 1):
                    response += f"{i}. **{net.get('network', 'Unknown')}**\n"
                    if net.get('comment'):
                        response += f"   • {net['comment']}\n"
                    response += "\n"
            
            if len(networks) > 20:
                response += f"... showing 20 of {len(networks)} networks"
            
            return response
        else:
            return f"❌ Error: {result.get('error')}"
    
    def handle_list_dns_zones(self) -> str:
        """List DNS zones"""
        result = self.execute_tool("list_zone_auth", {
            "return_fields": "fqdn,comment,view",
            "max_results": 50
        })
        
        if result.get("success"):
            zones = result["data"]
            response = "🌐 **DNS Zones in InfoBlox**\n\n"
            
            fwd_zones = [z for z in zones if not z.get('fqdn', '').endswith('.in-addr.arpa')]
            rev_zones = [z for z in zones if z.get('fqdn', '').endswith('.in-addr.arpa')]
            
            if fwd_zones:
                response += "**Forward Zones:**\n"
                for zone in fwd_zones[:10]:
                    response += f"• {zone.get('fqdn', 'Unknown')}"
                    if zone.get('comment'):
                        response += f" - {zone['comment']}"
                    response += "\n"
                if len(fwd_zones) > 10:
                    response += f"... and {len(fwd_zones) - 10} more\n"
            
            if rev_zones:
                response += "\n**Reverse Zones:**\n"
                for zone in rev_zones[:5]:
                    response += f"• {zone.get('fqdn', 'Unknown')}\n"
                if len(rev_zones) > 5:
                    response += f"... and {len(rev_zones) - 5} more\n"
            
            return response
        else:
            return f"❌ Error: {result.get('error')}"
    
    def handle_list_records(self, record_type: str) -> str:
        """List DNS records of specific type"""
        # Map common names to actual record types
        record_map = {
            "a": "record:a",
            "host": "record:host",
            "cname": "record:cname",
            "ptr": "record:ptr",
            "mx": "record:mx",
            "txt": "record:txt"
        }
        
        actual_type = record_map.get(record_type, f"record:{record_type}")
        tool_name = f"list_{actual_type.replace(':', '_')}"
        
        result = self.execute_tool(tool_name, {
            "max_results": 50
        })
        
        if result.get("success"):
            records = result["data"]
            response = f"📝 **{record_type.upper()} Records:**\n\n"
            
            for i, rec in enumerate(records[:20], 1):
                name = rec.get('name') or rec.get('fqdn') or 'Unknown'
                response += f"{i}. {name}"
                
                if 'ipv4addr' in rec:
                    response += f" → {rec['ipv4addr']}"
                elif 'ipv4addrs' in rec:
                    ips = [addr.get('ipv4addr', '') for addr in rec.get('ipv4addrs', [])]
                    response += f" → {', '.join(ips)}"
                elif 'canonical' in rec:
                    response += f" → {rec['canonical']}"
                
                response += "\n"
            
            if len(records) > 20:
                response += f"\n... showing 20 of {len(records)} records"
            
            return response
        else:
            return f"❌ Could not list {record_type} records"
    
    def handle_list_dhcp_ranges(self) -> str:
        """List DHCP ranges"""
        result = self.execute_tool("list_range", {
            "return_fields": "start_addr,end_addr,network,comment",
            "max_results": 50
        })
        
        if result.get("success"):
            ranges = result["data"]
            response = "🔄 **DHCP Ranges:**\n\n"
            
            for i, r in enumerate(ranges[:20], 1):
                response += f"{i}. **{r.get('start_addr', '?')} - {r.get('end_addr', '?')}**\n"
                response += f"   • Network: {r.get('network', 'Unknown')}\n"
                if r.get('comment'):
                    response += f"   • {r['comment']}\n"
                response += "\n"
            
            if len(ranges) > 20:
                response += f"... showing 20 of {len(ranges)} ranges"
            
            return response
        else:
            return f"❌ Error: {result.get('error')}"
    
    def handle_list_leases(self) -> str:
        """List DHCP leases"""
        result = self.execute_tool("list_lease", {
            "return_fields": "address,binding_state,client_hostname,hardware",
            "max_results": 50
        })
        
        if result.get("success"):
            leases = result["data"]
            response = "📋 **Active DHCP Leases:**\n\n"
            
            active_leases = [l for l in leases if l.get('binding_state') == 'ACTIVE']
            
            for i, lease in enumerate(active_leases[:20], 1):
                response += f"{i}. **{lease.get('address', 'Unknown')}**\n"
                if lease.get('client_hostname'):
                    response += f"   • Hostname: {lease['client_hostname']}\n"
                if lease.get('hardware'):
                    response += f"   • MAC: {lease['hardware']}\n"
                response += "\n"
            
            if len(active_leases) > 20:
                response += f"... showing 20 of {len(active_leases)} active leases"
            
            return response
        else:
            return f"❌ Error: {result.get('error')}"
    
    def audit_network(self, network: str) -> str:
        """Audit a network - implementation would go here"""
        return f"🔍 **Network Audit for {network}**\n\nThis would show IP usage, DNS records, DHCP info, etc."
    
    def search_ip(self, ip: str) -> str:
        """Search for IP information"""
        return f"🔎 **IP Information for {ip}**\n\nThis would show IP details, DNS names, MAC address, etc."
    
    def find_available_ips(self, network: str) -> str:
        """Find available IPs in network"""
        return f"✅ **Available IPs in {network}**\n\nThis would show next available IPs"
    
    def execute_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute tool against InfoBlox"""
        # Map tool names to object types
        tool_map = {
            "list_network": "network",
            "list_zone_auth": "zone_auth",
            "list_record_a": "record:a",
            "list_record_host": "record:host",
            "list_record_cname": "record:cname",
            "list_record_ptr": "record:ptr",
            "list_record_mx": "record:mx",
            "list_record_txt": "record:txt",
            "list_range": "range",
            "list_lease": "lease",
            "list_ipv4address": "ipv4address"
        }
        
        obj_type = tool_map.get(tool_name)
        if not obj_type:
            return {"success": False, "error": f"Unknown tool: {tool_name}"}
        
        try:
            # Build API parameters
            api_params = {
                "_max_results": params.get("max_results", 100)
            }
            
            # Add return fields if specified
            if params.get("return_fields"):
                api_params["_return_fields"] = params["return_fields"]
            
            # Add other parameters
            for key, value in params.items():
                if key not in ["max_results", "return_fields"]:
                    api_params[key] = value
            
            # Make request
            response = self.session.get(
                f"{self.grid_master}/{obj_type}",
                params=api_params
            )
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "data": response.json()
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def send_json(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
    
    def log_message(self, format, *args):
        """Log messages"""
        print(f"{datetime.now().strftime('%H:%M:%S')} - {format % args}")

if __name__ == "__main__":
    print("🚀 Starting Smart Help InfoBlox MCP Server...")
    print(f"Grid Master: {os.getenv('GRID_MASTER_URL', 'Not set')}")
    print(f"Network View: {os.getenv('NETWORK_VIEW', 'default')}")
    print("\n✅ Server ready with instant help and suggestions!")
    print("\nTry typing:")
    print("• ? - Quick help")
    print("• list - See list options")
    print("• help <topic> - Topic help")
    
    server = HTTPServer(('0.0.0.0', 8001), SmartHelpInfoBloxMCP)
    server.serve_forever()
