# JSON Formatter Update for InfoBlox MCP Server

def list_all_networks_json(self):
    """List all networks from InfoBlox with pretty JSON output"""
    try:
        print(f"Fetching all networks from {self.grid_master}")
        
        # Get networks with extended attributes
        params = {
            "_max_results": 100,
            "_return_fields": "network,comment,network_view,usage,members,extattrs",
            "network_view": self.network_view
        }
        
        response = self.session.get(f"{self.grid_master}/network", params=params)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            networks = response.json()
            
            if not networks:
                return json.dumps({
                    "status": "error",
                    "message": f"No networks found in view '{self.network_view}'",
                    "network_view": self.network_view
                }, indent=2)
            
            # Format each network properly
            formatted_networks = []
            for i, net in enumerate(networks, 1):
                network_obj = {
                    "number": i,
                    "network": net.get('network', 'Unknown'),
                    "comment": net.get('comment', 'No description'),
                    "network_view": net.get('network_view', self.network_view)
                }
                
                # Add usage information if available
                usage_list = net.get('usage', [])
                if usage_list:
                    usage = usage_list[0]
                    total = usage.get('total', 0)
                    used = usage.get('used', 0)
                    percentage = (used / total * 100) if total > 0 else 0
                    network_obj['usage'] = {
                        'total': total,
                        'used': used,
                        'available': total - used,
                        'percentage_used': round(percentage, 2),
                        'percentage_available': round(100 - percentage, 2)
                    }
                
                # Parse extended attributes
                extattrs = net.get('extattrs', {})
                if extattrs:
                    parsed_extattrs = {}
                    
                    # Parse AWS VPC info from comment or extattrs
                    if 'comment' in net:
                        import re
                        vpc_match = re.search(r'vpc-([a-f0-9]+)\s*\(([^)]+)\)\s*on\s*([\d-]+)', net['comment'])
                        if vpc_match:
                            parsed_extattrs['vpc_id'] = f"vpc-{vpc_match.group(1)}"
                            parsed_extattrs['vpc_name'] = vpc_match.group(2)
                            parsed_extattrs['import_date'] = vpc_match.group(3)
                            
                            # Extract region from VPC name
                            region_match = re.search(r'(us-east-1|us-west-2|eu-west-1|ap-southeast-1)', vpc_match.group(2))
                            if region_match:
                                parsed_extattrs['region'] = region_match.group(1)
                    
                    # Add any direct extended attributes
                    for key, value in extattrs.items():
                        if isinstance(value, dict) and 'value' in value:
                            parsed_extattrs[key.lower()] = value['value']
                        else:
                            parsed_extattrs[key.lower()] = value
                    
                    if parsed_extattrs:
                        network_obj['extended_attributes'] = parsed_extattrs
                
                formatted_networks.append(network_obj)
            
            # Create the final response
            result = {
                "status": "success",
                "network_view": self.network_view,
                "total_networks": len(formatted_networks),
                "networks": formatted_networks
            }
            
            # Pretty print JSON
            return json.dumps(result, indent=2, sort_keys=False)
            
        else:
            return json.dumps({
                "status": "error",
                "code": response.status_code,
                "message": response.text[:200]
            }, indent=2)
            
    except Exception as e:
        return json.dumps({
            "status": "error",
            "message": f"Error connecting to InfoBlox: {str(e)}"
        }, indent=2)


def process_real_infoblox_query_json(self, query):
    """Process queries against real InfoBlox with JSON output"""
    
    # Add JSON format flag parsing
    output_json = "json" in query.lower() or "format=json" in query.lower()
    
    # Clean query for processing
    clean_query = query.lower().replace("json", "").replace("format=", "").strip()
    
    # Test connection
    if any(word in clean_query for word in ["test", "connection", "verify", "check connection"]):
        if output_json:
            return self.test_connection_json()
        return self.test_connection()
    
    # LIST ALL NETWORKS
    elif any(phrase in clean_query for phrase in [
        "list all network", "list network", "show all network", 
        "all network", "list my network", "show network",
        "get all network", "what network", "which network",
        "display network", "view network"
    ]):
        if output_json:
            return self.list_all_networks_json()
        return self.list_all_networks()
    
    # Continue with other operations...
    
def test_connection_json(self):
    """Test InfoBlox connection with JSON output"""
    try:
        response = self.session.get(
            f"{self.grid_master}/grid",
            params={"_return_fields": "name,ntp_setting,dns_resolver_setting"}
        )
        
        if response.status_code == 200:
            grid_info = response.json()
            if isinstance(grid_info, list) and grid_info:
                grid_data = grid_info[0]
                return json.dumps({
                    "status": "success",
                    "connection": "established",
                    "grid_info": {
                        "name": grid_data.get('name', 'Unknown'),
                        "master": self.grid_master,
                        "api_version": self.grid_master.split('/')[-1],
                        "network_view": self.network_view
                    }
                }, indent=2)
        else:
            return json.dumps({
                "status": "error",
                "connection": "failed",
                "code": response.status_code,
                "message": response.text[:200]
            }, indent=2)
            
    except Exception as e:
        return json.dumps({
            "status": "error",
            "connection": "failed",
            "message": str(e)
        }, indent=2)


# Example of how to add JSON format to other methods
def get_network_usage_json(self, network_cidr):
    """Get network usage with JSON output"""
    try:
        params = {
            "network": network_cidr,
            "network_view": self.network_view,
            "_return_fields": "network,comment,usage,members,options,extattrs"
        }
        
        response = self.session.get(f"{self.grid_master}/network", params=params)
        
        if response.status_code == 200:
            networks = response.json()
            if networks:
                net = networks[0]
                
                result = {
                    "status": "success",
                    "network": net.get('network', 'Unknown'),
                    "comment": net.get('comment', 'No description'),
                    "network_view": self.network_view
                }
                
                usage_list = net.get('usage', [])
                if usage_list:
                    usage = usage_list[0]
                    total = usage.get('total', 0)
                    used = usage.get('used', 0)
                    free = total - used
                    percentage = (used / total * 100) if total > 0 else 0
                    
                    result['usage'] = {
                        'total_ips': total,
                        'used': used,
                        'available': free,
                        'percentage_used': round(percentage, 2),
                        'percentage_available': round(100 - percentage, 2)
                    }
                
                # Add extended attributes if present
                if 'extattrs' in net:
                    result['extended_attributes'] = net['extattrs']
                
                return json.dumps(result, indent=2)
            else:
                return json.dumps({
                    "status": "error",
                    "message": f"Network {network_cidr} not found in view '{self.network_view}'"
                }, indent=2)
        else:
            return json.dumps({
                "status": "error",
                "code": response.status_code,
                "message": f"Error fetching network: {response.text[:200]}"
            }, indent=2)
            
    except Exception as e:
        return json.dumps({
            "status": "error",
            "message": str(e)
        }, indent=2)
