#!/bin/bash
# Rebuild InfoBlox MCP with Enhanced Intelligence

echo "🔧 Rebuilding InfoBlox MCP Server with Enhanced Intelligence..."

# Stop and remove existing container
docker stop infoblox-mcp 2>/dev/null
docker rm infoblox-mcp 2>/dev/null

# Go to directory
cd /Users/<USER>/infoblox-real-mcp

# Copy the enhanced server with all suggestion methods
cat enhanced_server.py > real_mcp_server.py

# Add the suggestion methods to the end of the class before log_message
cat >> real_mcp_server.py << 'EOF'

    def get_contextual_help(self):
        """Return contextual help based on user needs"""
        return """🎯 **InfoBlox Assistant - How Can I Help?**

**🔍 Finding Tools:**
• `browse tools` - See all 1,345 tools by category
• `search tools: <term>` - Find specific tools
  - Try: `search tools: network`
  - Try: `search tools: dns record`
  - Try: `search tools: create`

**🚀 Quick Actions:**
• `list all networks` - View your networks
• `test connection` - Check InfoBlox status

**📚 Common Tasks:**

**Networks:**
- Create: `search tools: create network`
- List: `search tools: list network`
- Delete: `search tools: delete network`

**DNS:**
- Zones: `search tools: zone`
- A Records: `search tools: a record`
- CNAME: `search tools: cname`

**DHCP:**
- Ranges: `search tools: dhcp range`
- Leases: `search tools: lease`
- Fixed IPs: `search tools: fixed address`

**💡 Pro Tips:**
- Add `json` for JSON output
- Use partial words in searches
- Copy examples and modify

What would you like to do?"""
    
    def suggest_network_operations(self):
        """Suggest network-related operations"""
        return """🌐 **Network Operations Available**

I see you're interested in network management. Here are your options:

**View Networks:**
• `list all networks` - See all your networks
• `search tools: list network` - Find network listing tools

**Create Networks:**
• `search tools: create network` - Find network creation tool
• Example: `execute tool: {"tool_id": "create_network", "parameters": {"network": "*********/24", "comment": "New network"}}`

**Search Networks:**
• `search tools: find network` - Find network search tools
• `search tools: network by comment` - Search by description

**Network Info:**
• `search tools: network usage` - Check IP usage
• `search tools: network container` - Container tools

**Quick Start:**
Type `search tools: network` to see all 60+ network tools

Which operation do you need?"""
    
    def suggest_dns_operations(self):
        """Suggest DNS-related operations"""
        return """🌍 **DNS Operations Available**

Here are your DNS management options:

**DNS Zones:**
• `search tools: list zone` - List DNS zones
• `search tools: create zone` - Create new zone
• `search tools: zone auth` - Authoritative zones

**DNS Records:**
• `search tools: a record` - A record tools
• `search tools: cname` - CNAME tools
• `search tools: ptr` - PTR (reverse DNS)
• `search tools: mx record` - Mail records
• `search tools: txt record` - TXT records

**Common Tasks:**
1. List all A records: `search tools: list record a`
2. Create A record: `search tools: create record a`
3. Find specific record: `search tools: get record`

**Quick Start:**
Type `search tools: dns` to see all 250+ DNS tools

What DNS operation do you need?"""
    
    def suggest_dhcp_operations(self):
        """Suggest DHCP-related operations"""
        return """📡 **DHCP Operations Available**

Here are your DHCP management options:

**DHCP Ranges:**
• `search tools: dhcp range` - Manage DHCP ranges
• `search tools: create range` - Create new range

**DHCP Leases:**
• `search tools: lease` - View active leases
• `search tools: lease by mac` - Find by MAC address

**Fixed Addresses (Reservations):**
• `search tools: fixed address` - Manage reservations
• `search tools: create fixed` - Create reservation

**DHCP Options:**
• `search tools: dhcp option` - DHCP options
• `search tools: option space` - Option spaces

**Quick Examples:**
1. List ranges: `search tools: list range`
2. View leases: `search tools: list lease`
3. Create reservation: `search tools: create fixedaddress`

Type `search tools: dhcp` to see all 100+ DHCP tools

What DHCP task do you need help with?"""
    
    def suggest_creation_tools(self, query):
        """Suggest creation tools based on context"""
        obj_type = ""
        if "network" in query:
            obj_type = "network"
        elif "dns" in query or "record" in query:
            obj_type = "record"
        elif "zone" in query:
            obj_type = "zone"
        elif "dhcp" in query or "range" in query:
            obj_type = "range"
        elif "lease" in query:
            obj_type = "lease"
        
        response = f"""✨ **Create Operations**

I see you want to create something. """
        
        if obj_type:
            response += f"Let me find {obj_type} creation tools:\n\n"
            response += f"Searching for: `search tools: create {obj_type}`\n\n"
            # Actually perform the search
            return self.search_and_display_tools(f"create {obj_type}")
        else:
            response += """Here are common creation tools:

**Networks:**
• `search tools: create network`

**DNS Records:**
• `search tools: create record a` - A records
• `search tools: create record cname` - CNAME
• `search tools: create record ptr` - PTR

**DNS Zones:**
• `search tools: create zone`

**DHCP:**
• `search tools: create range` - DHCP range
• `search tools: create fixed` - Reservation

**IP Management:**
• `search tools: create host` - Host record

What would you like to create?"""
        
        return response
    
    def suggest_deletion_tools(self, query):
        """Suggest deletion tools based on context"""
        return """🗑️ **Delete Operations**

**⚠️ Important:** Deletion operations require the object reference (_ref) from a list operation.

**Common Deletion Tools:**

**Networks:**
• `search tools: delete network`
• First list networks to get _ref

**DNS Records:**
• `search tools: delete record` - Various record types
• `search tools: delete a record` - A records
• `search tools: delete cname` - CNAME records

**DHCP:**
• `search tools: delete range` - DHCP ranges
• `search tools: delete lease` - Active leases
• `search tools: delete fixed` - Reservations

**Process:**
1. List objects to get _ref: `search tools: list <object>`
2. Copy the _ref from results
3. Execute delete: `execute tool: {"tool_id": "delete_<object>", "parameters": {"_ref": "<copied_ref>"}}`

What do you need to delete?"""
    
    def suggest_listing_tools(self, query):
        """Suggest listing tools based on context"""
        # Extract potential object type from query
        suggestions = []
        
        if any(word in query for word in ["network", "subnet"]):
            suggestions.append(("list_network", "all networks"))
        if any(word in query for word in ["dns", "zone"]):
            suggestions.append(("list_zone_auth", "DNS zones"))
        if any(word in query for word in ["record", "dns"]):
            suggestions.append(("list_record_a", "A records"))
        if any(word in query for word in ["dhcp", "range"]):
            suggestions.append(("list_range", "DHCP ranges"))
        if any(word in query for word in ["lease"]):
            suggestions.append(("list_lease", "DHCP leases"))
        if any(word in query for word in ["ip", "address"]):
            suggestions.append(("list_ipv4address", "IP addresses"))
        
        if suggestions:
            response = "📋 **Here are relevant listing tools:**\n\n"
            for tool_id, desc in suggestions:
                response += f"• **{desc}**: `execute tool: {{\"tool_id\": \"{tool_id}\", \"parameters\": {{}}}}`\n"
            response += "\nOr search for more options: `search tools: list`"
        else:
            response = """📋 **List/View Operations**

**Quick Lists:**
• `list all networks` - View all networks
• `execute tool: {"tool_id": "list_zone_auth", "parameters": {}}` - List DNS zones
• `execute tool: {"tool_id": "list_record_a", "parameters": {}}` - List A records
• `execute tool: {"tool_id": "list_range", "parameters": {}}` - List DHCP ranges

**Find More:**
• `search tools: list network` - Network lists
• `search tools: list record` - DNS record lists
• `search tools: list dhcp` - DHCP lists

What information do you need to see?"""
        
        return response
    
    def get_contextual_help_with_suggestions(self, query):
        """Provide help with suggestions based on the query"""
        response = f"""🤔 **I didn't understand: "{query}"**

**Did you mean one of these?**

🔍 **Search for tools:**
• `search tools: {query}` - Search for tools related to "{query}"
• `browse tools` - See all categories

📚 **Common Commands:**
• `list all networks` - Show networks
• `test connection` - Check status
• `help` - Show all commands

**💡 Suggestions based on your input:**
"""
        
        # Add specific suggestions based on keywords
        query_lower = query.lower()
        suggestions = []
        
        if any(word in query_lower for word in ["create", "add", "new"]):
            suggestions.append("• Try: `search tools: create` to find creation tools")
        if any(word in query_lower for word in ["delete", "remove"]):
            suggestions.append("• Try: `search tools: delete` to find deletion tools")
        if any(word in query_lower for word in ["list", "show", "get"]):
            suggestions.append("• Try: `search tools: list` to find listing tools")
        if any(word in query_lower for word in ["network", "subnet", "ip"]):
            suggestions.append("• Try: `search tools: network` for network tools")
        if any(word in query_lower for word in ["dns", "record", "zone"]):
            suggestions.append("• Try: `search tools: dns` for DNS tools")
        
        if suggestions:
            response += "\n".join(suggestions) + "\n\n**Need help?** Type `help` for a full guide."
        else:
            response += """
• `browse tools` - See all available tools
• `search tools: ` - Search for specific tools
• `help` - Show command guide

**Pro tip:** Try searching with partial words like "net" for network or "rec" for record."""
        
        return response

# Add this to the end of the class definition
EOF

# Build and run
echo "Building Docker image..."
docker build -t infoblox-mcp:intelligence .

echo "Starting enhanced server..."
docker run -d --name infoblox-mcp -p 8000:8000 \
  -e GRID_MASTER_URL="https://*************/wapi/v2.13.1" \
  -e INFOBLOX_USERNAME="admin" \
  -e INFOBLOX_PASSWORD="infoblox" \
  -e NETWORK_VIEW="default" \
  infoblox-mcp:intelligence

sleep 3

echo "✅ Enhanced InfoBlox MCP Server is running!"
echo ""
echo "Testing intelligent suggestions..."
curl -s -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "network"}]}' | \
  jq -r '.choices[0].message.content' | head -20
