# 🚀 Add InfoBlox Connection to Open WebUI

## ✅ Services Status
Both services have been restarted and are running:
- **Open WebUI**: ✅ Healthy at http://localhost:3000
- **InfoBlox MCP**: ✅ Running at http://localhost:8000

## 📋 Step-by-Step Connection Setup

### 1. Open Open WebUI
Navigate to: http://localhost:3000

### 2. Login to Open WebUI
Use your credentials to login

### 3. Access Settings
Click the **Settings** icon (⚙️) in the sidebar

### 4. Navigate to Connections
Look for **"Connections"** or **"External Connections"** in the settings menu

### 5. Add New Connection
Click the **"+ Add Connection"** button

### 6. Fill in Connection Details
Enter these values EXACTLY:

| Field | Value |
|-------|-------|
| **URL** | `http://host.docker.internal:8000/v1` |
| **openapi.json** | `http://host.docker.internal:8000/v1/openapi.json` |
| **Auth** | Keep as "Session" (or select "None" if available) |

### 7. Save Connection
Click **Save** or **Add** button

### 8. Refresh Browser
Press **Cmd+R** (Mac) or **F5** (Windows/Linux) to refresh

### 9. Verify Model
Check the model dropdown - you should see **"InfoBlox Assistant"**

## 🔍 Quick Verification Commands

Test the endpoints are working:
```bash
# Test OpenAPI endpoint
curl http://localhost:8000/v1/openapi.json

# Test models endpoint
curl http://localhost:8000/v1/models

# Test chat completion
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "infoblox-assistant",
    "messages": [{"role": "user", "content": "Hello"}]
  }'
```

## 🎯 What You Can Do

Once connected, you can use natural language in Open WebUI to:
- List all networks: "Show me all networks"
- Find IPs: "Find IP **********"
- Create networks: "Create network *********/24"
- Manage DNS: "List DNS zones"
- And 1,345+ more InfoBlox operations!

## ⚠️ Troubleshooting

If the model doesn't appear:
1. Ensure both containers are running: `docker ps`
2. Check InfoBlox logs: `docker logs infoblox-mcp`
3. Verify endpoints are accessible from Open WebUI container:
   ```bash
   docker exec open-webui curl http://host.docker.internal:8000/v1/models
   ```
4. Try using the IP address instead of host.docker.internal:
   - On Mac/Windows: Use `host.docker.internal`
   - On Linux: Use `**********` (Docker bridge IP)

## 📝 Alternative Connection URLs (if needed)

If `host.docker.internal` doesn't work, try:
- `http://**********:8000/v1` (Linux)
- `http://docker.for.mac.localhost:8000/v1` (older Docker for Mac)
- `http://infoblox-mcp:8000/v1` (if on same network)

Last updated: $(date)
