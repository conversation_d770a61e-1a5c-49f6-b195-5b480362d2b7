#!/bin/bash

# InfoBlox MCP + Open WebUI Integration Test Script
set -e

echo "🧪 InfoBlox MCP + Open WebUI Integration Tests"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Test function
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    log_info "Testing: $test_name"
    
    if output=$(eval "$test_command" 2>&1); then
        if [[ -z "$expected_pattern" ]] || echo "$output" | grep -q "$expected_pattern"; then
            log_success "$test_name"
            return 0
        else
            log_error "$test_name - Expected pattern not found: $expected_pattern"
            echo "Output: $output"
            return 1
        fi
    else
        log_error "$test_name - Command failed"
        echo "Error: $output"
        return 1
    fi
}

# Test Docker and Docker Compose
echo -e "\n${BLUE}🐳 Testing Docker Environment${NC}"
run_test "Docker installed" "docker --version" "Docker version"
run_test "Docker Compose installed" "docker-compose --version || docker compose version" "version"

# Test file structure
echo -e "\n${BLUE}📁 Testing File Structure${NC}"
run_test "Docker Compose file exists" "test -f docker-compose.yml"
run_test "InfoBlox Dockerfile exists" "test -f Dockerfile.infoblox"
run_test "Environment example exists" "test -f .env.example"
run_test "Setup script exists" "test -x setup.sh"
run_test "Static directory exists" "test -d static"
run_test "Open WebUI config exists" "test -d open-webui-config"

# Test environment configuration
echo -e "\n${BLUE}⚙️  Testing Environment Configuration${NC}"
if [ -f .env ]; then
    log_success "Environment file exists"
    
    # Check required variables
    if grep -q "GRID_MASTER_URL" .env; then
        log_success "GRID_MASTER_URL configured"
    else
        log_error "GRID_MASTER_URL not found in .env"
    fi
    
    if grep -q "INFOBLOX_USERNAME" .env; then
        log_success "INFOBLOX_USERNAME configured"
    else
        log_error "INFOBLOX_USERNAME not found in .env"
    fi
else
    log_warning "Environment file not found - run setup.sh first"
fi

# Test Docker Compose configuration
echo -e "\n${BLUE}🔧 Testing Docker Compose Configuration${NC}"
run_test "Docker Compose syntax valid" "docker-compose config" "services:"
run_test "InfoBlox MCP service defined" "docker-compose config | grep -A 5 'infoblox-mcp:'" "infoblox-mcp"
run_test "Open WebUI service defined" "docker-compose config | grep -A 5 'open-webui:'" "open-webui"
run_test "Network configuration valid" "docker-compose config | grep -A 3 'networks:'" "infoblox-network"

# Test if services are running
echo -e "\n${BLUE}🚀 Testing Running Services${NC}"
if docker-compose ps | grep -q "Up"; then
    log_info "Services are running, testing endpoints..."
    
    # Test InfoBlox MCP endpoints
    run_test "InfoBlox MCP health endpoint" "curl -s http://localhost:8000/health" "status"
    run_test "InfoBlox MCP models endpoint" "curl -s http://localhost:8000/v1/models" "infoblox-assistant"
    
    # Test Open WebUI
    run_test "Open WebUI accessible" "curl -s -o /dev/null -w '%{http_code}' http://localhost:3000" "200"
    
    # Test MCP chat endpoint
    log_info "Testing InfoBlox MCP chat functionality..."
    chat_response=$(curl -s -X POST http://localhost:8000/v1/chat/completions \
        -H "Content-Type: application/json" \
        -d '{"messages": [{"role": "user", "content": "test connection"}]}')
    
    if echo "$chat_response" | grep -q "choices"; then
        log_success "InfoBlox MCP chat endpoint working"
    else
        log_error "InfoBlox MCP chat endpoint failed"
        echo "Response: $chat_response"
    fi
    
else
    log_warning "Services not running - start with: docker-compose up -d"
fi

# Test static files
echo -e "\n${BLUE}🎨 Testing Static Assets${NC}"
run_test "Custom CSS exists" "test -f static/infoblox-theme.css"
run_test "Custom JavaScript exists" "test -f static/custom-branding.js"
run_test "CSS contains InfoBlox branding" "grep -q 'infoblox-primary' static/infoblox-theme.css"

# Test Open WebUI configuration
echo -e "\n${BLUE}📋 Testing Open WebUI Configuration${NC}"
run_test "Models configuration exists" "test -f open-webui-config/models.json"
run_test "Functions configuration exists" "test -f open-webui-config/functions.py"
run_test "Tools configuration exists" "test -f open-webui-config/infoblox_tools.py"

if [ -f open-webui-config/models.json ]; then
    run_test "Models config is valid JSON" "python3 -m json.tool open-webui-config/models.json > /dev/null"
    run_test "InfoBlox model defined" "grep -q 'infoblox-assistant' open-webui-config/models.json"
fi

# Test InfoBlox MCP tools
echo -e "\n${BLUE}🛠️  Testing InfoBlox MCP Tools${NC}"
if [ -f infoblox_mcp_tools.json ]; then
    run_test "InfoBlox tools file exists" "test -f infoblox_mcp_tools.json"
    run_test "Tools file is valid JSON" "python3 -m json.tool infoblox_mcp_tools.json > /dev/null"
else
    log_warning "InfoBlox tools file not found - may be generated at runtime"
fi

if [ -f wapi_schema.json ]; then
    run_test "WAPI schema exists" "test -f wapi_schema.json"
    run_test "Schema is valid JSON" "python3 -m json.tool wapi_schema.json > /dev/null"
else
    log_warning "WAPI schema file not found - may be generated at runtime"
fi

# Test documentation
echo -e "\n${BLUE}📚 Testing Documentation${NC}"
run_test "README exists" "test -f README.md"
run_test "Deployment guide exists" "test -f DEPLOYMENT_GUIDE.md"
run_test "README contains setup instructions" "grep -q 'Quick Start' README.md"

# Network connectivity test (if services are running)
if docker-compose ps | grep -q "Up"; then
    echo -e "\n${BLUE}🌐 Testing Network Connectivity${NC}"
    
    # Test internal network connectivity
    if docker-compose exec -T infoblox-mcp curl -s http://localhost:8000/health > /dev/null; then
        log_success "InfoBlox MCP internal connectivity"
    else
        log_error "InfoBlox MCP internal connectivity failed"
    fi
    
    # Test container-to-container communication
    if docker-compose exec -T open-webui curl -s http://infoblox-mcp:8000/health > /dev/null 2>&1; then
        log_success "Container-to-container communication"
    else
        log_error "Container-to-container communication failed"
    fi
fi

# Security tests
echo -e "\n${BLUE}🔒 Testing Security Configuration${NC}"
run_test "Environment example doesn't contain real passwords" "! grep -q 'real-password' .env.example"
run_test "Docker Compose uses environment variables" "grep -q '\${' docker-compose.yml"

if [ -f .env ]; then
    run_test "Environment file has restricted permissions" "test $(stat -c '%a' .env 2>/dev/null || stat -f '%A' .env 2>/dev/null | cut -c 8-10) = '600' -o $(stat -c '%a' .env 2>/dev/null || stat -f '%A' .env 2>/dev/null | cut -c 8-10) = '644'"
fi

# Performance tests
echo -e "\n${BLUE}⚡ Testing Performance Configuration${NC}"
run_test "Docker Compose has resource limits" "grep -q 'resources:' docker-compose.yml || echo 'No resource limits defined (optional)'"
run_test "Health checks configured" "grep -q 'healthcheck:' docker-compose.yml"

# Final summary
echo -e "\n${BLUE}📊 Test Summary${NC}"
echo "================================"
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo -e "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! Your InfoBlox MCP + Open WebUI integration is ready.${NC}"
    echo -e "\n${BLUE}Next steps:${NC}"
    echo "1. Configure your InfoBlox credentials in .env"
    echo "2. Start services: docker-compose up -d"
    echo "3. Access Open WebUI at: http://localhost:3000"
    echo "4. Configure InfoBlox model in Open WebUI settings"
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Please review the errors above.${NC}"
    echo -e "\n${BLUE}Common fixes:${NC}"
    echo "1. Run setup.sh to create missing files"
    echo "2. Check Docker and Docker Compose installation"
    echo "3. Verify file permissions and structure"
    echo "4. Review configuration files for syntax errors"
    exit 1
fi
