# InfoBlox Model Not Showing in Open WebUI - Fix Guide

## Quick Fix Steps:

### 1. Verify InfoBlox MCP is Running Correctly
```bash
curl http://localhost:8000/v1/models
```
✅ Should return: `{"data": [{"id": "infoblox-assistant", ...}]}`

### 2. Configure Open WebUI Connection

**IMPORTANT**: You must add the InfoBlox server as a connection in Open WebUI.

1. Open http://localhost:3000 in your browser
2. Login or create an account
3. Go to **Settings** (gear icon)
4. Click **Connections** in the left menu
5. Click **"+ Add Connection"** button
6. Fill in:
   - **API**: Select "OpenAI"
   - **Name**: InfoBlox Assistant
   - **API Base URL**: `http://host.docker.internal:8000/v1`
   - **API Key**: `dummy-key-12345` (any value works)
7. Click **Save**
8. **Refresh the browser page** (important!)
9. Click the model dropdown - you should now see "infoblox-assistant"

### 3. If Model Still Doesn't Appear:

#### Option A: Try with IP address
Instead of `host.docker.internal`, use your actual IP:
```bash
# Find your IP
ifconfig | grep "inet " | grep -v 127.0.0.1

# Use it in API Base URL, e.g.:
# http://*************:8000/v1
```

#### Option B: Check Open WebUI logs
```bash
docker logs open-webui --tail 50 | grep -i "model\|error\|connection"
```

#### Option C: Test from within Open WebUI container
```bash
docker exec open-webui curl http://host.docker.internal:8000/v1/models
```

### 4. Alternative Docker Run Command
If host.docker.internal doesn't work, restart Open WebUI with explicit host:
```bash
docker stop open-webui && docker rm open-webui

docker run -d \
  --name open-webui \
  -p 3000:8080 \
  -v open-webui:/app/backend/data \
  --add-host=host.docker.internal:host-gateway \
  -e OPENAI_API_BASE_URLS="http://host.docker.internal:8000/v1" \
  --restart always \
  ghcr.io/open-webui/open-webui:main
```

### 5. Verify Connection from Open WebUI
After adding the connection:
1. Go to the chat interface
2. Click the model dropdown
3. You should see "infoblox-assistant"
4. Select it and type "hello"
5. You should get the InfoBlox greeting

## Common Issues:

### "Model not found"
- Make sure you added the connection in Settings → Connections
- Refresh the browser after adding connection
- Check the API Base URL ends with `/v1`

### "Connection refused"
- Use `host.docker.internal` not `localhost`
- Or use your machine's actual IP address
- Make sure InfoBlox MCP is running: `docker ps | grep infoblox`

### "No models available"
- The connection wasn't saved properly
- Delete and re-add the connection
- Make sure to click Save and refresh

## Testing the Connection:
```bash
# From your machine
curl http://localhost:8000/v1/models

# From Open WebUI container
docker exec open-webui curl http://host.docker.internal:8000/v1/models
```

Both should return the InfoBlox model data.