import requests
import json
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib3
import os
import re
from typing import Dict, List, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class EnhancedInfoBloxMCP(BaseHTTPRequestHandler):
    """Enhanced InfoBlox MCP with better context understanding"""
    
    # Class variables
    wapi_tools = {}
    wapi_objects = {}
    tool_index = {}
    working_tools = set()  # Track which tools actually work
    
    # Common object mappings - what users say vs actual object names
    object_aliases = {
        "networks": "network",
        "ips": "ipv4address",
        "ip addresses": "ipv4address",
        "dns zones": "zone_auth",
        "zones": "zone_auth",
        "a records": "record:a",
        "host records": "record:host",
        "dhcp ranges": "range",
        "leases": "lease",
        "fixed addresses": "fixedaddress",
        "dns records": "record:",  # Prefix match
        "hosts": "record:host"
    }
    
    # Discovery-related objects to avoid
    discovery_objects = [
        "discovery:device",
        "discovery:devicecomponent",
        "discovery:deviceinterface",
        "discovery:deviceneighbor",
        "discovery:devicesupportbundle",
        "discovery:diagnostictask",
        "discovery:gridproperties",
        "discovery:memberproperties",
        "discovery:sdnnetwork",
        "discovery:status",
        "discovery:vrf",
        "discovery:credentialgroup"
    ]
    
    def __init__(self, *args, **kwargs):
        self.grid_master = os.getenv("GRID_MASTER_URL", "https://***********/wapi/v2.13.1")
        self.username = os.getenv("INFOBLOX_USERNAME", "admin")
        self.password = os.getenv("INFOBLOX_PASSWORD", "infoblox")
        self.network_view = os.getenv("NETWORK_VIEW", "default")
        
        # Create session
        self.session = requests.Session()
        self.session.auth = (self.username, self.password)
        self.session.verify = False
        self.session.headers.update({'Content-Type': 'application/json'})
        
        # Load tools and working status
        if not self.__class__.wapi_tools:
            self.load_discovered_tools()
            self.load_working_tools()
            self.build_search_index()
        
        super().__init__(*args, **kwargs)
    
    def load_discovered_tools(self):
        """Load tools from discovery"""
        try:
            if os.path.exists('infoblox_mcp_tools.json'):
                with open('infoblox_mcp_tools.json', 'r') as f:
                    tools = json.load(f)
                    for tool in tools:
                        self.__class__.wapi_tools[tool['id']] = tool
                logger.info(f"Loaded {len(self.__class__.wapi_tools)} MCP tools")
        except Exception as e:
            logger.error(f"Error loading tools: {e}")
    
    def load_working_tools(self):
        """Load list of working tools"""
        try:
            # If we have a working tools file, use it
            if os.path.exists('working_infoblox_tools.json'):
                with open('working_infoblox_tools.json', 'r') as f:
                    working = json.load(f)
                    self.__class__.working_tools = set(t['id'] for t in working)
                logger.info(f"Loaded {len(self.__class__.working_tools)} working tools")
            else:
                # Otherwise, exclude known problematic tools
                for tool_id, tool_info in self.__class__.wapi_tools.items():
                    obj_type = tool_info.get('object', '')
                    # Exclude discovery tools
                    if not any(disc in obj_type for disc in self.discovery_objects):
                        self.__class__.working_tools.add(tool_id)
        except Exception as e:
            logger.error(f"Error loading working tools: {e}")
    
    def build_search_index(self):
        """Build search index for fast tool discovery"""
        for tool_id, tool_info in self.__class__.wapi_tools.items():
            # Only index working tools
            if tool_id not in self.__class__.working_tools:
                continue
                
            # Index by operation
            operation = tool_info.get('operation', '')
            if operation not in self.__class__.tool_index:
                self.__class__.tool_index[operation] = []
            self.__class__.tool_index[operation].append(tool_id)
            
            # Index by object type
            obj_type = tool_info.get('object', '')
            if obj_type not in self.__class__.tool_index:
                self.__class__.tool_index[obj_type] = []
            self.__class__.tool_index[obj_type].append(tool_id)
            
            # Index by keywords
            keywords = self.extract_keywords(tool_info)
            for keyword in keywords:
                if keyword not in self.__class__.tool_index:
                    self.__class__.tool_index[keyword] = []
                self.__class__.tool_index[keyword].append(tool_id)
    
    def extract_keywords(self, tool_info: Dict[str, Any]) -> List[str]:
        """Extract searchable keywords from tool info"""
        keywords = []
        
        obj_type = tool_info.get('object', '')
        
        # Skip discovery objects
        if any(disc in obj_type for disc in self.discovery_objects):
            return []
        
        # Extract from object name
        parts = re.split(r'[:_\.]', obj_type)
        keywords.extend([p.lower() for p in parts if p])
        
        # Add category keywords
        if 'network' in obj_type and 'discovery' not in obj_type:
            keywords.extend(['network', 'networks', 'subnet', 'subnets'])
        elif 'record:' in obj_type:
            keywords.extend(['dns', 'record', 'records'])
        elif 'zone' in obj_type:
            keywords.extend(['dns', 'zone', 'zones', 'domain'])
        elif any(x in obj_type for x in ['dhcp', 'range', 'lease']):
            keywords.extend(['dhcp', 'dynamic'])
        elif 'ipv4' in obj_type:
            keywords.extend(['ip', 'ipv4', 'address'])
        elif 'ipv6' in obj_type:
            keywords.extend(['ipv6', 'address'])
        
        return list(set(keywords))
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == "/health":
            self.send_json({
                "status": "healthy",
                "grid_master": self.grid_master,
                "total_tools": len(self.__class__.wapi_tools),
                "working_tools": len(self.__class__.working_tools)
            })
        elif self.path == "/tools":
            # Return only working tools
            working_tools = [
                tool for tool_id, tool in self.__class__.wapi_tools.items()
                if tool_id in self.__class__.working_tools
            ]
            self.send_json({
                "tools": working_tools,
                "total": len(working_tools)
            })
        else:
            self.send_json({"status": "ok"})
    
    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        body = self.rfile.read(content_length).decode('utf-8')
        
        if self.path == "/v1/chat/completions":
            try:
                data = json.loads(body)
                messages = data.get("messages", [])
                
                if messages:
                    user_message = messages[-1].get("content", "")
                    
                    # Check for direct tool invocation
                    if user_message.startswith("TOOL:"):
                        tool_data = json.loads(user_message[5:])
                        result = self.execute_tool(
                            tool_data.get("tool"),
                            tool_data.get("params", {})
                        )
                        response_content = json.dumps(result, indent=2)
                    else:
                        # Enhanced natural language processing
                        response_content = self.process_enhanced_query(user_message)
                else:
                    response_content = self.get_help_message()
                
                response = {
                    "id": "chatcmpl-" + str(int(datetime.now().timestamp())),
                    "object": "chat.completion",
                    "created": int(datetime.now().timestamp()),
                    "model": "infoblox-enhanced",
                    "choices": [{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_content
                        },
                        "finish_reason": "stop"
                    }]
                }
                self.send_json(response)
                
            except Exception as e:
                logger.error(f"Error processing request: {e}")
                self.send_json({"error": str(e)})
    
    def process_enhanced_query(self, query: str) -> str:
        """Process query with enhanced understanding"""
        query_lower = query.lower()
        
        # Analyze query intent
        intent = self.analyze_enhanced_intent(query_lower)
        
        # Handle extended attributes specifically
        if "extended attribute" in query_lower or "extattr" in query_lower:
            return self.handle_extended_attributes_query(query_lower, intent)
        
        # Common operations with better mapping
        if intent['operation'] == 'list' and intent['object_type']:
            # Use the correct tool for the object type
            tool_id = f"list_{intent['object_type'].replace(':', '_').replace('.', '_')}"
            
            # Check if it's a working tool
            if tool_id in self.__class__.working_tools:
                # Prepare parameters
                params = {"max_results": 100}
                
                # Add return fields if mentioned
                if any(word in query_lower for word in ['detail', 'all', 'extended', 'full']):
                    params["return_fields"] = self.get_detailed_fields(intent['object_type'])
                
                # Execute
                result = self.execute_tool(tool_id, params)
                return self.format_tool_result(result, tool_id, query)
            else:
                return f"❌ The tool '{tool_id}' is not available. Try:\n" + self.suggest_alternatives(intent['object_type'])
        
        # Natural language patterns
        patterns = [
            (r'list all networks?', self.list_networks),
            (r'list all dns zones?', self.list_dns_zones),
            (r'list all dhcp ranges?', self.list_dhcp_ranges),
            (r'audit network ([\d\.\/]+)', self.audit_network),
            (r'search ip ([\d\.]+)', self.search_ip)
        ]
        
        for pattern, handler in patterns:
            match = re.search(pattern, query_lower)
            if match:
                if handler.__code__.co_argcount > 1:  # Has parameters
                    return handler(match.group(1))
                else:
                    return handler()
        
        # If no match, provide intelligent suggestions
        return self.provide_intelligent_suggestions(query, intent)
    
    def analyze_enhanced_intent(self, query: str) -> Dict[str, Any]:
        """Enhanced intent analysis"""
        intent = {
            "operation": None,
            "object_type": None,
            "attributes": [],
            "filters": {}
        }
        
        # Detect operation
        if any(word in query for word in ['list', 'show', 'display', 'get all', 'find all']):
            intent['operation'] = 'list'
        elif any(word in query for word in ['create', 'add', 'new']):
            intent['operation'] = 'create'
        elif any(word in query for word in ['update', 'modify', 'change']):
            intent['operation'] = 'update'
        elif any(word in query for word in ['delete', 'remove']):
            intent['operation'] = 'delete'
        elif any(word in query for word in ['audit', 'analyze', 'check']):
            intent['operation'] = 'audit'
        
        # Detect object type with better mapping
        for alias, actual in self.object_aliases.items():
            if alias in query:
                intent['object_type'] = actual
                break
        
        # If no alias match, try direct detection
        if not intent['object_type']:
            # Look for specific object mentions
            if 'network' in query and 'discovery' not in query and 'sdn' not in query:
                intent['object_type'] = 'network'
            elif 'zone' in query:
                intent['object_type'] = 'zone_auth'
            elif 'a record' in query:
                intent['object_type'] = 'record:a'
            elif 'host record' in query:
                intent['object_type'] = 'record:host'
            elif 'dhcp' in query or 'range' in query:
                intent['object_type'] = 'range'
            elif 'ip' in query or 'address' in query:
                intent['object_type'] = 'ipv4address'
        
        # Detect attributes requested
        if 'extended attribute' in query or 'extattr' in query:
            intent['attributes'].append('extattrs')
        if 'comment' in query or 'description' in query:
            intent['attributes'].append('comment')
        if 'member' in query:
            intent['attributes'].append('members')
        
        return intent
    
    def handle_extended_attributes_query(self, query: str, intent: Dict[str, Any]) -> str:
        """Handle queries about extended attributes"""
        if intent['object_type'] == 'network' or 'network' in query:
            # Use the correct network listing tool
            result = self.execute_tool("list_network", {
                "return_fields": "network,comment,extattrs,members",
                "max_results": 100
            })
            
            if result.get("success"):
                networks = result["data"]
                response = "📊 **Networks with Extended Attributes:**\n\n"
                
                for i, net in enumerate(networks[:20], 1):
                    response += f"{i}. **{net.get('network', 'Unknown')}**\n"
                    if net.get('comment'):
                        response += f"   Comment: {net['comment']}\n"
                    
                    # Show extended attributes
                    extattrs = net.get('extattrs', {})
                    if extattrs:
                        response += "   Extended Attributes:\n"
                        for key, value in extattrs.items():
                            response += f"   • {key}: {value.get('value', 'N/A')}\n"
                    else:
                        response += "   Extended Attributes: None\n"
                    response += "\n"
                
                if len(networks) > 20:
                    response += f"... showing 20 of {len(networks)} networks\n"
                
                response += "\n💡 **Tip:** To see a specific network's extended attributes:\n"
                response += "`TOOL:{\"tool\":\"get_network\",\"params\":{\"ref\":\"<network_ref>\",\"return_fields\":\"network,extattrs\"}}`"
                
                return response
            else:
                return f"❌ Error: {result.get('error')}"
        
        return "Please specify which objects you want to see extended attributes for (e.g., networks, hosts, zones)"
    
    def get_detailed_fields(self, object_type: str) -> str:
        """Get appropriate return fields for detailed view"""
        field_maps = {
            "network": "network,comment,members,options,extattrs",
            "ipv4address": "ip_address,status,names,mac_address,network,usage,types",
            "record:host": "name,ipv4addrs,ipv6addrs,aliases,comment,extattrs",
            "record:a": "name,ipv4addr,comment,ttl,use_ttl,extattrs",
            "zone_auth": "fqdn,comment,view,primary_type,grid_primary",
            "range": "start_addr,end_addr,network,member,comment",
            "lease": "address,binding_state,client_hostname,hardware,starts,ends"
        }
        
        return field_maps.get(object_type, "")
    
    def suggest_alternatives(self, object_type: str) -> str:
        """Suggest working alternatives"""
        suggestions = []
        
        # Find similar working tools
        for tool_id in self.__class__.working_tools:
            tool_info = self.__class__.wapi_tools.get(tool_id, {})
            tool_obj = tool_info.get('object', '')
            
            # Look for similar object types
            if (object_type in tool_obj or 
                tool_obj in object_type or
                (object_type == 'network' and 'network' in tool_obj and 'discovery' not in tool_obj)):
                suggestions.append(f"• {tool_id} - {tool_info.get('description', '')}")
        
        if suggestions:
            return "Try these working alternatives:\n" + "\n".join(suggestions[:5])
        else:
            return "Try 'help' to see available operations"
    
    def provide_intelligent_suggestions(self, query: str, intent: Dict[str, Any]) -> str:
        """Provide intelligent suggestions based on query"""
        response = f"🤔 I understand you want to: **{query}**\n\n"
        
        if intent['operation'] and intent['object_type']:
            # Find the right tool
            tool_id = f"{intent['operation']}_{intent['object_type'].replace(':', '_')}"
            
            if tool_id in self.__class__.working_tools:
                tool_info = self.__class__.wapi_tools.get(tool_id, {})
                response += f"I recommend using: **{tool_info.get('name', tool_id)}**\n\n"
                
                # Generate example
                example_params = {}
                if intent['operation'] == 'list':
                    example_params = {"max_results": 100}
                    if intent['attributes']:
                        example_params["return_fields"] = ",".join(intent['attributes'])
                
                response += "Example:\n```\n"
                response += f'TOOL:{{"tool":"{tool_id}","params":{json.dumps(example_params)}}}'
                response += "\n```"
            else:
                response += "The exact tool you need might not be available.\n\n"
                response += self.suggest_alternatives(intent['object_type'])
        else:
            response += "I couldn't determine exactly what you need. Try:\n"
            response += "• Be more specific about the object type (network, DNS record, etc.)\n"
            response += "• Specify the operation (list, create, update, delete)\n"
            response += "• Or type 'help' for guidance"
        
        return response
    
    def list_networks(self) -> str:
        """List networks properly"""
        result = self.execute_tool("list_network", {
            "network_view": self.network_view,
            "return_fields": "network,comment,network_view",
            "max_results": 100
        })
        
        if result.get("success"):
            networks = result["data"]
            response = f"📊 **Networks in InfoBlox** (View: {self.network_view}):\n\n"
            
            for i, net in enumerate(networks[:25], 1):
                response += f"{i}. **{net.get('network', 'Unknown')}**\n"
                if net.get('comment'):
                    response += f"   • {net['comment']}\n"
                response += "\n"
            
            if len(networks) > 25:
                response += f"... showing 25 of {len(networks)} networks\n"
            
            response += "\n💡 To see extended attributes, use:\n"
            response += "`List all networks with their extended attributes`"
            
            return response
        else:
            return f"❌ Error: {result.get('error')}"
    
    def list_dns_zones(self) -> str:
        """List DNS zones"""
        result = self.execute_tool("list_zone_auth", {
            "return_fields": "fqdn,comment,view",
            "max_results": 50
        })
        
        if result.get("success"):
            zones = result["data"]
            response = "🌐 **DNS Zones in InfoBlox**\n\n"
            
            for i, zone in enumerate(zones[:20], 1):
                response += f"{i}. **{zone.get('fqdn', 'Unknown')}**\n"
                if zone.get('comment'):
                    response += f"   • {zone['comment']}\n"
                response += f"   • View: {zone.get('view', 'default')}\n\n"
            
            if len(zones) > 20:
                response += f"... showing 20 of {len(zones)} zones"
            
            return response
        else:
            return f"❌ Error: {result.get('error')}"
    
    def list_dhcp_ranges(self) -> str:
        """List DHCP ranges"""
        result = self.execute_tool("list_range", {
            "return_fields": "start_addr,end_addr,network,comment",
            "max_results": 50
        })
        
        if result.get("success"):
            ranges = result["data"]
            response = "🔄 **DHCP Ranges in InfoBlox**\n\n"
            
            for i, r in enumerate(ranges[:20], 1):
                response += f"{i}. **{r.get('start_addr', '?')} - {r.get('end_addr', '?')}**\n"
                response += f"   • Network: {r.get('network', 'Unknown')}\n"
                if r.get('comment'):
                    response += f"   • {r['comment']}\n"
                response += "\n"
            
            if len(ranges) > 20:
                response += f"... showing 20 of {len(ranges)} ranges"
            
            return response
        else:
            return f"❌ Error: {result.get('error')}"
    
    def audit_network(self, network: str) -> str:
        """Audit a network"""
        # Implementation as before
        result = self.execute_tool("list_ipv4address", {
            "network": network,
            "return_fields": "ip_address,status,names,mac_address",
            "max_results": 1000
        })
        
        if result.get("success"):
            ips = result["data"]
            response = f"🔍 **Network Audit for {network}**\n\n"
            
            used = [ip for ip in ips if ip.get("status") == "USED"]
            unused = [ip for ip in ips if ip.get("status") != "USED"]
            
            response += f"📊 **Summary:**\n"
            response += f"• Total IPs tracked: {len(ips)}\n"
            response += f"• Used: {len(used)}\n"
            response += f"• Available: {len(unused)}\n\n"
            
            if used:
                response += "✅ **Used IPs:**\n"
                for ip in used[:20]:
                    response += f"• {ip['ip_address']}"
                    if ip.get('names'):
                        response += f" - {', '.join(ip['names'])}"
                    if ip.get('mac_address'):
                        response += f" (MAC: {ip['mac_address']})"
                    response += "\n"
                if len(used) > 20:
                    response += f"... and {len(used) - 20} more\n"
            
            return response
        else:
            return f"❌ Error: {result.get('error')}"
    
    def search_ip(self, ip: str) -> str:
        """Search for IP information"""
        result = self.execute_tool("list_ipv4address", {
            "ip_address": ip,
            "return_fields": "ip_address,status,names,mac_address,network"
        })
        
        if result.get("success") and result.get("data"):
            ip_info = result["data"][0]
            response = f"🔎 **IP Address Information for {ip}**\n\n"
            response += f"• Status: {ip_info.get('status', 'Unknown')}\n"
            response += f"• Network: {ip_info.get('network', 'Unknown')}\n"
            if ip_info.get('names'):
                response += f"• DNS Names: {', '.join(ip_info['names'])}\n"
            if ip_info.get('mac_address'):
                response += f"• MAC Address: {ip_info['mac_address']}\n"
            return response
        else:
            return f"❌ IP address {ip} not found"
    
    def format_tool_result(self, result: Dict[str, Any], tool_id: str, original_query: str) -> str:
        """Format tool execution result"""
        if not result.get("success"):
            return f"❌ Error: {result.get('error', 'Unknown error')}"
        
        data = result.get("data", [])
        tool_info = self.__class__.wapi_tools.get(tool_id, {})
        
        # Special handling for network listing with extended attributes
        if tool_id == "list_network" and "extended attribute" in original_query.lower():
            return self.format_networks_with_extattrs(data)
        
        # Regular formatting
        operation = tool_info.get("operation", "")
        object_type = tool_info.get("object", "")
        
        if operation == "list":
            if not data:
                return f"No {object_type} objects found"
            
            response = f"📊 **{tool_info.get('name', tool_id)} Results**\n\n"
            response += f"Found {len(data)} items:\n\n"
            
            # Format based on object type
            for i, item in enumerate(data[:20], 1):
                if "network" in object_type:
                    response += f"{i}. **{item.get('network', 'Unknown')}**\n"
                    if item.get('comment'):
                        response += f"   • {item['comment']}\n"
                else:
                    # Generic format
                    key = item.get('name') or item.get('network') or item.get('address') or str(item)
                    response += f"{i}. {key}\n"
                response += "\n"
            
            if len(data) > 20:
                response += f"... showing 20 of {len(data)} items"
            
            return response
        
        return f"✅ Operation completed successfully"
    
    def format_networks_with_extattrs(self, networks: List[Dict]) -> str:
        """Format networks with extended attributes"""
        response = "📊 **Networks with Extended Attributes:**\n\n"
        
        for i, net in enumerate(networks[:20], 1):
            response += f"{i}. **{net.get('network', 'Unknown')}**\n"
            if net.get('comment'):
                response += f"   Comment: {net['comment']}\n"
            
            # Show extended attributes
            extattrs = net.get('extattrs', {})
            if extattrs:
                response += "   Extended Attributes:\n"
                for key, value in extattrs.items():
                    response += f"   • {key}: {value.get('value', 'N/A')}\n"
            else:
                response += "   Extended Attributes: None\n"
            response += "\n"
        
        if len(networks) > 20:
            response += f"... showing 20 of {len(networks)} networks"
        
        return response
    
    def execute_tool(self, tool_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool with error handling"""
        if tool_id not in self.__class__.wapi_tools:
            return {"error": f"Unknown tool: {tool_id}"}
        
        # Check if it's a working tool
        if tool_id not in self.__class__.working_tools:
            return {"error": f"Tool '{tool_id}' is not available (may require discovery service)"}
        
        tool_info = self.__class__.wapi_tools[tool_id]
        obj_type = tool_info["object"]
        operation = tool_info["operation"]
        
        try:
            if operation == "list":
                # List/search objects
                api_params = {
                    "_max_results": params.get("max_results", 100)
                }
                
                # Add return fields
                if params.get("return_fields"):
                    api_params["_return_fields"] = params["return_fields"]
                
                # Add filters
                for key, value in params.items():
                    if key not in ["max_results", "return_fields"]:
                        api_params[key] = value
                
                response = self.session.get(
                    f"{self.grid_master}/{obj_type}",
                    params=api_params
                )
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "data": response.json(),
                        "count": len(response.json())
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            # Other operations...
            # (Implementation continues as before)
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_help_message(self) -> str:
        """Get help message"""
        return """🚀 **Enhanced InfoBlox MCP Assistant**

I understand natural language better now! Try these:

**Network Operations:**
• `list all networks` - Shows all networks
• `list all networks with their extended attributes` - With extattrs
• `audit network 10.0.0.0/24` - Detailed network audit

**DNS Operations:**
• `list all DNS zones` - Shows all zones
• `list all A records` - Shows A records
• `list all host records` - Shows host records

**DHCP Operations:**
• `list all DHCP ranges` - Shows DHCP ranges
• `show DHCP leases` - Shows active leases

**IP Management:**
• `search IP **********` - Get IP information
• `find available IPs in 10.0.0.0/24` - Find free IPs

**Important Notes:**
• Discovery-related tools are excluded (they require discovery service)
• Use standard object names, not discovery variants
• Extended attributes are available for most objects

**Direct Tool Usage:**
```
TOOL:{"tool":"list_network","params":{"return_fields":"network,comment,extattrs"}}
```

💡 I now better understand what you mean and suggest the right tools!"""
    
    def send_json(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
    
    def log_message(self, format, *args):
        """Log messages"""
        logger.info(f"{format % args}")

if __name__ == "__main__":
    print("🚀 Starting Enhanced InfoBlox MCP Server...")
    print(f"Grid Master: {os.getenv('GRID_MASTER_URL', 'Not set')}")
    print(f"Network View: {os.getenv('NETWORK_VIEW', 'default')}")
    
    server = HTTPServer(('0.0.0.0', 8000), EnhancedInfoBloxMCP)
    
    print("\n✅ Server ready with enhanced context understanding!")
    print("• Excludes discovery tools that don't work")
    print("• Better understands natural language")
    print("• Properly handles extended attributes")
    
    server.serve_forever()
