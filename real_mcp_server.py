#!/usr/bin/env python3
"""
Enhanced InfoBlox MCP Server with Natural Language Understanding and Suggestions
"""
import requests
import json
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib3
import os
import re
from typing import Dict, List, Any, Optional
import logging
from urllib.parse import urlparse, parse_qs

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class EnhancedInfoBloxMCP(BaseHTTPRequestHandler):
    # Class variables for tools
    wapi_tools = {}
    wapi_objects = {}
    tool_categories = {}
    
    def __init__(self, *args, **kwargs):
        self.grid_master = os.getenv("GRID_MASTER_URL", "https://192.168.1.1/wapi/v2.13.1")
        self.username = os.getenv("INFOBLOX_USERNAME", "admin")
        self.password = os.getenv("INFOBLOX_PASSWORD", "infoblox")
        self.network_view = os.getenv("NETWORK_VIEW", "default")
        
        # Create session
        self.session = requests.Session()
        self.session.auth = (self.username, self.password)
        self.session.verify = False
        self.session.headers.update({'Content-Type': 'application/json'})
        
        # Load tools on first init
        if not self.__class__.wapi_tools:
            self.load_tools()
            self.categorize_tools()
        
        super().__init__(*args, **kwargs)
    
    def load_tools(self):
        """Load tool definitions"""
        try:
            if os.path.exists('infoblox_mcp_tools.json'):
                with open('infoblox_mcp_tools.json', 'r') as f:
                    tools = json.load(f)
                    for tool in tools:
                        self.__class__.wapi_tools[tool['id']] = tool
                logger.info(f"Loaded {len(self.__class__.wapi_tools)} MCP tools")
            
            if os.path.exists('discovered_wapi_objects.json'):
                with open('discovered_wapi_objects.json', 'r') as f:
                    self.__class__.wapi_objects = json.load(f)
        except Exception as e:
            logger.error(f"Error loading tools: {e}")
    
    def categorize_tools(self):
        """Organize tools into categories"""
        for tool_id, tool in self.__class__.wapi_tools.items():
            category = self.get_category(tool.get('object', ''))
            if category not in self.__class__.tool_categories:
                self.__class__.tool_categories[category] = []
            self.__class__.tool_categories[category].append(tool)
    
    def get_category(self, obj_type: str) -> str:
        """Get category for an object type"""
        if "network" in obj_type and "discovery" not in obj_type:
            return "Network"
        elif "record:" in obj_type:
            return "DNS Records"
        elif "zone" in obj_type:
            return "DNS Zones"
        elif any(x in obj_type for x in ["dhcp", "range", "lease", "fixed"]):
            return "DHCP"
        elif any(x in obj_type for x in ["ipv4", "ipv6"]):
            return "IPAM"
        elif any(x in obj_type for x in ["admin", "saml", "certificate"]):
            return "Security"
        elif "discovery" in obj_type:
            return "Discovery"
        elif any(x in obj_type for x in ["grid", "member"]):
            return "Grid"
        else:
            return "Other"
    
    def do_GET(self):
        """Handle GET requests"""
        path = urlparse(self.path).path
        
        if path == "/health":
            self.send_json({
                "status": "healthy",
                "grid_master": self.grid_master,
                "tools_loaded": len(self.__class__.wapi_tools)
            })
        
        elif path == "/v1/models":
            self.send_json({
                "data": [{
                    "id": "infoblox-assistant",
                    "name": "InfoBlox Assistant",
                    "description": f"Natural language InfoBlox management with {len(self.__class__.wapi_tools)} tools",
                    "object": "model",
                    "owned_by": "infoblox-wapi",
                    "permission": [],
                    "created": int(datetime.now().timestamp())
                }]
            })
        
        elif path == "/tools":
            self.send_json({
                "total": len(self.__class__.wapi_tools),
                "categories": {
                    cat: len(tools) for cat, tools in self.__class__.tool_categories.items()
                }
            })
        
        else:
            self.send_json({"status": "ok"})
    
    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        body = self.rfile.read(content_length).decode('utf-8')
        path = urlparse(self.path).path
        
        if path == "/v1/chat/completions":
            try:
                data = json.loads(body)
                messages = data.get("messages", [])
                
                if messages:
                    user_message = messages[-1].get("content", "")
                    response_content = self.process_query(user_message)
                else:
                    response_content = self.get_initial_greeting()
                
                response = {
                    "id": "chatcmpl-" + str(int(datetime.now().timestamp())),
                    "object": "chat.completion",
                    "created": int(datetime.now().timestamp()),
                    "model": "infoblox-assistant",
                    "choices": [{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_content
                        },
                        "finish_reason": "stop"
                    }]
                }
                self.send_json(response)
                
            except Exception as e:
                logger.error(f"Error: {e}")
                self.send_json({"error": str(e)})
        
        elif path == "/tools/execute":
            try:
                data = json.loads(body)
                tool_id = data.get("tool_id")
                params = data.get("parameters", {})
                
                result = self.execute_tool(tool_id, params)
                self.send_json(result)
                
            except Exception as e:
                self.send_json({"error": str(e)})
        
        else:
            self.send_404()
    
    def get_initial_greeting(self):
        """Return initial greeting with helpful suggestions"""
        return f"""👋 **Welcome to InfoBlox Assistant!**

I can help you manage your InfoBlox infrastructure with **{len(self.__class__.wapi_tools)} tools** using natural language.

**🚀 Just tell me what you need:**

📊 **"Show me all networks"**
🔍 **"Find IP **********"**
➕ **"Create a network *********/24"**
📋 **"List DNS zones"**
🌐 **"Show A records for example.com"**
🛠️ **"Browse available tools"**

**💡 Quick Actions:**

**Network Management:**
• View networks → Type: `list all networks`
• Create network → Type: `create network 10.x.x.x/24`
• Check usage → Type: `check network usage 10.x.x.x/24`

**DNS Management:**
• View zones → Type: `list dns zones`
• Show records → Type: `show A records`
• Create record → Type: `create dns record`

**DHCP Management:**
• View ranges → Type: `show dhcp ranges`
• Active leases → Type: `list active leases`
• Reservations → Type: `show dhcp reservations`

**🔧 Advanced:**
• `browse tools` - Explore all {len(self.__class__.wapi_tools)} tools
• `search tools: <keyword>` - Find specific tools
• Add `json` to any command for JSON output

**📡 Status:**
• Grid: {self.grid_master.split('/')[2]}
• View: {self.network_view}
• Connection: ✅ Active

What would you like to do?"""
    
    def process_query(self, query):
        """Process user query with natural language understanding"""
        original_query = query
        query_lower = query.lower().strip()
        
        # Check for JSON format
        output_json = "json" in query_lower or "format=json" in query_lower
        
        # Initial greeting/help
        if query_lower in ["", "hello", "hi", "help", "start", "?"] or len(query_lower) < 3:
            return self.get_initial_greeting()
        
        # Network operations - Natural language
        if self.is_network_query(query_lower):
            return self.handle_network_query(original_query, query_lower, output_json)
        
        # DNS operations - Natural language
        elif self.is_dns_query(query_lower):
            return self.handle_dns_query(original_query, query_lower, output_json)
        
        # DHCP operations - Natural language
        elif self.is_dhcp_query(query_lower):
            return self.handle_dhcp_query(original_query, query_lower, output_json)
        
        # IP operations - Natural language
        elif self.is_ip_query(query_lower):
            return self.handle_ip_query(original_query, query_lower, output_json)
        
        # Tool browsing
        elif "browse tools" in query_lower or "show tools" in query_lower:
            return self.get_tool_browser()
        
        # Tool search
        elif "search tools:" in query_lower or "find tool" in query_lower:
            if "search tools:" in query_lower:
                search_term = original_query.split("search tools:", 1)[1].strip()
            else:
                search_term = query_lower.replace("find tool", "").strip()
            return self.search_and_display_tools(search_term)
        
        # Direct tool execution
        elif query_lower.startswith("execute tool:"):
            try:
                tool_data = json.loads(original_query[13:])
                result = self.execute_tool(tool_data.get("tool_id"), tool_data.get("parameters", {}))
                return self.format_tool_result(result, suggest_next=True)
            except Exception as e:
                return f"❌ Error: {str(e)}\n\n💡 Make sure your JSON is properly formatted."
        
        # Test connection
        elif any(word in query_lower for word in ["test", "connection", "status"]):
            return self.test_connection_json() if output_json else self.test_connection()
        
        # Unknown query - provide suggestions
        else:
            return self.suggest_based_on_query(original_query)
    
    def is_network_query(self, query):
        """Check if query is network-related"""
        network_keywords = [
            "network", "subnet", "cidr", "networks",
            "create network", "new network", "add network",
            "list network", "show network", "view network",
            "network usage", "check usage", "utilization"
        ]
        return any(keyword in query for keyword in network_keywords)
    
    def handle_network_query(self, original_query, query_lower, output_json):
        """Handle network-related queries"""
        # List networks
        if any(phrase in query_lower for phrase in [
            "list", "show", "view", "display", "all network", "my network"
        ]):
            result = self.execute_tool("list_network", {})
            return self.format_network_list(result, output_json)
        
        # Create network
        elif any(word in query_lower for word in ["create", "add", "new"]):
            cidr_match = re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', original_query)
            if cidr_match:
                network = cidr_match.group(1)
                comment_match = re.search(r'(?:comment|description|desc)\s*["\']?([^"\']+)["\']?', original_query, re.I)
                params = {"network": network}
                if comment_match:
                    params["comment"] = comment_match.group(1).strip()
                
                result = self.execute_tool("create_network", params)
                return self.format_create_result(result, "network", output_json)
            else:
                return self.show_network_creation_help()
        
        # Network usage
        elif any(word in query_lower for word in ["usage", "utilization", "used"]):
            cidr_match = re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', original_query)
            if cidr_match:
                return self.get_network_usage(cidr_match.group(1), output_json)
            else:
                return "Please specify a network. Example: `check network usage 10.0.0.0/24`"
        
        # Find specific network
        elif any(word in query_lower for word in ["find", "search", "get"]):
            cidr_match = re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', original_query)
            if cidr_match:
                result = self.execute_tool("list_network", {"network": cidr_match.group(1)})
                return self.format_network_list(result, output_json)
            else:
                return "Please specify a network to find. Example: `find network 10.0.0.0/24`"
        
        else:
            return self.suggest_network_operations()
    
    def is_dns_query(self, query):
        """Check if query is DNS-related"""
        dns_keywords = [
            "dns", "zone", "domain", "record",
            "a record", "cname", "ptr", "mx record",
            "create record", "add record",
            "list zone", "show zone"
        ]
        return any(keyword in query for keyword in dns_keywords)
    
    def handle_dns_query(self, original_query, query_lower, output_json):
        """Handle DNS-related queries"""
        # List DNS zones
        if "zone" in query_lower and any(word in query_lower for word in ["list", "show", "view"]):
            result = self.execute_tool("list_zone_auth", {})
            return self.format_dns_zones(result, output_json)
        
        # DNS records
        elif "record" in query_lower:
            # Determine record type
            record_type = None
            if "a record" in query_lower or "a-record" in query_lower:
                record_type = "a"
            elif "cname" in query_lower:
                record_type = "cname"
            elif "ptr" in query_lower:
                record_type = "ptr"
            elif "mx" in query_lower:
                record_type = "mx"
            
            # Create record
            if any(word in query_lower for word in ["create", "add", "new"]):
                if record_type == "a":
                    return self.show_create_a_record_help()
                else:
                    return self.show_dns_record_creation_help(record_type)
            
            # List records
            elif any(word in query_lower for word in ["list", "show", "view"]):
                if record_type:
                    tool_id = f"list_record_{record_type}"
                    params = {}
                    
                    # Check for zone filter
                    zone_match = re.search(r'(?:for|in|zone)\s+(\S+\.\S+)', original_query, re.I)
                    if zone_match:
                        params["zone"] = zone_match.group(1)
                    
                    result = self.execute_tool(tool_id, params)
                    return self.format_dns_records(result, record_type, output_json)
                else:
                    return "What type of DNS record? (A, CNAME, PTR, MX, etc.)"
        
        else:
            return self.suggest_dns_operations()
    
    def is_dhcp_query(self, query):
        """Check if query is DHCP-related"""
        dhcp_keywords = [
            "dhcp", "lease", "reservation", "fixed address",
            "dhcp range", "active lease", "current lease"
        ]
        return any(keyword in query for keyword in dhcp_keywords)
    
    def handle_dhcp_query(self, original_query, query_lower, output_json):
        """Handle DHCP-related queries"""
        # DHCP ranges
        if "range" in query_lower:
            result = self.execute_tool("list_range", {})
            return self.format_dhcp_ranges(result, output_json)
        
        # DHCP leases
        elif "lease" in query_lower:
            result = self.execute_tool("list_lease", {})
            return self.format_dhcp_leases(result, output_json)
        
        # DHCP reservations/fixed addresses
        elif any(word in query_lower for word in ["reservation", "fixed"]):
            if any(word in query_lower for word in ["create", "add", "new"]):
                return self.show_dhcp_reservation_help()
            else:
                result = self.execute_tool("list_fixedaddress", {})
                return self.format_dhcp_reservations(result, output_json)
        
        else:
            return self.suggest_dhcp_operations()
    
    def is_ip_query(self, query):
        """Check if query is IP-related"""
        return (re.search(r'\d+\.\d+\.\d+\.\d+', query) and 
                not re.search(r'\d+\.\d+\.\d+\.\d+/\d+', query)) or \
               any(word in query for word in ["ip address", "find ip", "search ip", "check ip"])
    
    def handle_ip_query(self, original_query, query_lower, output_json):
        """Handle IP address queries"""
        ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)(?!/)', original_query)
        
        if ip_match:
            ip = ip_match.group(1)
            result = self.execute_tool("list_ipv4address", {"ip_address": ip})
            return self.format_ip_search(result, ip, output_json)
        else:
            return "Please specify an IP address. Example: `find IP **********`"
    
    def suggest_based_on_query(self, query):
        """Provide intelligent suggestions based on query"""
        query_lower = query.lower()
        
        response = f"🤔 I'm not quite sure what you mean by: **{query}**\n\n"
        response += "Here are some things you might want to try:\n\n"
        
        # Extract any IPs or networks from query
        ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', query)
        network_match = re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', query)
        
        if network_match:
            network = network_match.group(1)
            response += f"**For network {network}:**\n"
            response += f"• `show network {network}` - View details\n"
            response += f"• `check network usage {network}` - See utilization\n"
            response += f"• `list IPs in {network}` - Show all IPs\n\n"
        elif ip_match:
            ip = ip_match.group(1)
            response += f"**For IP {ip}:**\n"
            response += f"• `find IP {ip}` - Search for this IP\n"
            response += f"• `create dhcp reservation for {ip}` - Reserve it\n\n"
        
        # General suggestions
        response += "**Or try these common tasks:**\n"
        response += "• `list all networks` - View your networks\n"
        response += "• `list dns zones` - Show DNS zones\n"
        response += "• `browse tools` - See all available tools\n"
        response += f"• `search tools: {query_lower}` - Find related tools\n"
        
        response += "\n💡 **Tip:** I understand natural language! Just tell me what you want to do."
        
        return response
    
    # Formatting methods
    def format_network_list(self, result, output_json):
        """Format network list results"""
        if output_json:
            return json.dumps(result, indent=2)
        
        if not result.get("success"):
            return f"❌ Error: {result.get('error', 'Unknown error')}"
        
        networks = result.get("data", [])
        if not networks:
            return "No networks found. Would you like to create one? Type: `create network 10.x.x.x/24`"
        
        response = f"📊 **Found {len(networks)} Networks**\n\n"
        
        for i, net in enumerate(networks[:10], 1):
            response += f"**{i}. {net.get('network', 'Unknown')}**\n"
            if net.get('comment'):
                response += f"   📝 {net['comment']}\n"
            if 'extattrs' in net:
                extattrs = net['extattrs']
                if extattrs:
                    response += "   🏷️ "
                    attrs = []
                    for key, value in extattrs.items():
                        if isinstance(value, dict) and 'value' in value:
                            attrs.append(f"{key}: {value['value']}")
                        else:
                            attrs.append(f"{key}: {value}")
                    response += ", ".join(attrs[:3])
                    if len(attrs) > 3:
                        response += f" +{len(attrs)-3} more"
                    response += "\n"
            response += "\n"
        
        if len(networks) > 10:
            response += f"... and {len(networks) - 10} more networks\n\n"
        
        response += "**Next actions:**\n"
        response += "• `check network usage <network>` - View utilization\n"
        response += "• `create network 10.x.x.x/24` - Add new network\n"
        response += "• `find IPs in <network>` - List IPs"
        
        return response
    
    def format_create_result(self, result, item_type, output_json):
        """Format creation results"""
        if output_json:
            return json.dumps(result, indent=2)
        
        if result.get("success"):
            response = f"✅ **Successfully created {item_type}!**\n\n"
            response += f"Reference: `{result.get('ref', 'N/A')}`\n\n"
            response += "**What would you like to do next?**\n"
            response += f"• `list all {item_type}s` - View all items\n"
            response += f"• `create another {item_type}` - Add more\n"
            response += "• `browse tools` - See other options"
        else:
            response = f"❌ **Failed to create {item_type}**\n\n"
            response += f"Error: {result.get('error', 'Unknown error')}\n\n"
            response += "**Common issues:**\n"
            response += "• Item already exists\n"
            response += "• Invalid parameters\n"
            response += "• Permission denied\n\n"
            response += "Try checking the parameters and trying again."
        
        return response
    
    def format_dns_zones(self, result, output_json):
        """Format DNS zone list"""
        if output_json:
            return json.dumps(result, indent=2)
        
        if not result.get("success"):
            return f"❌ Error: {result.get('error', 'Unknown error')}"
        
        zones = result.get("data", [])
        if not zones:
            return "No DNS zones found. Would you like to create one?"
        
        # Separate forward and reverse zones
        forward_zones = [z for z in zones if not z.get('fqdn', '').endswith('.in-addr.arpa')]
        reverse_zones = [z for z in zones if z.get('fqdn', '').endswith('.in-addr.arpa')]
        
        response = f"🌐 **DNS Zones** ({len(zones)} total)\n\n"
        
        if forward_zones:
            response += f"**Forward Zones ({len(forward_zones)}):**\n"
            for zone in forward_zones[:10]:
                response += f"• {zone.get('fqdn', 'Unknown')}"
                if zone.get('comment'):
                    response += f" - {zone['comment']}"
                response += "\n"
            if len(forward_zones) > 10:
                response += f"... and {len(forward_zones) - 10} more\n"
        
        if reverse_zones:
            response += f"\n**Reverse Zones ({len(reverse_zones)}):**\n"
            for zone in reverse_zones[:5]:
                response += f"• {zone.get('fqdn', 'Unknown')}\n"
            if len(reverse_zones) > 5:
                response += f"... and {len(reverse_zones) - 5} more\n"
        
        response += "\n**Next actions:**\n"
        response += "• `show A records for <zone>` - List records\n"
        response += "• `create DNS record` - Add new record\n"
        response += "• `search tools: dns` - More DNS tools"
        
        return response
    
    def format_ip_search(self, result, ip, output_json):
        """Format IP search results"""
        if output_json:
            return json.dumps(result, indent=2)
        
        if not result.get("success"):
            return f"❌ Error: {result.get('error', 'Unknown error')}"
        
        data = result.get("data", [])
        
        if not data:
            response = f"🔍 **IP {ip} not found**\n\n"
            response += "This IP is not currently tracked in InfoBlox.\n\n"
            response += "**You can:**\n"
            response += f"• `create dhcp reservation for {ip}` - Reserve this IP\n"
            response += f"• `add host record for {ip}` - Create DNS entry\n"
            response += "• `list all networks` - See available networks"
        else:
            ip_info = data[0]
            response = f"🔍 **IP Address: {ip}**\n\n"
            response += f"**Status:** {ip_info.get('status', 'Unknown')}\n"
            
            if ip_info.get('names'):
                response += f"**DNS Names:** {', '.join(ip_info['names'])}\n"
            
            if ip_info.get('network'):
                response += f"**Network:** {ip_info['network']}\n"
            
            if ip_info.get('mac_address'):
                response += f"**MAC Address:** {ip_info['mac_address']}\n"
            
            if ip_info.get('usage'):
                response += f"**Usage:** {', '.join(ip_info['usage'])}\n"
            
            response += "\n**Next actions:**\n"
            response += f"• `check network usage {ip_info.get('network', 'network')}` - View network\n"
            response += f"• `update IP {ip}` - Modify this IP\n"
            response += "• `find similar IPs` - Search nearby"
        
        return response
    
    # Help methods
    def show_network_creation_help(self):
        """Show help for creating networks"""
        return """🌐 **Create a Network**

I need the network address in CIDR format. Here are some examples:

**Basic:**
• `create network *********/24`
• `create network *************/24`

**With description:**
• `create network *********/24 comment "Guest WiFi"`
• `create network **********/16 description "Data Center"`

**Common subnet sizes:**
• `/24` = 254 usable IPs (most common)
• `/23` = 510 usable IPs
• `/22` = 1,022 usable IPs
• `/16` = 65,534 usable IPs

What network would you like to create?"""
    
    def show_create_a_record_help(self):
        """Show help for creating A records"""
        return """📝 **Create an A Record**

To create an A record, use this format:

**Examples:**
• `create A record server.example.com **********`
• `add A record mail.example.com pointing to *********`
• `create dns A record www.example.com = *********`

**Or use the full command:**
```
execute tool: {"tool_id": "create_record_a", "parameters": {"name": "server.example.com", "ipv4addr": "**********"}}
```

What A record would you like to create?"""
    
    def suggest_network_operations(self):
        """Suggest network operations"""
        return """🌐 **Network Operations**

Here's what you can do with networks:

**View:**
• `list all networks` - Show all networks
• `find network 10.0.0.0/24` - Search specific network
• `check network usage 10.0.0.0/24` - View utilization

**Create:**
• `create network *********/24` - Add new network
• `create network *********/24 comment "Test"` - With description

**Manage:**
• `update network 10.0.0.0/24` - Modify network
• `delete network 10.0.0.0/24` - Remove network

**Explore:**
• `search tools: network` - Find all network tools
• `browse tools` - See all available tools

What would you like to do?"""
    
    def suggest_dns_operations(self):
        """Suggest DNS operations"""
        return """🌐 **DNS Operations**

Here's what you can do with DNS:

**Zones:**
• `list dns zones` - Show all zones
• `create dns zone example.com` - Add new zone

**Records:**
• `show A records` - List all A records
• `show A records for example.com` - For specific zone
• `create A record server.example.com **********` - Add A record
• `list CNAME records` - Show CNAME records
• `show PTR records` - List reverse DNS

**Search:**
• `find record server.example.com` - Search for record
• `search tools: dns` - Find all DNS tools

What would you like to do?"""
    
    def suggest_dhcp_operations(self):
        """Suggest DHCP operations"""
        return """📡 **DHCP Operations**

Here's what you can do with DHCP:

**Ranges:**
• `show dhcp ranges` - List all DHCP ranges
• `create dhcp range` - Add new range

**Leases:**
• `list active leases` - Show current leases
• `find lease for MAC 00:11:22:33:44:55` - Search lease

**Reservations:**
• `show dhcp reservations` - List fixed addresses
• `create dhcp reservation ********** MAC 00:11:22:33:44:55` - Add reservation

**Explore:**
• `search tools: dhcp` - Find all DHCP tools
• `browse tools` - See all available tools

What would you like to do?"""
    
    def get_tool_browser(self):
        """Return tool browser interface"""
        categories = {}
        for tool_id, tool in self.__class__.wapi_tools.items():
            category = self.get_category(tool.get('object', ''))
            if category not in categories:
                categories[category] = 0
            categories[category] += 1
        
        response = f"🛠️ **InfoBlox Tool Browser**\n\n"
        response += f"Explore {len(self.__class__.wapi_tools)} tools organized by category:\n\n"
        
        for category, count in sorted(categories.items()):
            response += f"**{category}** ({count} tools)\n"
            response += f"• `search tools: {category.lower()}` - View all {category} tools\n\n"
        
        response += "**How to use tools:**\n"
        response += "1. Search: `search tools: <keyword>`\n"
        response += "2. Find the tool you need\n"
        response += "3. Copy the example command\n"
        response += "4. Execute with your parameters\n\n"
        
        response += "**Popular searches:**\n"
        response += "• `search tools: create` - All creation tools\n"
        response += "• `search tools: list` - All listing tools\n"
        response += "• `search tools: network` - Network tools\n"
        response += "• `search tools: dns` - DNS tools"
        
        return response
    
    def search_and_display_tools(self, search_term):
        """Search and display tools"""
        if not search_term:
            return "Please provide a search term. Example: `search tools: network`"
        
        search_lower = search_term.lower()
        matching_tools = []
        
        # Search through all tools
        for tool_id, tool in self.__class__.wapi_tools.items():
            if any([
                search_lower in tool_id.lower(),
                search_lower in tool.get('description', '').lower(),
                search_lower in tool.get('object', '').lower(),
                search_lower in self.get_category(tool.get('object', '')).lower()
            ]):
                matching_tools.append((tool_id, tool))
        
        if not matching_tools:
            return f"❌ No tools found for '{search_term}'\n\nTry: `browse tools` to see categories"
        
        # Sort by relevance
        matching_tools.sort(key=lambda x: (
            search_lower not in x[0].lower(),
            x[1].get('operation') != 'list'
        ))
        
        response = f"🔍 **Found {len(matching_tools)} tools for '{search_term}'**\n\n"
        
        # Group by operation
        by_operation = {}
        for tool_id, tool in matching_tools[:20]:
            op = tool.get('operation', 'other')
            if op not in by_operation:
                by_operation[op] = []
            by_operation[op].append((tool_id, tool))
        
        for operation, tools in by_operation.items():
            response += f"**{operation.upper()} Operations:**\n\n"
            
            for tool_id, tool in tools[:5]:
                response += f"📌 **{tool_id}**\n"
                response += f"   {tool.get('description', 'No description')}\n"
                
                # Show example
                params = {}
                if operation == "create" and "network" in tool_id:
                    params = {"network": "10.0.0.0/24"}
                elif operation == "create" and "record_a" in tool_id:
                    params = {"name": "host.example.com", "ipv4addr": "**********"}
                
                response += f"   ```\n   execute tool: {{\"tool_id\": \"{tool_id}\", \"parameters\": {json.dumps(params)}}}\n   ```\n\n"
            
            if len(tools) > 5:
                response += f"   ... and {len(tools) - 5} more {operation} tools\n\n"
        
        if len(matching_tools) > 20:
            response += f"Showing 20 of {len(matching_tools)} results. Try a more specific search."
        
        return response
    
    def test_connection(self):
        """Test connection"""
        try:
            response = self.session.get(
                f"{self.grid_master}/grid",
                params={"_return_fields": "name"}
            )
            
            if response.status_code == 200:
                grid_info = response.json()
                if isinstance(grid_info, list) and grid_info:
                    grid_data = grid_info[0]
                    return f"""✅ **Connection Successful!**

**Grid Information:**
• Name: {grid_data.get('name', 'Unknown')}
• Master: {self.grid_master}
• API: {self.grid_master.split('/')[-1]}
• View: {self.network_view}
• Tools: {len(self.__class__.wapi_tools)} available

Ready to help! What would you like to do?"""
            else:
                return f"❌ Connection failed: HTTP {response.status_code}"
        except Exception as e:
            return f"❌ Connection error: {str(e)}"
    
    def test_connection_json(self):
        """Test connection with JSON output"""
        try:
            response = self.session.get(
                f"{self.grid_master}/grid",
                params={"_return_fields": "name"}
            )
            
            if response.status_code == 200:
                grid_info = response.json()
                if isinstance(grid_info, list) and grid_info:
                    return json.dumps({
                        "status": "success",
                        "grid": {
                            "name": grid_info[0].get('name', 'Unknown'),
                            "master": self.grid_master,
                            "api_version": self.grid_master.split('/')[-1],
                            "network_view": self.network_view,
                            "tools_available": len(self.__class__.wapi_tools)
                        }
                    }, indent=2)
            
            return json.dumps({
                "status": "error",
                "code": response.status_code
            }, indent=2)
            
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)
    
    def execute_tool(self, tool_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool"""
        if tool_id not in self.__class__.wapi_tools:
            # Try to find by partial match
            matches = [tid for tid in self.__class__.wapi_tools if tool_id in tid]
            if matches:
                tool_id = matches[0]
            else:
                return {"error": f"Unknown tool: {tool_id}"}
        
        tool = self.__class__.wapi_tools[tool_id]
        obj_type = tool["object"]
        operation = tool["operation"]
        
        try:
            if operation == "list":
                api_params = {
                    "_max_results": params.get("_max_results", 100)
                }
                
                if params.get("_return_fields"):
                    api_params["_return_fields"] = params["_return_fields"]
                
                for key, value in params.items():
                    if key not in ["_max_results", "_return_fields"] and value:
                        api_params[key] = value
                
                response = self.session.get(
                    f"{self.grid_master}/{obj_type}",
                    params=api_params
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "success": True,
                        "count": len(data),
                        "data": data
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif operation == "create":
                response = self.session.post(
                    f"{self.grid_master}/{obj_type}",
                    json=params
                )
                
                if response.status_code in [200, 201]:
                    return {
                        "success": True,
                        "ref": response.json(),
                        "message": f"Created {obj_type}"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            else:
                return {"error": f"Operation {operation} not implemented"}
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def format_tool_result(self, result, suggest_next=False):
        """Format tool execution result"""
        if not result.get("success"):
            return f"❌ **Error:** {result.get('error', 'Unknown error')}"
        
        data = result.get("data", [])
        
        if isinstance(data, list):
            if not data:
                return "No results found."
            
            response = f"✅ **Found {len(data)} results**\n\n"
            
            # Show first few results
            for i, item in enumerate(data[:5], 1):
                if isinstance(item, dict):
                    # Format based on content
                    if 'network' in item:
                        response += f"{i}. **{item['network']}**"
                        if 'comment' in item:
                            response += f" - {item['comment']}"
                    elif 'name' in item:
                        response += f"{i}. **{item['name']}**"
                    elif 'fqdn' in item:
                        response += f"{i}. **{item['fqdn']}**"
                    else:
                        key = list(item.keys())[0] if item else "Item"
                        response += f"{i}. **{item.get(key, 'Unknown')}**"
                    response += "\n"
            
            if len(data) > 5:
                response += f"\n... and {len(data) - 5} more results"
            
            if suggest_next:
                response += "\n\n💡 Add `json` to see full details"
        else:
            response = f"✅ **Success**\n\n{json.dumps(data, indent=2)}"
        
        return response
    
    def get_network_usage(self, network, output_json):
        """Get network usage information"""
        # Get network info
        net_result = self.execute_tool("list_network", {"network": network})
        
        if not net_result.get("success") or not net_result.get("data"):
            return f"❌ Network {network} not found"
        
        # Get IPs in network
        ip_result = self.execute_tool("list_ipv4address", {
            "network": network,
            "_max_results": 10000
        })
        
        if output_json:
            return json.dumps({
                "network": net_result,
                "ip_addresses": ip_result
            }, indent=2)
        
        # Calculate usage
        total_ips = 2 ** (32 - int(network.split('/')[1])) - 2
        
        if ip_result.get("success"):
            ips = ip_result.get("data", [])
            used_ips = len([ip for ip in ips if ip.get("status") == "USED"])
            available_ips = total_ips - used_ips
            usage_percent = (used_ips / total_ips * 100) if total_ips > 0 else 0
            
            response = f"📊 **Network Usage: {network}**\n\n"
            
            net_data = net_result["data"][0]
            if net_data.get("comment"):
                response += f"**Description:** {net_data['comment']}\n\n"
            
            response += f"**Statistics:**\n"
            response += f"• Total IPs: {total_ips:,}\n"
            response += f"• Used: {used_ips:,} ({usage_percent:.1f}%)\n"
            response += f"• Available: {available_ips:,} ({100-usage_percent:.1f}%)\n\n"
            
            # Visual bar
            bar_length = 30
            filled = int(bar_length * usage_percent / 100)
            bar = "█" * filled + "░" * (bar_length - filled)
            response += f"[{bar}] {usage_percent:.1f}%\n"
            
            if used_ips > 0:
                response += f"\n**Sample Used IPs:**\n"
                shown = 0
                for ip in ips:
                    if ip.get("status") == "USED" and shown < 5:
                        response += f"• {ip['ip_address']}"
                        if ip.get('names'):
                            response += f" - {', '.join(ip['names'])}"
                        response += "\n"
                        shown += 1
                
                if used_ips > 5:
                    response += f"... and {used_ips - 5} more"
            
            response += "\n\n**Next actions:**\n"
            response += f"• `find available IPs in {network}` - Get free IPs\n"
            response += f"• `create dhcp range in {network}` - Add DHCP\n"
            response += "• `browse tools` - More options"
            
            return response
        else:
            return f"❌ Error getting IP information: {ip_result.get('error', 'Unknown')}"
    
    def format_dhcp_ranges(self, result, output_json):
        """Format DHCP ranges"""
        if output_json:
            return json.dumps(result, indent=2)
        
        if not result.get("success"):
            return f"❌ Error: {result.get('error', 'Unknown error')}"
        
        ranges = result.get("data", [])
        if not ranges:
            return "No DHCP ranges found. Would you like to create one?"
        
        response = f"📡 **DHCP Ranges** ({len(ranges)} total)\n\n"
        
        for i, range_data in enumerate(ranges[:10], 1):
            response += f"**{i}. {range_data.get('start_addr', '?')} - {range_data.get('end_addr', '?')}**\n"
            if range_data.get('network'):
                response += f"   Network: {range_data['network']}\n"
            if range_data.get('comment'):
                response += f"   Comment: {range_data['comment']}\n"
            response += "\n"
        
        if len(ranges) > 10:
            response += f"... and {len(ranges) - 10} more\n\n"
        
        response += "**Next actions:**\n"
        response += "• `list active leases` - View current leases\n"
        response += "• `create dhcp range` - Add new range\n"
        response += "• `show dhcp reservations` - View fixed addresses"
        
        return response
    
    def format_dhcp_leases(self, result, output_json):
        """Format DHCP leases"""
        if output_json:
            return json.dumps(result, indent=2)
        
        if not result.get("success"):
            return f"❌ Error: {result.get('error', 'Unknown error')}"
        
        leases = result.get("data", [])
        if not leases:
            return "No active DHCP leases found."
        
        response = f"📡 **Active DHCP Leases** ({len(leases)} total)\n\n"
        
        for i, lease in enumerate(leases[:20], 1):
            response += f"**{i}. {lease.get('address', 'Unknown IP')}**\n"
            if lease.get('client_hostname'):
                response += f"   Hostname: {lease['client_hostname']}\n"
            if lease.get('hardware'):
                response += f"   MAC: {lease['hardware']}\n"
            if lease.get('starts'):
                response += f"   Started: {lease['starts']}\n"
            response += "\n"
        
        if len(leases) > 20:
            response += f"... and {len(leases) - 20} more\n\n"
        
        response += "**Next actions:**\n"
        response += "• `create dhcp reservation` - Make permanent\n"
        response += "• `show dhcp ranges` - View ranges\n"
        response += "• `search tools: lease` - More lease tools"
        
        return response
    
    def format_dhcp_reservations(self, result, output_json):
        """Format DHCP reservations"""
        if output_json:
            return json.dumps(result, indent=2)
        
        if not result.get("success"):
            return f"❌ Error: {result.get('error', 'Unknown error')}"
        
        reservations = result.get("data", [])
        if not reservations:
            return "No DHCP reservations found. Type `create dhcp reservation` to add one."
        
        response = f"🔒 **DHCP Reservations** ({len(reservations)} total)\n\n"
        
        for i, res in enumerate(reservations[:10], 1):
            response += f"**{i}. {res.get('ipv4addr', 'Unknown IP')}**\n"
            if res.get('mac'):
                response += f"   MAC: {res['mac']}\n"
            if res.get('name'):
                response += f"   Name: {res['name']}\n"
            if res.get('comment'):
                response += f"   Comment: {res['comment']}\n"
            response += "\n"
        
        if len(reservations) > 10:
            response += f"... and {len(reservations) - 10} more\n\n"
        
        response += "**Next actions:**\n"
        response += "• `create dhcp reservation` - Add new\n"
        response += "• `list active leases` - View leases\n"
        response += "• `delete reservation <ip>` - Remove"
        
        return response
    
    def format_dns_records(self, result, record_type, output_json):
        """Format DNS records"""
        if output_json:
            return json.dumps(result, indent=2)
        
        if not result.get("success"):
            return f"❌ Error: {result.get('error', 'Unknown error')}"
        
        records = result.get("data", [])
        if not records:
            return f"No {record_type.upper()} records found. Would you like to create one?"
        
        response = f"📝 **{record_type.upper()} Records** ({len(records)} total)\n\n"
        
        for i, record in enumerate(records[:20], 1):
            if record_type == "a":
                response += f"**{i}. {record.get('name', 'Unknown')} → {record.get('ipv4addr', 'Unknown')}**\n"
            elif record_type == "cname":
                response += f"**{i}. {record.get('name', 'Unknown')} → {record.get('canonical', 'Unknown')}**\n"
            elif record_type == "ptr":
                response += f"**{i}. {record.get('ptrdname', 'Unknown')} → {record.get('name', 'Unknown')}**\n"
            else:
                response += f"**{i}. {record.get('name', record.get('fqdn', 'Unknown'))}**\n"
            
            if record.get('comment'):
                response += f"   Comment: {record['comment']}\n"
            if record.get('ttl'):
                response += f"   TTL: {record['ttl']}\n"
            response += "\n"
        
        if len(records) > 20:
            response += f"... and {len(records) - 20} more\n\n"
        
        response += "**Next actions:**\n"
        response += f"• `create {record_type} record` - Add new\n"
        response += f"• `delete {record_type} record <name>` - Remove\n"
        response += "• `search tools: dns record` - More DNS tools"
        
        return response
    
    def show_dns_record_creation_help(self, record_type):
        """Show help for creating DNS records"""
        if not record_type:
            return """📝 **Create DNS Record**

What type of record would you like to create?

• `create A record` - Map hostname to IPv4
• `create AAAA record` - Map hostname to IPv6
• `create CNAME record` - Create alias
• `create PTR record` - Reverse DNS
• `create MX record` - Mail exchanger
• `create TXT record` - Text record

Or `search tools: create record` to see all options."""
        
        examples = {
            "cname": "create CNAME alias.example.com pointing to server.example.com",
            "ptr": "create PTR record for ********** pointing to server.example.com",
            "mx": "create MX record for example.com priority 10 mail.example.com",
            "txt": 'create TXT record example.com "v=spf1 include:_spf.google.com ~all"'
        }
        
        return f"""📝 **Create {record_type.upper()} Record**

Example:
• `{examples.get(record_type, f'create {record_type} record')}`

Or use:
```
execute tool: {{"tool_id": "create_record_{record_type}", "parameters": {{...}}}}
```

What would you like to create?"""
    
    def show_dhcp_reservation_help(self):
        """Show help for DHCP reservations"""
        return """🔒 **Create DHCP Reservation**

To reserve an IP for a specific device:

**Examples:**
• `create dhcp reservation ********** for MAC 00:11:22:33:44:55`
• `add fixed address ********** MAC aa:bb:cc:dd:ee:ff hostname server1`

**Required information:**
• IP Address (must be within a network)
• MAC Address (format: XX:XX:XX:XX:XX:XX)

**Optional:**
• Hostname
• Comment/Description

Or use:
```
execute tool: {"tool_id": "create_fixedaddress", "parameters": {"ipv4addr": "**********", "mac": "00:11:22:33:44:55"}}
```

What reservation would you like to create?"""
    
    def send_json(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
    
    def send_404(self):
        """Send 404 response"""
        self.send_response(404)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({"error": "Not found"}).encode())
    
    def log_message(self, format, *args):
        """Log messages"""
        logger.info(f"{format % args}")

if __name__ == "__main__":
    print(f"🚀 Starting Enhanced InfoBlox MCP Server with Natural Language...")
    print(f"Grid Master: {os.getenv('GRID_MASTER_URL', 'Not set')}")
    print(f"Network View: {os.getenv('NETWORK_VIEW', 'default')}")
    
    server = HTTPServer(('0.0.0.0', 8000), EnhancedInfoBloxMCP)
    
    print(f"\n✅ Server ready with natural language understanding!")
    print("\n📍 Endpoints:")
    print("• Chat: http://localhost:8000/v1/chat/completions")
    print("• Health: http://localhost:8000/health")
    print("• Models: http://localhost:8000/v1/models")
    
    server.serve_forever()
