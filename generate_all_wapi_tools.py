import json
import requests
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Load the schema
with open('wapi_schema.json', 'r') as f:
    schema = json.load(f)

print(f"📊 Processing {len(schema['supported_objects'])} WAPI objects...")

# Configuration
GRID_MASTER = "https://192.168.1.222/wapi/v2.13.1"
USERNAME = "admin"
PASSWORD = "infoblox"

# Create session
session = requests.Session()
session.auth = (USERNAME, PASSWORD)
session.verify = False

# Store discovered objects and tools
discovered_objects = {}
all_tools = []

# Process each object
for obj_type in schema['supported_objects']:
    print(f"Processing {obj_type}...")
    
    try:
        # Try to get schema for this object
        response = session.get(f"{GRID_MASTER}/{obj_type}?_schema&_return_type=json-pretty")
        
        if response.status_code == 200:
            obj_schema = response.json()
            
            # Store object info
            discovered_objects[obj_type] = {
                "exists": True,
                "fields": list(obj_schema.get("fields", {}).keys()),
                "functions": list(obj_schema.get("functions", {}).keys()) if obj_schema.get("functions") else [],
                "supports": {
                    "create": obj_schema.get("supports_create", True),
                    "update": obj_schema.get("supports_update", True),
                    "delete": obj_schema.get("supports_delete", True),
                    "search": obj_schema.get("supports_search", True)
                }
            }
        else:
            # Basic support assumed
            discovered_objects[obj_type] = {
                "exists": True,
                "fields": [],
                "functions": [],
                "supports": {
                    "create": True,
                    "update": True,
                    "delete": True,
                    "search": True
                }
            }
    except:
        # Still add it with basic support
        discovered_objects[obj_type] = {
            "exists": True,
            "fields": [],
            "functions": [],
            "supports": {
                "create": True,
                "update": True,
                "delete": True,
                "search": True
            }
        }
    
    # Generate tools for this object
    safe_name = obj_type.replace(":", "_").replace(".", "_")
    
    # List/Search tool
    all_tools.append({
        "id": f"list_{safe_name}",
        "object": obj_type,
        "operation": "list",
        "name": f"List {obj_type}",
        "description": f"List/search {obj_type} objects with filters"
    })
    
    # Get tool
    all_tools.append({
        "id": f"get_{safe_name}",
        "object": obj_type,
        "operation": "get",
        "name": f"Get {obj_type}",
        "description": f"Get specific {obj_type} by reference"
    })
    
    # Create tool
    if discovered_objects[obj_type]["supports"]["create"]:
        all_tools.append({
            "id": f"create_{safe_name}",
            "object": obj_type,
            "operation": "create",
            "name": f"Create {obj_type}",
            "description": f"Create new {obj_type}"
        })
    
    # Update tool
    if discovered_objects[obj_type]["supports"]["update"]:
        all_tools.append({
            "id": f"update_{safe_name}",
            "object": obj_type,
            "operation": "update",
            "name": f"Update {obj_type}",
            "description": f"Update existing {obj_type}"
        })
    
    # Delete tool
    if discovered_objects[obj_type]["supports"]["delete"]:
        all_tools.append({
            "id": f"delete_{safe_name}",
            "object": obj_type,
            "operation": "delete",
            "name": f"Delete {obj_type}",
            "description": f"Delete {obj_type}"
        })
    
    # Function tools
    for func_name in discovered_objects[obj_type].get("functions", []):
        all_tools.append({
            "id": f"{safe_name}_{func_name}",
            "object": obj_type,
            "operation": "function",
            "function": func_name,
            "name": f"{obj_type} - {func_name}",
            "description": f"Execute {func_name} function on {obj_type}"
        })

# Save results
with open('discovered_wapi_objects.json', 'w') as f:
    json.dump(discovered_objects, f, indent=2)

with open('infoblox_mcp_tools.json', 'w') as f:
    json.dump(all_tools, f, indent=2)

print(f"\n✅ Generated {len(all_tools)} tools from {len(discovered_objects)} objects!")

# Print summary
operations = {}
for tool in all_tools:
    op = tool.get("operation", "unknown")
    operations[op] = operations.get(op, 0) + 1

print("\n📊 Tools by operation:")
for op, count in operations.items():
    print(f"  • {op}: {count}")

# Print some interesting objects
print("\n🌟 Some interesting WAPI objects discovered:")
interesting = [
    "network", "ipv4address", "record:host", "record:a", "record:ptr",
    "zone_auth", "range", "fixedaddress", "grid", "member",
    "lease", "dns64group", "dtc:lbdn", "threatanalytics:whitelist"
]
for obj in interesting:
    if obj in discovered_objects:
        info = discovered_objects[obj]
        print(f"  • {obj}: {len(info['fields'])} fields, {len(info['functions'])} functions")

print(f"\n✅ All {len(schema['supported_objects'])} WAPI objects processed!")
