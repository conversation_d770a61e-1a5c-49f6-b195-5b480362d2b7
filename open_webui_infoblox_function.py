"""
InfoBlox Tool Browser Function for Open WebUI
This creates a custom function that can be used within Open WebUI to browse and execute InfoBlox tools
"""

import json
import requests
from typing import Dict, List, Any, Optional

class Filter:
    """Open WebUI Filter for InfoBlox Tool Browser"""
    
    def __init__(self):
        self.infoblox_url = "http://localhost:8000"
        self.tools_cache = None
    
    def load_tools(self):
        """Load tools from InfoBlox MCP server"""
        if self.tools_cache is None:
            try:
                response = requests.get(f"{self.infoblox_url}/tools/tree")
                self.tools_cache = response.json()
            except:
                self.tools_cache = {"error": "Failed to load tools"}
        return self.tools_cache
    
    def inlet(self, body: dict) -> dict:
        """Process incoming messages"""
        messages = body.get("messages", [])
        if messages and messages[-1].get("role") == "user":
            content = messages[-1].get("content", "").lower()
            
            # Check for tool browser commands
            if "browse tools" in content or "tool browser" in content:
                # Inject tool browser HTML
                messages[-1]["content"] = "SHOW_TOOL_BROWSER"
            elif content.startswith("execute tool:"):
                # Parse tool execution request
                messages[-1]["content"] = f"EXECUTE_TOOL:{content[13:]}"
        
        return body
    
    def outlet(self, body: dict) -> dict:
        """Process outgoing messages"""
        messages = body.get("messages", [])
        if messages and messages[-1].get("role") == "assistant":
            content = messages[-1].get("content", "")
            
            if content == "SHOW_TOOL_BROWSER":
                # Replace with tool browser interface
                messages[-1]["content"] = self.generate_tool_browser_html()
            elif content.startswith("EXECUTE_TOOL_RESULT:"):
                # Format tool execution result
                result = json.loads(content[20:])
                messages[-1]["content"] = self.format_tool_result(result)
        
        return body
    
    def generate_tool_browser_html(self) -> str:
        """Generate embedded tool browser HTML"""
        return """
<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 10px 0;">
    <h3>🛠️ InfoBlox Tool Browser</h3>
    <p>Browse and execute any of the 1,300+ InfoBlox tools directly here!</p>
    
    <div style="display: grid; grid-template-columns: 300px 1fr; gap: 20px; margin-top: 20px;">
        <!-- Tool Tree -->
        <div style="background: white; padding: 15px; border-radius: 4px; max-height: 500px; overflow-y: auto;">
            <input type="text" placeholder="Search tools..." 
                   style="width: 100%; padding: 8px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 4px;"
                   onkeyup="filterTools(this.value)">
            <div id="toolTree">
                <!-- Tree will be loaded here -->
            </div>
        </div>
        
        <!-- Tool Details -->
        <div style="background: white; padding: 15px; border-radius: 4px;">
            <div id="toolDetails">
                <p style="color: #666;">Select a tool from the tree to see details and execute it.</p>
            </div>
        </div>
    </div>
</div>

<script>
// Load tools when component mounts
(function() {
    // Fetch tools from InfoBlox MCP
    fetch('http://localhost:8000/tools/tree')
        .then(response => response.json())
        .then(data => {
            renderToolTree(data, document.getElementById('toolTree'));
        });
})();

function renderToolTree(node, container, level = 0) {
    if (node.children) {
        node.children.forEach(child => {
            const nodeDiv = document.createElement('div');
            nodeDiv.style.marginLeft = (level * 20) + 'px';
            nodeDiv.style.cursor = 'pointer';
            nodeDiv.style.padding = '5px';
            nodeDiv.style.borderRadius = '4px';
            
            if (child.children) {
                // Category or operation node
                nodeDiv.innerHTML = `<span style="font-weight: bold;">▶ ${child.name}</span> <span style="color: #666;">(${child.count})</span>`;
                nodeDiv.onclick = () => {
                    const isExpanded = nodeDiv.getAttribute('data-expanded') === 'true';
                    nodeDiv.setAttribute('data-expanded', !isExpanded);
                    const childContainer = nodeDiv.nextElementSibling;
                    childContainer.style.display = isExpanded ? 'none' : 'block';
                    nodeDiv.querySelector('span').textContent = (isExpanded ? '▶' : '▼') + ' ' + child.name;
                };
                
                container.appendChild(nodeDiv);
                
                const childContainer = document.createElement('div');
                childContainer.style.display = 'none';
                renderToolTree(child, childContainer, level + 1);
                container.appendChild(childContainer);
            } else {
                // Tool node
                nodeDiv.innerHTML = `• ${child.name}`;
                nodeDiv.onmouseover = () => nodeDiv.style.background = '#e3f2fd';
                nodeDiv.onmouseout = () => nodeDiv.style.background = '';
                nodeDiv.onclick = () => selectTool(child);
                container.appendChild(nodeDiv);
            }
        });
    }
}

function selectTool(tool) {
    const detailsDiv = document.getElementById('toolDetails');
    let html = `
        <h4>${tool.name}</h4>
        <p style="color: #666; margin: 10px 0;">${tool.description || 'No description'}</p>
        <p><strong>ID:</strong> ${tool.id}</p>
        <p><strong>Object:</strong> ${tool.object}</p>
        
        <h5 style="margin-top: 20px;">Parameters:</h5>
        <form id="toolForm" style="margin-top: 10px;">
    `;
    
    if (tool.parameters && tool.parameters.length > 0) {
        tool.parameters.forEach(param => {
            html += `
                <div style="margin-bottom: 15px;">
                    <label style="display: block; font-weight: 500; margin-bottom: 5px;">
                        ${param.name} ${param.required ? '<span style="color: red;">*</span>' : ''}
                    </label>
                    <input type="${param.type === 'number' ? 'number' : 'text'}" 
                           name="${param.name}" 
                           placeholder="${param.description || ''}"
                           ${param.default ? `value="${param.default}"` : ''}
                           ${param.required ? 'required' : ''}
                           style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
            `;
        });
    } else {
        html += '<p>No parameters required</p>';
    }
    
    html += `
        <button type="button" 
                onclick="executeTool('${tool.id}')"
                style="background: #1976d2; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
            Execute Tool
        </button>
        <div id="toolResult" style="margin-top: 20px;"></div>
        </form>
    `;
    
    detailsDiv.innerHTML = html;
}

function executeTool(toolId) {
    const form = document.getElementById('toolForm');
    const formData = new FormData(form);
    const parameters = {};
    
    for (let [key, value] of formData.entries()) {
        if (value) parameters[key] = value;
    }
    
    // Send execution request through chat
    const message = `execute tool: ${JSON.stringify({tool_id: toolId, parameters: parameters})}`;
    
    // Find the chat input and send the message
    const chatInput = document.querySelector('textarea[placeholder*="Message"]') || 
                     document.querySelector('input[placeholder*="Message"]');
    if (chatInput) {
        chatInput.value = message;
        chatInput.dispatchEvent(new Event('input', { bubbles: true }));
        
        // Find and click send button
        const sendButton = document.querySelector('button[type="submit"]') ||
                          document.querySelector('button:has(svg)');
        if (sendButton) sendButton.click();
    }
}

function filterTools(searchTerm) {
    // Simple filter implementation
    const term = searchTerm.toLowerCase();
    const allNodes = document.querySelectorAll('#toolTree > div');
    allNodes.forEach(node => {
        const text = node.textContent.toLowerCase();
        node.style.display = text.includes(term) ? 'block' : 'none';
    });
}
</script>
"""
    
    def format_tool_result(self, result: Dict[str, Any]) -> str:
        """Format tool execution result"""
        if result.get("success"):
            return f"""
✅ **Tool executed successfully!**

**Result:**
```json
{json.dumps(result.get("data", {}), indent=2)}
```

**Count:** {result.get("count", "N/A")}
"""
        else:
            return f"""
❌ **Tool execution failed**

**Error:** {result.get("error", "Unknown error")}
"""
