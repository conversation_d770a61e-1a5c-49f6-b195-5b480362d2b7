import requests
import json
import urllib3
from typing import Dict, List, Any

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configuration
GRID_MASTER = "https://192.168.1.222/wapi/v2.13.1"
USERNAME = "admin"
PASSWORD = "infoblox"

# Create session
session = requests.Session()
session.auth = (USERNAME, PASSWORD)
session.verify = False

print("🔍 Discovering InfoBlox WAPI Schema...")
print("=" * 50)

# Get WAPI schema documentation
def get_schema_doc():
    """Get the schema documentation URL"""
    try:
        # Try to get schema doc
        response = session.get(f"{GRID_MASTER}/?_schema&_return_type=json-pretty")
        if response.status_code == 200:
            return response.json()
    except:
        pass
    return None

# Get all supported objects
def discover_all_objects():
    """Discover all WAPI object types"""
    all_objects = []
    
    # Standard WAPI objects that should exist
    standard_objects = [
        # Network objects
        "network", "networkview", "networkcontainer", 
        "ipv4address", "ipv6address", "ipv6network", "ipv6networkcontainer",
        
        # DNS objects
        "zone_auth", "zone_delegated", "zone_forward", "zone_stub",
        "record:a", "record:aaaa", "record:alias", "record:caa", "record:cname", 
        "record:dhcid", "record:dname", "record:dnskey", "record:ds",
        "record:host", "record:host_ipv4addr", "record:host_ipv6addr",
        "record:mx", "record:naptr", "record:ns", "record:nsec", "record:nsec3",
        "record:nsec3param", "record:ptr", "record:rrsig", "record:srv", 
        "record:txt", "record:unknown", "record:soa",
        
        # DHCP objects
        "range", "sharednetwork", "fixedaddress", "lease",
        "dhcpfailover", "dhcpoptionspace", "dhcpoption",
        
        # IPAM objects
        "vlan", "vlanrange", "vlanview",
        "ipv4address", "ipv6address", "macfilteraddress",
        
        # Grid/Member objects
        "grid", "member", "discovery:memberproperties",
        "msserver", "msserver:adsites:domain", "msserver:adsites:site",
        
        # Security objects
        "admingroup", "adminrole", "adminuser",
        "certificate:authservice", "saml:authservice",
        
        # DNS Security
        "dnsseczone", "dnsseckey", "dnssectrustedkey",
        "response_policy", "response_policy_rule", "response_policy_zone",
        
        # Other objects
        "csvimporttask", "scheduledtask", "approval",
        "notification:rule", "notification:rest:endpoint",
        "outbound:cloudclient", "threatprotection:profile",
        "hsm:allgroups", "hsm:thalesgroup",
        
        # Reporting objects
        "search", "request", "allrecords",
        
        # Extensible attributes
        "extensibleattributedef",
        
        # Discovery objects
        "discovery:device", "discovery:deviceneighbor", "discovery:devicenetwork",
        "discovery:vrf", "discovery:status",
        
        # Grid Cloud objects
        "grid:cloudapi", "grid:cloudapi:cloudstatistics",
        "grid:cloudapi:tenant", "grid:cloudapi:vm", "grid:cloudapi:vmaddress",
        
        # DNS Traffic Control
        "dtc:lbdn", "dtc:pool", "dtc:server", "dtc:monitor",
        
        # Threat Analytics
        "threatanalytics:analytics_whitelist", "threatanalytics:whitelist"
    ]
    
    discovered = {}
    
    # Test each object type
    for obj_type in standard_objects:
        try:
            # Try to get schema for this object
            response = session.get(f"{GRID_MASTER}/{obj_type}?_schema&_return_type=json-pretty")
            if response.status_code == 200:
                schema = response.json()
                discovered[obj_type] = {
                    "exists": True,
                    "schema": schema,
                    "supports": {
                        "create": schema.get("supports_create", False),
                        "update": schema.get("supports_update", False),
                        "delete": schema.get("supports_delete", False),
                        "read": True,
                        "search": True
                    },
                    "fields": list(schema.get("fields", {}).keys()),
                    "functions": list(schema.get("functions", {}).keys()) if schema.get("functions") else []
                }
                print(f"✅ {obj_type} - {len(discovered[obj_type]['fields'])} fields, {len(discovered[obj_type]['functions'])} functions")
            else:
                # Try to list to see if it exists
                list_response = session.get(f"{GRID_MASTER}/{obj_type}?_max_results=1")
                if list_response.status_code == 200:
                    discovered[obj_type] = {
                        "exists": True,
                        "schema": None,
                        "supports": {
                            "create": True,
                            "update": True,
                            "delete": True,
                            "read": True,
                            "search": True
                        }
                    }
                    print(f"✅ {obj_type} - exists (no schema)")
                    
        except Exception as e:
            # Object doesn't exist or error
            pass
    
    return discovered

# Get actual schema if available
schema_doc = get_schema_doc()
if schema_doc:
    print("\n📄 Schema documentation available!")
    with open('wapi_schema.json', 'w') as f:
        json.dump(schema_doc, f, indent=2)
    print("Saved to wapi_schema.json")

# Discover all objects
print("\n🔍 Discovering all WAPI objects...")
discovered_objects = discover_all_objects()

print(f"\n📊 Summary:")
print(f"Total objects discovered: {len(discovered_objects)}")

# Group by category
categories = {
    "Network": [],
    "DNS Records": [],
    "DNS Zones": [],
    "DHCP": [],
    "IPAM": [],
    "Security": [],
    "Discovery": [],
    "Grid": [],
    "Other": []
}

for obj_name in discovered_objects:
    if "network" in obj_name and not "discovery" in obj_name:
        categories["Network"].append(obj_name)
    elif "record:" in obj_name:
        categories["DNS Records"].append(obj_name)
    elif "zone" in obj_name:
        categories["DNS Zones"].append(obj_name)
    elif any(x in obj_name for x in ["dhcp", "range", "lease", "fixed"]):
        categories["DHCP"].append(obj_name)
    elif any(x in obj_name for x in ["ipv4", "ipv6", "vlan", "mac"]):
        categories["IPAM"].append(obj_name)
    elif any(x in obj_name for x in ["admin", "saml", "certificate"]):
        categories["Security"].append(obj_name)
    elif "discovery" in obj_name:
        categories["Discovery"].append(obj_name)
    elif any(x in obj_name for x in ["grid", "member", "ms"]):
        categories["Grid"].append(obj_name)
    else:
        categories["Other"].append(obj_name)

print("\n📂 Objects by Category:")
for category, objects in categories.items():
    if objects:
        print(f"\n{category}: {len(objects)} objects")
        for obj in sorted(objects)[:5]:
            obj_info = discovered_objects[obj]
            funcs = obj_info.get('functions', [])
            if funcs:
                print(f"  • {obj} (functions: {', '.join(funcs[:3])}{'...' if len(funcs) > 3 else ''})")
            else:
                print(f"  • {obj}")
        if len(objects) > 5:
            print(f"  ... and {len(objects) - 5} more")

# Save discovered objects
with open('discovered_wapi_objects.json', 'w') as f:
    json.dump(discovered_objects, f, indent=2)

print("\n✅ Discovery complete! Saved to discovered_wapi_objects.json")

# Generate MCP tools for all discovered objects
print("\n🔧 Generating MCP tool definitions...")

tools = []
tool_count = 0

for obj_type, obj_info in discovered_objects.items():
    if not obj_info.get("exists"):
        continue
    
    safe_name = obj_type.replace(":", "_").replace(".", "_")
    
    # List/Search tool
    tools.append({
        "id": f"list_{safe_name}",
        "object": obj_type,
        "operation": "list",
        "name": f"List {obj_type}",
        "description": f"List/search {obj_type} objects with filters"
    })
    tool_count += 1
    
    # Get tool
    tools.append({
        "id": f"get_{safe_name}",
        "object": obj_type,
        "operation": "get",
        "name": f"Get {obj_type}",
        "description": f"Get specific {obj_type} by reference"
    })
    tool_count += 1
    
    # Create tool (if supported)
    if obj_info["supports"].get("create"):
        tools.append({
            "id": f"create_{safe_name}",
            "object": obj_type,
            "operation": "create",
            "name": f"Create {obj_type}",
            "description": f"Create new {obj_type}"
        })
        tool_count += 1
    
    # Update tool (if supported)
    if obj_info["supports"].get("update"):
        tools.append({
            "id": f"update_{safe_name}",
            "object": obj_type,
            "operation": "update",
            "name": f"Update {obj_type}",
            "description": f"Update existing {obj_type}"
        })
        tool_count += 1
    
    # Delete tool (if supported)
    if obj_info["supports"].get("delete"):
        tools.append({
            "id": f"delete_{safe_name}",
            "object": obj_type,
            "operation": "delete",
            "name": f"Delete {obj_type}",
            "description": f"Delete {obj_type}"
        })
        tool_count += 1
    
    # Function tools
    for func_name in obj_info.get("functions", []):
        tools.append({
            "id": f"{safe_name}_{func_name}",
            "object": obj_type,
            "operation": "function",
            "function": func_name,
            "name": f"{obj_type} - {func_name}",
            "description": f"Execute {func_name} function on {obj_type}"
        })
        tool_count += 1

print(f"\n✅ Generated {tool_count} MCP tools from {len(discovered_objects)} objects!")

# Save tools
with open('infoblox_mcp_tools.json', 'w') as f:
    json.dump(tools, f, indent=2)

print("\nSaved tool definitions to infoblox_mcp_tools.json")
print("\nNext: Build MCP server with all these tools...")
