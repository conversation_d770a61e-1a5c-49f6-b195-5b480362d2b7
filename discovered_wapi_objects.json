{"ad_auth_service": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "admingroup": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "adminrole": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "adminuser": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "allendpoints": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "allnsgroup": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "allrecords": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "allrpzrecords": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "approvalworkflow": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "authpolicy": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "awsrte53taskgroup": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "awsuser": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "bfdtemplate": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "bulkhost": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "bulkhostnametemplate": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "cacertificate": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "capacityreport": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "captiveportal": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "certificate:authservice": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "csvimporttask": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "datacollectioncluster": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "db_objects": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dbsnapshot": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ddns:principalcluster": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ddns:principalcluster:group": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "deleted_objects": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dhcp:statistics": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dhcpfailover": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dhcpoptiondefinition": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dhcpoptionspace": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery:credentialgroup": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery:device": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery:devicecomponent": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery:deviceinterface": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery:deviceneighbor": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery:devicesupportbundle": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery:diagnostictask": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery:gridproperties": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery:memberproperties": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery:sdnnetwork": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery:status": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discovery:vrf": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "discoverytask": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "distributionschedule": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dns64group": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:allrecords": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:certificate": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:lbdn": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:monitor": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:monitor:http": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:monitor:icmp": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:monitor:pdp": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:monitor:sip": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:monitor:snmp": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:monitor:tcp": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:object": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:pool": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:record:a": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:record:aaaa": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:record:cname": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:record:naptr": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:record:srv": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:server": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:topology": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:topology:label": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dtc:topology:rule": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "dxl:endpoint": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "extensibleattributedef": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "fileop": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "filterfingerprint": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "filtermac": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "filternac": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "filteroption": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "filterrelayagent": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "fingerprint": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "fixedaddress": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "fixedaddresstemplate": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ftpuser": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "gmcgroup": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "gmcschedule": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:cloudapi": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:cloudapi:cloudstatistics": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:cloudapi:tenant": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:cloudapi:vm": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:cloudapi:vmaddress": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:dashboard": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:dhcpproperties": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:dns": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:filedistribution": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:license_pool": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:license_pool_container": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:maxminddbinfo": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:member:cloudapi": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:servicerestart:group": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:servicerestart:group:order": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:servicerestart:request": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:servicerestart:request:changedobject": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:servicerestart:status": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:threatanalytics": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:threatprotection": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "grid:x509certificate": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "hostnamerewritepolicy": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "hsm:allgroups": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "hsm:entrustnshieldgroup": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "hsm:thaleslunagroup": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipam:statistics": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv4address": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv6address": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv6dhcpoptiondefinition": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv6dhcpoptionspace": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv6filteroption": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv6fixedaddress": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv6fixedaddresstemplate": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv6network": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv6networkcontainer": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv6networktemplate": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv6range": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv6rangetemplate": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ipv6sharednetwork": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "kerberoskey": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ldap_auth_service": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "lease": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "license:gridwide": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "localuser:authservice": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "macfilteraddress": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "mastergrid": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "member": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "member:dhcpproperties": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "member:dns": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "member:filedistribution": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "member:license": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "member:parentalcontrol": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "member:threatanalytics": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "member:threatprotection": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "memberclouddnssync": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "memberdfp": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "msserver": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "msserver:adsites:domain": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "msserver:adsites:site": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "msserver:dhcp": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "msserver:dns": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "mssuperscope": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "namedacl": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "natgroup": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "network": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "network_discovery": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "networkcontainer": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "networktemplate": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "networkuser": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "networkview": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "notification:rest:endpoint": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "notification:rest:template": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "notification:rule": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "nsgroup": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "nsgroup:delegation": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "nsgroup:forwardingmember": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "nsgroup:forwardstubserver": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "nsgroup:stubmember": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "orderedranges": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "orderedresponsepolicyzones": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "outbound:cloudclient": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "parentalcontrol:avp": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "parentalcontrol:blockingpolicy": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "parentalcontrol:subscriber": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "parentalcontrol:subscriberrecord": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "parentalcontrol:subscribersite": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "permission": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "pxgrid:endpoint": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "radius:authservice": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "range": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "rangetemplate": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:a": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:aaaa": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:alias": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:caa": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:cname": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:dhcid": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:dname": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:dnskey": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:ds": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:dtclbdn": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:host": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:host_ipv4addr": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:host_ipv6addr": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:mx": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:naptr": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:ns": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:nsec": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:nsec3": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:nsec3param": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:ptr": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:a": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:a:ipaddress": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:aaaa": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:aaaa:ipaddress": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:cname": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:cname:clientipaddress": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:cname:clientipaddressdn": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:cname:ipaddress": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:cname:ipaddressdn": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:mx": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:naptr": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:ptr": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:srv": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rpz:txt": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:rrsig": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:srv": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:tlsa": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:txt": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "record:unknown": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "recordnamepolicy": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "request": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "restartservicestatus": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "rir": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "rir:organization": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "roaminghost": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "ruleset": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "saml:authservice": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "scavengingtask": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "scheduledtask": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "search": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "sharednetwork": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "sharedrecord:a": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "sharedrecord:aaaa": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "sharedrecord:cname": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "sharedrecord:mx": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "sharedrecord:srv": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "sharedrecord:txt": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "sharedrecordgroup": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "smartfolder:children": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "smartfolder:global": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "smartfolder:personal": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "snmpuser": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "superhost": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "superhostchild": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "syslog:endpoint": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "tacacsplus:authservice": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "taxii": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "tftpfiledir": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "threatanalytics:analytics_whitelist": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "threatanalytics:moduleset": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "threatanalytics:whitelist": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "threatinsight:cloudclient": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "threatprotection:grid:rule": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "threatprotection:profile": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "threatprotection:profile:rule": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "threatprotection:rule": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "threatprotection:rulecategory": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "threatprotection:ruleset": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "threatprotection:ruletemplate": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "threatprotection:statistics": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "upgradegroup": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "upgradeschedule": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "upgradestatus": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "userprofile": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "vdiscoverytask": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "view": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "vlan": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "vlanrange": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "vlanview": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "zone_auth": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "zone_auth_discrepancy": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "zone_delegated": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "zone_forward": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "zone_rp": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}, "zone_stub": {"exists": true, "fields": [], "functions": [], "supports": {"create": true, "update": true, "delete": true, "search": true}}}