# Adding InfoBlox Tool Browser to Open WebUI Suggested Prompts

## Method 1: Using Prompts Feature in Open WebUI

1. **In Open WebUI, go to:**
   - Workspace → Prompts
   - Or Settings → Interface → Prompts

2. **Create New Prompts:**
   Click "Create New" and add these prompts:

### Prompt 1: Browse InfoBlox Tools
- **Title:** Browse InfoBlox Tools
- **Content:** `browse tools`
- **Description:** View all 1,345 InfoBlox tools organized by category
- **Category:** InfoBlox

### Prompt 2: Search InfoBlox Tools
- **Title:** Search Tools
- **Content:** `search tools: [TERM]`
- **Description:** Search for specific InfoBlox tools (replace [TERM] with network, dns, dhcp, etc.)
- **Category:** InfoBlox

### Prompt 3: List Networks
- **Title:** List All Networks
- **Content:** `execute tool: {"tool_id": "list_network", "parameters": {"_max_results": 25}}`
- **Description:** List all networks in InfoBlox
- **Category:** InfoBlox

### Prompt 4: Test InfoBlox Connection
- **Title:** Test Connection
- **Content:** `test connection`
- **Description:** Verify connection to InfoBlox Grid
- **Category:** InfoBlox

### Prompt 5: Create Network
- **Title:** Create Network
- **Content:** `search tools: create network`
- **Description:** Find network creation tools
- **Category:** InfoBlox

## Method 2: Using Model System Prompt

Add this to your model's system prompt in Open WebUI:

```
## Suggested Commands:
When the user starts a conversation, suggest these helpful commands:
- "browse tools" - View all 1,345 InfoBlox tools
- "search tools: network" - Search for network tools
- "search tools: dns" - Search for DNS tools
- "test connection" - Test InfoBlox connectivity
- "list all networks" - Show all networks
```

## Method 3: Custom Welcome Message

Set a custom welcome message in Open WebUI settings:

```
Welcome to InfoBlox Assistant! 

Try these commands:
• browse tools - View all available tools
• search tools: <term> - Find specific tools
• test connection - Verify InfoBlox connection
• list all networks - Show network inventory

Type 'help' for more options.
```

## Method 4: Using Open WebUI's models.json

Edit your Open WebUI configuration to include default prompts:

```json
{
  "models": {
    "infoblox-assistant": {
      "name": "InfoBlox Assistant",
      "base_url": "http://localhost:8000/v1",
      "api_key": "dummy",
      "default_prompts": [
        {
          "title": "Browse Tools",
          "prompt": "browse tools"
        },
        {
          "title": "Search Network Tools",
          "prompt": "search tools: network"
        },
        {
          "title": "List Networks",
          "prompt": "execute tool: {\"tool_id\": \"list_network\", \"parameters\": {}}"
        },
        {
          "title": "Test Connection",
          "prompt": "test connection"
        }
      ]
    }
  }
}
```

## Method 5: Quick Actions in UI

If using Open WebUI's latest version, you can add quick actions:

1. Go to Settings → Interface
2. Find "Quick Actions" or "Suggested Prompts"
3. Add these entries:

```
Browse InfoBlox Tools|browse tools
Search Network Tools|search tools: network
Search DNS Tools|search tools: dns
List All Networks|list all networks json
Test Connection|test connection
Find DHCP Tools|search tools: dhcp
Create Network|search tools: create network
```

## Recommended Approach

Use **Method 1** (Prompts Feature) as it's the most user-friendly and doesn't require configuration file changes.
