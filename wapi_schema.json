{"requested_version": "2.13.1", "supported_objects": ["ad_auth_service", "admingroup", "adminrole", "adminuser", "allendpoints", "allnsgroup", "allrecords", "allrpzrecords", "approvalworkflow", "authpolicy", "awsrte53taskgroup", "awsuser", "bfdtemplate", "bulkhost", "bulkhostnametemplate", "cacertificate", "capacityreport", "captiveportal", "certificate:authservice", "csvimporttask", "datacollectioncluster", "db_objects", "dbsnapshot", "ddns:principalcluster", "ddns:principalcluster:group", "deleted_objects", "dhcp:statistics", "dhc<PERSON><PERSON><PERSON>", "dhcpoptiondefinition", "dhcpoptionspace", "discovery", "discovery:credentialgroup", "discovery:device", "discovery:devicecomponent", "discovery:deviceinterface", "discovery:<PERSON><PERSON><PERSON><PERSON>", "discovery:devicesupportbundle", "discovery:diagnostictask", "discovery:gridproperties", "discovery:memberproperties", "discovery:sdnnetwork", "discovery:status", "discovery:vrf", "discoverytask", "distributionschedule", "dns64group", "dtc", "dtc:allrecords", "dtc:certificate", "dtc:lbdn", "dtc:monitor", "dtc:monitor:http", "dtc:monitor:icmp", "dtc:monitor:pdp", "dtc:monitor:sip", "dtc:monitor:snmp", "dtc:monitor:tcp", "dtc:object", "dtc:pool", "dtc:record:a", "dtc:record:aaaa", "dtc:record:cname", "dtc:record:naptr", "dtc:record:srv", "dtc:server", "dtc:topology", "dtc:topology:label", "dtc:topology:rule", "dxl:endpoint", "extensibleattributedef", "fileop", "filterfingerprint", "filtermac", "filternac", "filteroption", "filterrelayagent", "fingerprint", "fixedaddress", "fixedaddresstemplate", "ftpuser", "gmcgroup", "gmcschedule", "grid", "grid:cloudapi", "grid:cloudapi:cloudstatistics", "grid:cloudapi:tenant", "grid:cloudapi:vm", "grid:cloudapi:vmaddress", "grid:dashboard", "grid:dhcpproperties", "grid:dns", "grid:filedistribution", "grid:license_pool", "grid:license_pool_container", "grid:maxminddbinfo", "grid:member:cloudapi", "grid:servicerestart:group", "grid:servicerestart:group:order", "grid:servicerestart:request", "grid:servicerestart:request:changedobject", "grid:servicerestart:status", "grid:threatanalytics", "grid:threatprotection", "grid:x509certificate", "hostnamerewritepolicy", "hsm:allgroups", "hsm:entrustnshieldgroup", "hsm:thaleslunagroup", "ipam:statistics", "ipv4address", "ipv6address", "ipv6dhcpoptiondefinition", "ipv6dhcpoptionspace", "ipv6filteroption", "ipv6fixedaddress", "ipv6fixedaddresstemplate", "ipv6network", "ipv6networkcontainer", "ipv6networktemplate", "ipv6range", "ipv6rangetemplate", "ipv6sharednetwork", "<PERSON>er<PERSON><PERSON><PERSON>", "ldap_auth_service", "lease", "license:gridwide", "localuser:authservice", "macfilteraddress", "mastergrid", "member", "member:dhcpproperties", "member:dns", "member:filedistribution", "member:license", "member:parentalcontrol", "member:threatanalytics", "member:threatprotection", "memberclouddnssync", "memberdfp", "msserver", "msserver:adsites:domain", "msserver:adsites:site", "msserver:dhcp", "msserver:dns", "mssuperscope", "<PERSON><PERSON><PERSON>", "natgroup", "network", "network_discovery", "networkcontainer", "networktemplate", "networkuser", "networkview", "notification:rest:endpoint", "notification:rest:template", "notification:rule", "nsgroup", "nsgroup:delegation", "nsgroup:forwardingmember", "nsgroup:forwardstubserver", "nsgroup:stubmember", "orderedranges", "orderedresponsepolicyzones", "outbound:cloudclient", "parentalcontrol:avp", "parentalcontrol:blockingpolicy", "parentalcontrol:subscriber", "parentalcontrol:subscriberrecord", "parentalcontrol:subscribersite", "permission", "pxgrid:endpoint", "radius:authservice", "range", "rangetemplate", "record:a", "record:aaaa", "record:alias", "record:caa", "record:cname", "record:dhcid", "record:dname", "record:dns<PERSON>", "record:ds", "record:dtclbdn", "record:host", "record:host_ipv4addr", "record:host_ipv6addr", "record:mx", "record:naptr", "record:ns", "record:nsec", "record:nsec3", "record:nsec3param", "record:ptr", "record:rpz:a", "record:rpz:a:ipaddress", "record:rpz:aaaa", "record:rpz:aaaa:ipad<PERSON>", "record:rpz:cname", "record:rpz:cname:clientipaddress", "record:rpz:cname:clientipaddressdn", "record:rpz:cname:ipaddress", "record:rpz:cname:ipaddressdn", "record:rpz:mx", "record:rpz:naptr", "record:rpz:ptr", "record:rpz:srv", "record:rpz:txt", "record:rrsig", "record:srv", "record:tlsa", "record:txt", "record:unknown", "recordnamepolicy", "request", "restartservicestatus", "rir", "rir:organization", "roaminghost", "ruleset", "saml:authservice", "scavengingtask", "scheduledtask", "search", "sharednetwork", "sharedrecord:a", "sharedrecord:aaaa", "sharedrecord:cname", "sharedrecord:mx", "sharedrecord:srv", "sharedrecord:txt", "sharedrecordgroup", "smartfolder:children", "smartfolder:global", "smartfolder:personal", "snmpuser", "superhost", "superhostchild", "syslog:endpoint", "tacacsplus:authservice", "taxii", "tftpfiledir", "threatanalytics:analytics_whitelist", "threatanalytics:moduleset", "threatanalytics:whitelist", "threatinsight:cloudclient", "threatprotection:grid:rule", "threatprotection:profile", "threatprotection:profile:rule", "threatprotection:rule", "threatprotection:rulecategory", "threatprotection:ruleset", "threatprotection:ruletemplate", "threatprotection:statistics", "upgradegroup", "upgradeschedule", "upgradestatus", "userprofile", "vdiscoverytask", "view", "vlan", "vlanrange", "vlanview", "zone_auth", "zone_auth_discrepancy", "zone_delegated", "zone_forward", "zone_rp", "zone_stub"], "supported_versions": ["1.0", "1.1", "1.2", "1.2.1", "1.3", "1.4", "1.4.1", "1.4.2", "1.5", "1.6", "1.6.1", "1.7", "1.7.1", "1.7.2", "1.7.3", "1.7.4", "1.7.5", "2.0", "2.1", "2.1.1", "2.1.2", "2.10", "2.10.1", "2.10.2", "2.10.3", "2.10.5", "2.11", "2.11.1", "2.11.2", "2.11.3", "2.11.5", "2.12", "2.12.1", "2.12.2", "2.12.3", "2.13", "2.13.1", "2.2", "2.2.1", "2.2.2", "2.3", "2.3.1", "2.4", "2.5", "2.6", "2.6.1", "2.7", "2.7.1", "2.7.2", "2.7.3", "2.8", "2.9", "2.9.1", "2.9.5", "2.9.7"]}