# InfoBlox MCP + Open WebUI Deployment Guide

This guide provides step-by-step instructions for deploying the InfoBlox MCP integration with Open WebUI.

## 📋 Prerequisites

### System Requirements

- **Operating System**: Linux, macOS, or Windows with WSL2
- **Docker**: Version 20.10 or higher
- **Docker Compose**: Version 2.0 or higher
- **Memory**: Minimum 4GB RAM, recommended 8GB+
- **Storage**: Minimum 10GB free space
- **Network**: Access to InfoBlox Grid Master

### InfoBlox Requirements

- **Grid Master**: Accessible via HTTPS
- **WAPI Version**: 2.13.1 or compatible
- **Credentials**: Admin or API user account
- **Network Access**: Ports 443/80 to Grid Master
- **SSL Certificate**: Valid or self-signed accepted

## 🚀 Installation Steps

### Step 1: Environment Setup

1. **Clone Repository**:
   ```bash
   git clone <repository-url>
   cd infoblox-real-mcp
   ```

2. **Run Setup Script**:
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

3. **Configure Environment**:
   ```bash
   cp .env.example .env
   nano .env  # Edit with your settings
   ```

### Step 2: Environment Configuration

Edit `.env` file with your InfoBlox details:

```env
# InfoBlox Configuration
GRID_MASTER_URL=https://*************/wapi/v2.13.1
INFOBLOX_USERNAME=admin
INFOBLOX_PASSWORD=your-secure-password
NETWORK_VIEW=default

# Open WebUI Configuration
WEBUI_SECRET_KEY=generate-a-secure-key-here
WEBUI_NAME=InfoBlox Assistant
WEBUI_URL=http://localhost:3000

# Security Settings
ENABLE_SIGNUP=true
ENABLE_LOGIN_FORM=true
DEFAULT_USER_ROLE=user

# Features
ENABLE_RAG_HYBRID_SEARCH=false
ENABLE_IMAGE_GENERATION=false
ENABLE_COMMUNITY_SHARING=false
```

### Step 3: Deploy Services

1. **Development Deployment**:
   ```bash
   docker-compose up -d
   ```

2. **Production Deployment**:
   ```bash
   # First, add SSL certificates to nginx/ssl/
   docker-compose --profile production up -d
   ```

3. **Verify Deployment**:
   ```bash
   docker-compose ps
   docker-compose logs -f
   ```

### Step 4: Configure Open WebUI

1. **Access Open WebUI**:
   - Open browser to http://localhost:3000
   - Create admin account if first time

2. **Add InfoBlox Model**:
   - Go to Settings → Connections
   - Click "Add Connection"
   - Select "OpenAI API"
   - Configure:
     - **Name**: InfoBlox Assistant
     - **API Base URL**: `http://infoblox-mcp:8000/v1`
     - **API Key**: `dummy-key`
   - Save and refresh browser

3. **Verify Model**:
   - Check model dropdown for "infoblox-assistant"
   - Test with: "test connection"

## 🎨 Customization

### UI Branding

1. **Custom Logo**:
   ```bash
   # Add your logo to static/
   cp your-logo.png static/infoblox-logo.png
   ```

2. **Custom Colors**:
   Edit `static/infoblox-theme.css`:
   ```css
   :root {
     --infoblox-primary: #your-color;
     --infoblox-secondary: #your-color;
   }
   ```

3. **Custom Favicon**:
   ```bash
   cp your-favicon.ico static/favicon-infoblox.ico
   ```

### Function Integration

1. **Add Custom Functions**:
   - Copy `open-webui-config/functions.py`
   - Go to Open WebUI → Workspace → Functions
   - Create new function and paste code

2. **Configure Tools**:
   - Import `open-webui-config/infoblox_tools.py`
   - Test functions in chat interface

## 🔒 Security Configuration

### SSL/TLS Setup

1. **Generate Certificates**:
   ```bash
   # Self-signed certificate
   openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
     -keyout nginx/ssl/server.key \
     -out nginx/ssl/server.crt
   ```

2. **Production Certificates**:
   ```bash
   # Copy your certificates
   cp your-domain.crt nginx/ssl/
   cp your-domain.key nginx/ssl/
   ```

3. **Enable SSL**:
   ```bash
   docker-compose --profile production up -d
   ```

### Authentication

1. **Enable User Registration**:
   ```env
   ENABLE_SIGNUP=true
   ENABLE_LOGIN_FORM=true
   ```

2. **LDAP Integration** (Optional):
   Add to docker-compose.yml:
   ```yaml
   environment:
     - LDAP_SERVER_URL=ldap://your-ldap-server
     - LDAP_BIND_DN=cn=admin,dc=company,dc=com
   ```

### Network Security

1. **Firewall Rules**:
   ```bash
   # Allow only necessary ports
   ufw allow 3000/tcp  # Open WebUI
   ufw allow 8000/tcp  # InfoBlox MCP (internal only)
   ufw allow 443/tcp   # HTTPS (production)
   ```

2. **Container Network**:
   - Services communicate via internal network
   - InfoBlox MCP not exposed externally
   - SSL termination at nginx proxy

## 📊 Monitoring and Maintenance

### Health Checks

1. **Service Health**:
   ```bash
   # Check all services
   docker-compose ps
   
   # Check specific service
   curl http://localhost:8000/health
   curl http://localhost:3000/health
   ```

2. **InfoBlox Connection**:
   ```bash
   # Test InfoBlox connectivity
   curl -X POST http://localhost:8000/v1/chat/completions \
     -H "Content-Type: application/json" \
     -d '{"messages": [{"role": "user", "content": "test connection"}]}'
   ```

### Logging

1. **View Logs**:
   ```bash
   # All services
   docker-compose logs -f
   
   # Specific service
   docker-compose logs -f infoblox-mcp
   docker-compose logs -f open-webui
   ```

2. **Log Rotation**:
   Configure in docker-compose.yml:
   ```yaml
   logging:
     driver: "json-file"
     options:
       max-size: "10m"
       max-file: "3"
   ```

### Backup and Recovery

1. **Backup Data**:
   ```bash
   # Backup Open WebUI data
   docker run --rm \
     -v infoblox-real-mcp_open-webui-data:/data \
     -v $(pwd):/backup \
     alpine tar czf /backup/openwebui-backup.tar.gz -C /data .
   
   # Backup configuration
   tar czf config-backup.tar.gz .env open-webui-config/ static/
   ```

2. **Restore Data**:
   ```bash
   # Restore Open WebUI data
   docker run --rm \
     -v infoblox-real-mcp_open-webui-data:/data \
     -v $(pwd):/backup \
     alpine tar xzf /backup/openwebui-backup.tar.gz -C /data
   ```

## 🔧 Troubleshooting

### Common Issues

1. **Model Not Appearing**:
   ```bash
   # Check MCP server
   curl http://localhost:8000/v1/models
   
   # Check Open WebUI logs
   docker-compose logs open-webui
   
   # Restart services
   docker-compose restart
   ```

2. **Connection Errors**:
   ```bash
   # Test InfoBlox connectivity
   curl -k https://your-infoblox-ip/wapi/v2.13.1/grid
   
   # Check environment variables
   docker-compose exec infoblox-mcp env | grep INFOBLOX
   ```

3. **UI Issues**:
   - Clear browser cache
   - Check browser console for errors
   - Verify static file mounting

### Performance Optimization

1. **Resource Limits**:
   ```yaml
   services:
     infoblox-mcp:
       deploy:
         resources:
           limits:
             memory: 1G
             cpus: '0.5'
   ```

2. **Caching**:
   - Enable nginx caching for static files
   - Configure Open WebUI caching
   - Use Redis for session storage

## 📈 Scaling

### Horizontal Scaling

1. **Load Balancer**:
   ```yaml
   services:
     nginx:
       image: nginx:alpine
       volumes:
         - ./nginx/nginx-lb.conf:/etc/nginx/nginx.conf
   ```

2. **Multiple MCP Instances**:
   ```yaml
   services:
     infoblox-mcp-1:
       # ... configuration
     infoblox-mcp-2:
       # ... configuration
   ```

### Monitoring

1. **Prometheus Metrics**:
   - Add metrics endpoints
   - Configure Grafana dashboards
   - Set up alerting rules

2. **Health Monitoring**:
   - Implement health check endpoints
   - Configure uptime monitoring
   - Set up notification systems

## 🎯 Best Practices

1. **Security**:
   - Use strong passwords
   - Enable SSL/TLS
   - Regular security updates
   - Network segmentation

2. **Performance**:
   - Monitor resource usage
   - Optimize container images
   - Use caching strategies
   - Regular maintenance

3. **Reliability**:
   - Implement health checks
   - Set up monitoring
   - Regular backups
   - Disaster recovery plan

---

For additional support, refer to the main README.md or open an issue on GitHub.
