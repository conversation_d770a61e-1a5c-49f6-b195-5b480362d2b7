#!/bin/bash
# Fix InfoBlox model visibility in Open WebUI

echo "Checking InfoBlox MCP endpoints..."

# Test all required OpenAI endpoints
echo -e "\n1. Models endpoint:"
curl -s http://localhost:8000/v1/models | jq .

echo -e "\n2. Testing chat completion:"
curl -s -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model": "infoblox-assistant", "messages": [{"role": "user", "content": "test"}]}' | jq -c '.choices[0].message.content' | head -c 100

echo -e "\n\n3. Configuration for Open WebUI:"
echo "================================"
echo "In Open WebUI (http://localhost:3000):"
echo "1. Go to Settings → Connections"
echo "2. Click '+ New Connection'"
echo "3. Select 'OpenAI' as the API type"
echo "4. Enter these details:"
echo "   - Name: InfoBlox Assistant"
echo "   - API Base URL: http://host.docker.internal:8000/v1"
echo "   - API Key: dummy-key-12345"
echo "5. Click 'Save'"
echo "6. Refresh the page"
echo "7. The model 'infoblox-assistant' should appear in the dropdown"
echo ""
echo "Note: Use 'host.docker.internal' instead of 'localhost' because Open WebUI is in Docker"
