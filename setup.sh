#!/bin/bash

# InfoBlox MCP + Open WebUI Setup Script
set -e

echo "🚀 InfoBlox MCP + Open WebUI Setup"
echo "=================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p config logs static open-webui-config nginx/ssl

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating environment file..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your InfoBlox credentials"
fi

# Create Open WebUI configuration directory
echo "⚙️  Setting up Open WebUI configuration..."
mkdir -p open-webui-config

# Create custom CSS for InfoBlox branding
cat > static/custom.css << 'EOF'
/* InfoBlox Custom Styling */
:root {
  --primary-color: #0066cc;
  --secondary-color: #004499;
  --accent-color: #00aaff;
  --background-color: #f8f9fa;
  --text-color: #333333;
}

.navbar-brand {
  color: var(--primary-color) !important;
  font-weight: bold;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.sidebar {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.chat-message.assistant {
  background-color: #e3f2fd;
  border-left: 4px solid var(--primary-color);
}

.model-selector {
  border: 2px solid var(--primary-color);
  border-radius: 8px;
}
EOF

# Create favicon
echo "🎨 Setting up branding assets..."
# You can replace this with actual InfoBlox favicon
touch static/favicon-infoblox.ico

# Create nginx configuration for production
cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream open-webui {
        server open-webui:8080;
    }
    
    upstream infoblox-mcp {
        server infoblox-mcp:8000;
    }

    server {
        listen 80;
        server_name localhost;

        # Redirect to HTTPS in production
        # return 301 https://$server_name$request_uri;

        location / {
            proxy_pass http://open-webui;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/mcp/ {
            proxy_pass http://infoblox-mcp/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
EOF

echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your InfoBlox credentials"
echo "2. Run: docker-compose up -d"
echo "3. Access Open WebUI at: http://localhost:3000"
echo "4. Configure InfoBlox model in Open WebUI settings"
echo ""
echo "For production with SSL, run: docker-compose --profile production up -d"
