import requests
import json
import urllib3
from typing import Dict, List, Tuple
import time

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configuration
GRID_MASTER = "https://192.168.1.222/wapi/v2.13.1"
USERNAME = "admin"
PASSWORD = "infoblox"

# Create session
session = requests.Session()
session.auth = (USERNAME, PASSWORD)
session.verify = False

# Load tools
with open('infoblox_mcp_tools.json', 'r') as f:
    all_tools = json.load(f)

print(f"🔍 Testing {len(all_tools)} tools for availability...\n")

# Group tools by object type
tools_by_object = {}
for tool in all_tools:
    obj_type = tool.get('object', '')
    if obj_type not in tools_by_object:
        tools_by_object[obj_type] = []
    tools_by_object[obj_type].append(tool)

# Test each object type
working_objects = []
failing_objects = []
discovery_objects = []
results = {}

print("Testing object types...")
for obj_type, tools in sorted(tools_by_object.items()):
    # Only test list operation for each object type
    list_tool = next((t for t in tools if t['operation'] == 'list'), None)
    if not list_tool:
        continue
    
    try:
        # Test with minimal parameters
        response = session.get(
            f"{GRID_MASTER}/{obj_type}",
            params={"_max_results": 1}
        )
        
        if response.status_code == 200:
            working_objects.append(obj_type)
            results[obj_type] = {"status": "working", "count": len(response.json())}
            print(f"✅ {obj_type}")
        elif "discovery service" in response.text:
            discovery_objects.append(obj_type)
            results[obj_type] = {"status": "requires_discovery", "error": "Discovery service not running"}
            print(f"🔍 {obj_type} (requires discovery service)")
        else:
            failing_objects.append(obj_type)
            results[obj_type] = {"status": "failed", "error": f"HTTP {response.status_code}"}
            print(f"❌ {obj_type} (HTTP {response.status_code})")
            
    except Exception as e:
        failing_objects.append(obj_type)
        results[obj_type] = {"status": "error", "error": str(e)}
        print(f"❌ {obj_type} (Error: {str(e)[:50]}...)")
    
    # Small delay to avoid overwhelming the server
    time.sleep(0.1)

# Save results
availability_report = {
    "total_objects": len(tools_by_object),
    "working": working_objects,
    "requires_discovery": discovery_objects,
    "failing": failing_objects,
    "details": results
}

with open('tool_availability_report.json', 'w') as f:
    json.dump(availability_report, f, indent=2)

# Print summary
print(f"\n📊 Summary:")
print(f"• Total object types: {len(tools_by_object)}")
print(f"• Working: {len(working_objects)} ✅")
print(f"• Requires Discovery: {len(discovery_objects)} 🔍")
print(f"• Failed/Not Available: {len(failing_objects)} ❌")

# Print working categories
print(f"\n✅ Working Categories:")
categories = {
    "Network": 0,
    "DNS": 0,
    "DHCP": 0,
    "IPAM": 0,
    "Grid": 0,
    "Other": 0
}

for obj in working_objects:
    if 'network' in obj and 'discovery' not in obj:
        categories["Network"] += 1
    elif 'record:' in obj or 'zone' in obj:
        categories["DNS"] += 1
    elif any(x in obj for x in ['dhcp', 'range', 'lease', 'fixed']):
        categories["DHCP"] += 1
    elif any(x in obj for x in ['ipv4', 'ipv6']):
        categories["IPAM"] += 1
    elif 'grid' in obj or 'member' in obj:
        categories["Grid"] += 1
    else:
        categories["Other"] += 1

for cat, count in categories.items():
    if count > 0:
        print(f"  • {cat}: {count} object types")

# Show some working examples
print(f"\n🌟 Examples of working tools:")
for obj in working_objects[:10]:
    tools = [t['id'] for t in tools_by_object[obj] if t['operation'] == 'list']
    if tools:
        print(f"  • {tools[0]} - List {obj} objects")

print(f"\n💡 Report saved to: tool_availability_report.json")
