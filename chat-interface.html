<!DOCTYPE html>
<html>
<head>
    <title>InfoBlox Chat Interface</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .header {
            background: #1976d2;
            color: white;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 1200px;
            width: 100%;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        .message {
            margin-bottom: 20px;
        }
        .message.user {
            text-align: right;
        }
        .message.assistant {
            text-align: left;
        }
        .message-content {
            display: inline-block;
            padding: 12px 16px;
            border-radius: 8px;
            max-width: 70%;
            white-space: pre-wrap;
        }
        .user .message-content {
            background: #1976d2;
            color: white;
        }
        .assistant .message-content {
            background: #f0f0f0;
            color: #333;
        }
        .input-area {
            border-top: 1px solid #ddd;
            padding: 20px;
            display: flex;
            gap: 10px;
        }
        .input-area input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .input-area button {
            padding: 12px 24px;
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .input-area button:hover {
            background: #1565c0;
        }
        .input-area button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .quick-actions {
            padding: 10px 20px;
            background: #fafafa;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .quick-action {
            padding: 6px 12px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        .quick-action:hover {
            background: #e3f2fd;
            border-color: #1976d2;
        }
        .format-toggle {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-left: auto;
        }
        .json-content {
            background: #f5f5f5;
            padding: 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .tool-browser-link {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 16px;
            background: #4caf50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .tool-browser-link:hover {
            background: #45a049;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1976d2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>InfoBlox Assistant</h1>
        <p>Chat with your InfoBlox WAPI - 1,300+ tools available</p>
    </div>
    
    <div class="chat-container">
        <div class="messages" id="messages"></div>
        
        <div class="quick-actions">
            <div class="quick-action" onclick="sendMessage('test connection')">Test Connection</div>
            <div class="quick-action" onclick="sendMessage('list all networks')">List Networks</div>
            <div class="quick-action" onclick="sendMessage('list all networks json')">List Networks (JSON)</div>
            <div class="quick-action" onclick="sendMessage('browse tools')">Browse Tools</div>
            <div class="quick-action" onclick="sendMessage('list dns zones')">List DNS Zones</div>
            <div class="format-toggle">
                <label>
                    <input type="checkbox" id="jsonFormat"> Always use JSON format
                </label>
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="Type your message..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()" id="sendButton">Send</button>
        </div>
    </div>

    <script>
        const messagesDiv = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const jsonFormatCheckbox = document.getElementById('jsonFormat');

        function addMessage(content, isUser) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            // Check if content is JSON
            if (!isUser && content.trim().startsWith('{')) {
                try {
                    const json = JSON.parse(content);
                    contentDiv.innerHTML = '<div class="json-content">' + JSON.stringify(json, null, 2) + '</div>';
                } catch (e) {
                    contentDiv.textContent = content;
                }
            } else {
                // Convert markdown-style formatting
                let html = content
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/• /g, '• ')
                    .replace(/\n/g, '<br>');
                
                // Add tool browser link if mentioned
                if (content.includes('http://localhost:8000/ui/tool-browser')) {
                    html = html.replace(
                        'http://localhost:8000/ui/tool-browser',
                        '<a href="http://localhost:8000/ui/tool-browser" target="_blank" class="tool-browser-link">Open Tool Browser</a>'
                    );
                }
                
                contentDiv.innerHTML = html;
            }
            
            messageDiv.appendChild(contentDiv);
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        async function sendMessage(message) {
            const text = message || messageInput.value.trim();
            if (!text) return;
            
            // Add JSON format if checkbox is checked
            const finalText = jsonFormatCheckbox.checked && !text.includes('json') ? text + ' json' : text;
            
            // Add user message
            addMessage(text, true);
            
            // Clear input
            if (!message) {
                messageInput.value = '';
            }
            
            // Disable input
            sendButton.disabled = true;
            messageInput.disabled = true;
            
            // Add loading indicator
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'message assistant';
            loadingDiv.innerHTML = '<div class="message-content"><div class="loading"></div></div>';
            messagesDiv.appendChild(loadingDiv);
            
            try {
                const response = await fetch('http://localhost:8000/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        messages: [
                            { role: 'user', content: finalText }
                        ]
                    })
                });
                
                const data = await response.json();
                
                // Remove loading indicator
                messagesDiv.removeChild(loadingDiv);
                
                if (data.choices && data.choices[0]) {
                    addMessage(data.choices[0].message.content, false);
                } else if (data.error) {
                    addMessage('Error: ' + data.error, false);
                }
            } catch (error) {
                messagesDiv.removeChild(loadingDiv);
                addMessage('Error: Failed to connect to InfoBlox MCP server. Make sure it\'s running on port 8000.', false);
            } finally {
                // Re-enable input
                sendButton.disabled = false;
                messageInput.disabled = false;
                messageInput.focus();
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Initial message
        addMessage('Welcome to InfoBlox Assistant! I can help you manage your InfoBlox infrastructure with over 1,300 tools. Try "test connection" to get started.', false);
        
        // Focus input
        messageInput.focus();
    </script>
</body>
</html>
