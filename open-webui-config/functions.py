"""
InfoBlox Custom Functions for Open WebUI
These functions provide direct integration with InfoBlox MCP server
"""

import requests
import json
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field


class InfoBloxFunction:
    """Base class for InfoBlox functions"""
    
    def __init__(self):
        self.mcp_base_url = "http://infoblox-mcp:8000"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'OpenWebUI-InfoBlox/1.0'
        })

    def call_mcp(self, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make a call to the InfoBlox MCP server"""
        try:
            if data:
                response = self.session.post(f"{self.mcp_base_url}{endpoint}", json=data)
            else:
                response = self.session.get(f"{self.mcp_base_url}{endpoint}")
            
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}


class NetworkDiscoveryInput(BaseModel):
    """Input for network discovery function"""
    include_usage: bool = Field(default=True, description="Include usage statistics")
    network_view: str = Field(default="default", description="Network view to query")


class NetworkDiscoveryFunction(InfoBloxFunction):
    """Discover and list all networks"""
    
    def __call__(self, input_data: NetworkDiscoveryInput) -> Dict[str, Any]:
        """Execute network discovery"""
        prompt = f"list all networks"
        if input_data.include_usage:
            prompt += " with usage statistics"
        
        response = self.call_mcp("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            return {
                "success": True,
                "data": response["choices"][0]["message"]["content"],
                "type": "network_list"
            }
        
        return {"success": False, "error": "Failed to retrieve networks"}


class DNSManagementInput(BaseModel):
    """Input for DNS management function"""
    action: str = Field(description="Action to perform: list, create, delete, update")
    zone_name: Optional[str] = Field(default=None, description="DNS zone name")
    record_type: Optional[str] = Field(default=None, description="DNS record type")


class DNSManagementFunction(InfoBloxFunction):
    """Manage DNS zones and records"""
    
    def __call__(self, input_data: DNSManagementInput) -> Dict[str, Any]:
        """Execute DNS management operation"""
        if input_data.action == "list":
            prompt = "list all dns zones"
        elif input_data.action == "create" and input_data.zone_name:
            prompt = f"create dns zone {input_data.zone_name}"
        elif input_data.action == "delete" and input_data.zone_name:
            prompt = f"delete dns zone {input_data.zone_name}"
        else:
            return {"success": False, "error": "Invalid action or missing parameters"}
        
        response = self.call_mcp("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            return {
                "success": True,
                "data": response["choices"][0]["message"]["content"],
                "type": "dns_operation"
            }
        
        return {"success": False, "error": "Failed to perform DNS operation"}


class IPSearchInput(BaseModel):
    """Input for IP search function"""
    ip_address: str = Field(description="IP address to search for")
    include_details: bool = Field(default=True, description="Include detailed information")


class IPSearchFunction(InfoBloxFunction):
    """Search for IP address information"""
    
    def __call__(self, input_data: IPSearchInput) -> Dict[str, Any]:
        """Execute IP search"""
        prompt = f"search ip {input_data.ip_address}"
        if input_data.include_details:
            prompt += " with details"
        
        response = self.call_mcp("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            return {
                "success": True,
                "data": response["choices"][0]["message"]["content"],
                "type": "ip_search"
            }
        
        return {"success": False, "error": "Failed to search IP address"}


class SystemHealthInput(BaseModel):
    """Input for system health check"""
    detailed: bool = Field(default=False, description="Include detailed health information")


class SystemHealthFunction(InfoBloxFunction):
    """Check InfoBlox system health"""
    
    def __call__(self, input_data: SystemHealthInput) -> Dict[str, Any]:
        """Execute system health check"""
        # First check MCP server health
        health_response = self.call_mcp("/health")
        
        if "error" in health_response:
            return {
                "success": False,
                "error": "MCP server is not responding",
                "type": "health_check"
            }
        
        # Then test InfoBlox connection
        prompt = "test connection"
        if input_data.detailed:
            prompt += " detailed"
        
        response = self.call_mcp("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            return {
                "success": True,
                "data": response["choices"][0]["message"]["content"],
                "mcp_health": health_response,
                "type": "health_check"
            }
        
        return {"success": False, "error": "Failed to check system health"}


class ToolBrowserInput(BaseModel):
    """Input for tool browser function"""
    category: Optional[str] = Field(default=None, description="Tool category to browse")
    search_term: Optional[str] = Field(default=None, description="Search term for tools")


class ToolBrowserFunction(InfoBloxFunction):
    """Browse available InfoBlox tools"""
    
    def __call__(self, input_data: ToolBrowserInput) -> Dict[str, Any]:
        """Execute tool browsing"""
        if input_data.search_term:
            prompt = f"search tools: {input_data.search_term}"
        elif input_data.category:
            prompt = f"browse tools category {input_data.category}"
        else:
            prompt = "browse tools"
        
        response = self.call_mcp("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            return {
                "success": True,
                "data": response["choices"][0]["message"]["content"],
                "type": "tool_browser"
            }
        
        return {"success": False, "error": "Failed to browse tools"}


# Function registry for Open WebUI
INFOBLOX_FUNCTIONS = {
    "network_discovery": {
        "function": NetworkDiscoveryFunction(),
        "input_model": NetworkDiscoveryInput,
        "description": "Discover and list all networks in InfoBlox",
        "category": "Network Management"
    },
    "dns_management": {
        "function": DNSManagementFunction(),
        "input_model": DNSManagementInput,
        "description": "Manage DNS zones and records",
        "category": "DNS Management"
    },
    "ip_search": {
        "function": IPSearchFunction(),
        "input_model": IPSearchInput,
        "description": "Search for IP address information",
        "category": "IP Management"
    },
    "system_health": {
        "function": SystemHealthFunction(),
        "input_model": SystemHealthInput,
        "description": "Check InfoBlox system health and connectivity",
        "category": "System Management"
    },
    "tool_browser": {
        "function": ToolBrowserFunction(),
        "input_model": ToolBrowserInput,
        "description": "Browse available InfoBlox tools and capabilities",
        "category": "Tool Management"
    }
}


def get_function_list() -> List[Dict[str, Any]]:
    """Get list of available functions for Open WebUI"""
    return [
        {
            "name": name,
            "description": func_info["description"],
            "category": func_info["category"],
            "input_schema": func_info["input_model"].schema()
        }
        for name, func_info in INFOBLOX_FUNCTIONS.items()
    ]


def execute_function(function_name: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
    """Execute a specific InfoBlox function"""
    if function_name not in INFOBLOX_FUNCTIONS:
        return {"success": False, "error": f"Function {function_name} not found"}
    
    func_info = INFOBLOX_FUNCTIONS[function_name]
    
    try:
        # Validate input
        validated_input = func_info["input_model"](**input_data)
        
        # Execute function
        result = func_info["function"](validated_input)
        
        return result
    except Exception as e:
        return {"success": False, "error": str(e)}
