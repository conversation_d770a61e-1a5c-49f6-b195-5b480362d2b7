{"models": [{"id": "infoblox-assistant", "name": "InfoBlox Assistant", "description": "Natural language InfoBlox WAPI management with 1,300+ tools for network infrastructure", "provider": "openai", "base_url": "http://infoblox-mcp:8000/v1", "api_key": "dummy-key", "max_tokens": 4096, "context_length": 8192, "temperature": 0.1, "top_p": 0.9, "frequency_penalty": 0, "presence_penalty": 0, "capabilities": ["chat", "function_calling", "tool_use"], "metadata": {"category": "Network Management", "vendor": "InfoBlox", "version": "2.13.1", "tools_count": 1300, "features": ["Network Discovery", "DNS Management", "IP Address Management", "DHCP Configuration", "Security Policies", "Reporting & Analytics"]}, "system_prompt": "You are an InfoBlox network management assistant with access to over 1,300 WAPI tools. You help users manage their network infrastructure through natural language commands. Always provide clear, actionable responses and suggest next steps. When users ask for help, guide them through available options with examples.", "default_prompts": ["Show me all networks in my infrastructure", "Test InfoBlox connection and system status", "List all DNS zones with their current status", "Generate an IP usage report", "Browse available tools and categories", "Help me create a new network", "Search for information about an IP address", "Show DHCP scope utilization"]}], "model_groups": [{"name": "InfoBlox Management", "description": "Network infrastructure management tools", "models": ["infoblox-assistant"], "icon": "🌐", "color": "#0066cc"}], "default_model": "infoblox-assistant", "model_selection": {"show_description": true, "show_capabilities": true, "show_metadata": true, "group_by_category": true}}