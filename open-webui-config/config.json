{"ui": {"name": "InfoBlox Network Management", "description": "Professional network infrastructure management powered by InfoBlox WAPI", "default_locale": "en-US", "prompt_suggestions": [{"title": "🔍 Network Discovery", "content": "Show me all networks in my infrastructure"}, {"title": "🌐 DNS Management", "content": "List all DNS zones and their status"}, {"title": "📊 IP Usage Report", "content": "Generate an IP usage report for network 10.0.0.0/24"}, {"title": "🛠️ Browse Tools", "content": "browse tools"}, {"title": "🔧 System Health", "content": "test connection and show system status"}]}, "models": {"default": "infoblox-assistant", "openai": {"api_base_url": "http://infoblox-mcp:8000/v1", "api_key": "dummy-key", "models": [{"id": "infoblox-assistant", "name": "InfoBlox Assistant", "description": "Natural language InfoBlox WAPI management with 1,300+ tools", "max_tokens": 4096, "context_length": 8192}]}}, "features": {"enable_signup": true, "enable_login": true, "enable_web_search": false, "enable_image_generation": false, "enable_community_sharing": false, "enable_message_rating": true, "enable_model_filter": true}, "branding": {"name": "InfoBlox Assistant", "logo_url": "/static/custom/infoblox-logo.png", "favicon_url": "/static/custom/favicon-infoblox.ico", "primary_color": "#0066cc", "secondary_color": "#004499", "accent_color": "#00aaff"}, "security": {"jwt_expires_in": "7d", "session_cookie_secure": false, "session_cookie_httponly": true}}