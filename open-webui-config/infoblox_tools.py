"""
InfoBlox Tools Integration for Open WebUI
Custom tools that integrate directly with InfoBlox MCP server
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime


class InfoBloxTool:
    """Base class for InfoBlox tools in Open WebUI"""
    
    def __init__(self, name: str, description: str, category: str = "InfoBlox"):
        self.name = name
        self.description = description
        self.category = category
        self.mcp_url = "http://infoblox-mcp:8000"
    
    async def call_mcp_async(self, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Async call to InfoBlox MCP server"""
        async with aiohttp.ClientSession() as session:
            try:
                if data:
                    async with session.post(f"{self.mcp_url}{endpoint}", json=data) as response:
                        return await response.json()
                else:
                    async with session.get(f"{self.mcp_url}{endpoint}") as response:
                        return await response.json()
            except Exception as e:
                return {"error": str(e)}
    
    def format_response(self, data: Any, response_type: str = "text") -> str:
        """Format response for Open WebUI display"""
        if isinstance(data, dict) and "error" in data:
            return f"❌ Error: {data['error']}"
        
        if response_type == "json":
            return f"```json\n{json.dumps(data, indent=2)}\n```"
        
        return str(data)


class NetworkDiscoveryTool(InfoBloxTool):
    """Tool for discovering networks"""
    
    def __init__(self):
        super().__init__(
            name="network_discovery",
            description="Discover and list all networks in InfoBlox infrastructure",
            category="Network Management"
        )
    
    async def execute(self, include_usage: bool = True, format_json: bool = False) -> str:
        """Execute network discovery"""
        prompt = "list all networks"
        if include_usage:
            prompt += " with usage statistics"
        if format_json:
            prompt += " json"
        
        response = await self.call_mcp_async("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            content = response["choices"][0]["message"]["content"]
            return f"🌐 **Network Discovery Results**\n\n{content}"
        
        return self.format_response(response)


class DNSManagementTool(InfoBloxTool):
    """Tool for DNS management"""
    
    def __init__(self):
        super().__init__(
            name="dns_management",
            description="Manage DNS zones and records in InfoBlox",
            category="DNS Management"
        )
    
    async def list_zones(self, format_json: bool = False) -> str:
        """List all DNS zones"""
        prompt = "list all dns zones"
        if format_json:
            prompt += " json"
        
        response = await self.call_mcp_async("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            content = response["choices"][0]["message"]["content"]
            return f"🌐 **DNS Zones**\n\n{content}"
        
        return self.format_response(response)
    
    async def create_zone(self, zone_name: str, zone_type: str = "forward") -> str:
        """Create a new DNS zone"""
        prompt = f"create dns zone {zone_name} type {zone_type}"
        
        response = await self.call_mcp_async("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            content = response["choices"][0]["message"]["content"]
            return f"✅ **DNS Zone Creation**\n\n{content}"
        
        return self.format_response(response)


class IPManagementTool(InfoBloxTool):
    """Tool for IP address management"""
    
    def __init__(self):
        super().__init__(
            name="ip_management",
            description="Search and manage IP addresses in InfoBlox",
            category="IP Management"
        )
    
    async def search_ip(self, ip_address: str, detailed: bool = True) -> str:
        """Search for IP address information"""
        prompt = f"search ip {ip_address}"
        if detailed:
            prompt += " detailed"
        
        response = await self.call_mcp_async("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            content = response["choices"][0]["message"]["content"]
            return f"🔍 **IP Search Results for {ip_address}**\n\n{content}"
        
        return self.format_response(response)
    
    async def get_next_available_ip(self, network: str) -> str:
        """Get next available IP in network"""
        prompt = f"get next available ip in network {network}"
        
        response = await self.call_mcp_async("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            content = response["choices"][0]["message"]["content"]
            return f"📍 **Next Available IP in {network}**\n\n{content}"
        
        return self.format_response(response)


class SystemHealthTool(InfoBloxTool):
    """Tool for system health monitoring"""
    
    def __init__(self):
        super().__init__(
            name="system_health",
            description="Monitor InfoBlox system health and connectivity",
            category="System Management"
        )
    
    async def check_connection(self, detailed: bool = False) -> str:
        """Check InfoBlox connection"""
        # First check MCP health
        health_response = await self.call_mcp_async("/health")
        
        if "error" in health_response:
            return "❌ **MCP Server Offline**\n\nThe InfoBlox MCP server is not responding."
        
        # Then test InfoBlox connection
        prompt = "test connection"
        if detailed:
            prompt += " detailed"
        
        response = await self.call_mcp_async("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            content = response["choices"][0]["message"]["content"]
            mcp_status = f"MCP Server: ✅ Online (Tools: {health_response.get('tools_loaded', 'Unknown')})"
            return f"🔧 **System Health Check**\n\n{mcp_status}\n\n{content}"
        
        return self.format_response(response)
    
    async def get_system_info(self) -> str:
        """Get system information"""
        response = await self.call_mcp_async("/health")
        
        if "error" not in response:
            info = f"""📊 **InfoBlox MCP System Information**

**Status:** ✅ Online
**Grid Master:** {response.get('grid_master', 'Unknown')}
**Tools Loaded:** {response.get('tools_loaded', 'Unknown')}
**Timestamp:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Use other tools to get detailed InfoBlox infrastructure information."""
            return info
        
        return self.format_response(response)


class ToolBrowserTool(InfoBloxTool):
    """Tool for browsing available InfoBlox tools"""
    
    def __init__(self):
        super().__init__(
            name="tool_browser",
            description="Browse and search InfoBlox WAPI tools",
            category="Tool Management"
        )
    
    async def browse_tools(self, category: Optional[str] = None) -> str:
        """Browse available tools"""
        if category:
            prompt = f"browse tools category {category}"
        else:
            prompt = "browse tools"
        
        response = await self.call_mcp_async("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            content = response["choices"][0]["message"]["content"]
            return f"🛠️ **InfoBlox Tools Browser**\n\n{content}"
        
        return self.format_response(response)
    
    async def search_tools(self, search_term: str) -> str:
        """Search for specific tools"""
        prompt = f"search tools: {search_term}"
        
        response = await self.call_mcp_async("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            content = response["choices"][0]["message"]["content"]
            return f"🔍 **Tool Search Results for '{search_term}'**\n\n{content}"
        
        return self.format_response(response)


class ReportingTool(InfoBloxTool):
    """Tool for generating reports"""
    
    def __init__(self):
        super().__init__(
            name="reporting",
            description="Generate InfoBlox infrastructure reports",
            category="Reporting"
        )
    
    async def network_usage_report(self, network: Optional[str] = None) -> str:
        """Generate network usage report"""
        if network:
            prompt = f"generate usage report for network {network}"
        else:
            prompt = "generate network usage report for all networks"
        
        response = await self.call_mcp_async("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            content = response["choices"][0]["message"]["content"]
            return f"📊 **Network Usage Report**\n\n{content}"
        
        return self.format_response(response)
    
    async def dns_health_report(self) -> str:
        """Generate DNS health report"""
        prompt = "generate dns health report"
        
        response = await self.call_mcp_async("/v1/chat/completions", {
            "messages": [{"role": "user", "content": prompt}]
        })
        
        if "choices" in response and response["choices"]:
            content = response["choices"][0]["message"]["content"]
            return f"🌐 **DNS Health Report**\n\n{content}"
        
        return self.format_response(response)


# Tool registry for Open WebUI
INFOBLOX_TOOLS = {
    "network_discovery": NetworkDiscoveryTool(),
    "dns_management": DNSManagementTool(),
    "ip_management": IPManagementTool(),
    "system_health": SystemHealthTool(),
    "tool_browser": ToolBrowserTool(),
    "reporting": ReportingTool()
}


def get_available_tools() -> List[Dict[str, Any]]:
    """Get list of available InfoBlox tools"""
    return [
        {
            "name": tool.name,
            "description": tool.description,
            "category": tool.category
        }
        for tool in INFOBLOX_TOOLS.values()
    ]


async def execute_tool(tool_name: str, method: str, **kwargs) -> str:
    """Execute a specific InfoBlox tool method"""
    if tool_name not in INFOBLOX_TOOLS:
        return f"❌ Tool '{tool_name}' not found"
    
    tool = INFOBLOX_TOOLS[tool_name]
    
    if not hasattr(tool, method):
        return f"❌ Method '{method}' not found in tool '{tool_name}'"
    
    try:
        method_func = getattr(tool, method)
        if asyncio.iscoroutinefunction(method_func):
            return await method_func(**kwargs)
        else:
            return method_func(**kwargs)
    except Exception as e:
        return f"❌ Error executing {tool_name}.{method}: {str(e)}"
