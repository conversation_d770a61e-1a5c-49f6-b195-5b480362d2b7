#!/bin/bash
# Demo script for InfoBlox Tool Browser in Chat

echo "🎯 InfoBlox Tool Browser Demo"
echo "============================"
echo ""
echo "The tool browser is now embedded directly in your chat!"
echo ""
echo "Example conversation flow:"
echo ""

# Example 1: Browse tools
echo "1. Browse available tools:"
echo "   You: browse tools"
echo "   Bot: Shows all 1,345 tools organized in 9 categories"
echo ""

# Example 2: Search for tools
echo "2. Search for network tools:"
echo "   You: search tools: network"
curl -s -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "search tools: network"}]}' | \
  jq -r '.choices[0].message.content' | head -15
echo ""

# Example 3: Execute a tool
echo "3. Execute a tool:"
echo "   You: execute tool: {\"tool_id\": \"list_network\", \"parameters\": {\"_max_results\": 3}}"
curl -s -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "execute tool: {\"tool_id\": \"list_network\", \"parameters\": {\"_max_results\": 3}}"}]}' | \
  jq -r '.choices[0].message.content'
echo ""

echo "✅ All tools are accessible directly in Open WebUI chat!"
echo "No need to open separate ports or browser windows."
