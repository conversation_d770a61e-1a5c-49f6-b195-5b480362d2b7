# InfoBlox MCP + Open WebUI Integration

A professional ChatGPT-like interface for InfoBlox network management, combining the power of InfoBlox WAPI with Open WebUI's modern chat interface.

## 🌟 Features

- **🌐 Complete InfoBlox Integration**: Access 1,300+ WAPI tools through natural language
- **💬 ChatGPT-like Interface**: Modern, responsive chat interface powered by Open WebUI
- **🎨 Custom Branding**: Professional InfoBlox-themed UI with custom styling
- **🐳 Docker Orchestration**: Easy deployment with Docker Compose
- **🔧 Custom Tools**: Specialized InfoBlox functions integrated into Open WebUI
- **📊 Real-time Monitoring**: System health checks and connection status
- **🚀 Production Ready**: SSL support, security features, and scalable architecture

## 🏗️ Architecture

```
┌─────────────────────────────────────────────┐
│  Open WebUI (Port 3000)                    │
│  ├── Custom InfoBlox Theme                 │
│  ├── Branded Interface                     │
│  ├── Integrated Functions                  │
│  └── Real-time Status                      │
└─────────────────────────────────────────────┘
                    ↕️ HTTP API
┌─────────────────────────────────────────────┐
│  InfoBlox MCP Server (Port 8000)           │
│  ├── Natural Language Processing           │
│  ├── 1,300+ WAPI Tools                     │
│  ├── OpenAI-Compatible API                 │
│  └── Health Monitoring                     │
└─────────────────────────────────────────────┘
                    ↕️ WAPI
┌─────────────────────────────────────────────┐
│  InfoBlox Grid Master                      │
│  └── Network Infrastructure                │
└─────────────────────────────────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- InfoBlox Grid Master access
- Network connectivity between containers and InfoBlox

### 1. Clone and Setup

```bash
git clone <repository>
cd infoblox-real-mcp
chmod +x setup.sh
./setup.sh
```

### 2. Configure Environment

```bash
cp .env.example .env
# Edit .env with your InfoBlox credentials
nano .env
```

Required environment variables:
```env
GRID_MASTER_URL=https://your-infoblox-ip/wapi/v2.13.1
INFOBLOX_USERNAME=admin
INFOBLOX_PASSWORD=your-password
NETWORK_VIEW=default
```

### 3. Deploy

```bash
# Development deployment
docker-compose up -d

# Production deployment with SSL
docker-compose --profile production up -d
```

### 4. Access

- **Open WebUI**: http://localhost:3000
- **InfoBlox MCP**: http://localhost:8000
- **Health Check**: http://localhost:8000/health

## 🎨 Customization

### UI Branding

The interface is fully customized with InfoBlox branding:

- **Colors**: InfoBlox blue theme (#0066cc, #004499, #00aaff)
- **Logo**: Custom InfoBlox logo and favicon
- **Typography**: Professional Segoe UI font stack
- **Layout**: Modern card-based design with gradients

### Custom CSS

Edit `static/infoblox-theme.css` to modify:
- Color scheme
- Typography
- Layout and spacing
- Animations and effects

### JavaScript Enhancements

`static/custom-branding.js` provides:
- Dynamic branding updates
- Connection status monitoring
- Quick action buttons
- Welcome messages

## 🔧 Configuration

### Open WebUI Settings

1. **Add Model Connection**:
   - Go to Settings → Connections
   - Add OpenAI API connection:
     - Base URL: `http://localhost:8000/v1`
     - API Key: `dummy-key`
     - Model: `infoblox-assistant`

2. **Import Functions**:
   - Copy functions from `open-webui-config/functions.py`
   - Add to Open WebUI Functions section

3. **Apply Theme**:
   - Upload custom CSS and JS files
   - Configure branding settings

### InfoBlox MCP Configuration

The MCP server automatically loads:
- WAPI tools from `infoblox_mcp_tools.json`
- Schema from `wapi_schema.json`
- Object definitions from `discovered_wapi_objects.json`

## 📚 Usage Examples

### Natural Language Commands

```
User: Show me all networks in my infrastructure
Bot: [Lists all networks with usage statistics]

User: Create a new network for guest WiFi
Bot: [Guides through network creation process]

User: What's the health of my DNS zones?
Bot: [Shows DNS zone status and health metrics]

User: Find information about IP **********
Bot: [Displays detailed IP information]
```

### Quick Actions

The interface provides quick action buttons for:
- 🔍 Network Discovery
- 🌐 DNS Management  
- 📊 IP Usage Reports
- 🛠️ Tool Browser
- 🔧 System Health

### Advanced Features

- **Tool Browser**: `browse tools` to see all 1,300+ available tools
- **Search Tools**: `search tools: network` to find specific capabilities
- **JSON Output**: Add `json` to any command for structured data
- **Batch Operations**: Execute multiple commands in sequence

## 🔒 Security

### Production Deployment

For production use:

1. **Enable SSL**:
   ```bash
   # Add SSL certificates to nginx/ssl/
   docker-compose --profile production up -d
   ```

2. **Secure Environment**:
   - Use strong passwords
   - Enable firewall rules
   - Configure proper network segmentation

3. **Authentication**:
   - Enable user authentication in Open WebUI
   - Configure LDAP/SSO if needed
   - Set up proper user roles

### Network Security

- InfoBlox credentials are encrypted in transit
- API keys are configurable
- Container network isolation
- Health check endpoints for monitoring

## 🐛 Troubleshooting

### Common Issues

1. **Model Not Appearing**:
   - Check MCP server health: `curl http://localhost:8000/health`
   - Verify Open WebUI connection settings
   - Refresh browser and check model dropdown

2. **Connection Errors**:
   - Verify InfoBlox credentials in `.env`
   - Check network connectivity to Grid Master
   - Review container logs: `docker-compose logs`

3. **UI Issues**:
   - Clear browser cache
   - Check custom CSS/JS loading
   - Verify static file mounting

### Logs and Monitoring

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f infoblox-mcp
docker-compose logs -f open-webui

# Check container health
docker-compose ps
```

## 🔄 Updates and Maintenance

### Updating the System

```bash
# Pull latest images
docker-compose pull

# Restart services
docker-compose down
docker-compose up -d
```

### Backup and Restore

```bash
# Backup Open WebUI data
docker run --rm -v infoblox-real-mcp_open-webui-data:/data -v $(pwd):/backup alpine tar czf /backup/openwebui-backup.tar.gz -C /data .

# Restore Open WebUI data
docker run --rm -v infoblox-real-mcp_open-webui-data:/data -v $(pwd):/backup alpine tar xzf /backup/openwebui-backup.tar.gz -C /data
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📋 API Reference

### InfoBlox MCP Endpoints

- `GET /health` - Server health check
- `GET /v1/models` - Available models
- `POST /v1/chat/completions` - Chat interface
- `GET /tools` - Tool information
- `GET /v1/prompts` - Suggested prompts

### Custom Functions

Available in Open WebUI Functions:
- `network_discovery()` - List all networks
- `dns_management()` - Manage DNS zones
- `ip_search()` - Search IP addresses
- `system_health()` - Health monitoring
- `tool_browser()` - Browse WAPI tools

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section
- Review container logs
- Open an issue on GitHub
- Contact your InfoBlox administrator

---

**🌐 InfoBlox Network Management - Powered by Open WebUI**
