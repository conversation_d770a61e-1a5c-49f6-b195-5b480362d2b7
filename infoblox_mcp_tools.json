[{"id": "list_ad_auth_service", "object": "ad_auth_service", "operation": "list", "name": "List ad_auth_service", "description": "List/search ad_auth_service objects with filters"}, {"id": "get_ad_auth_service", "object": "ad_auth_service", "operation": "get", "name": "Get ad_auth_service", "description": "Get specific ad_auth_service by reference"}, {"id": "create_ad_auth_service", "object": "ad_auth_service", "operation": "create", "name": "Create ad_auth_service", "description": "Create new ad_auth_service"}, {"id": "update_ad_auth_service", "object": "ad_auth_service", "operation": "update", "name": "Update ad_auth_service", "description": "Update existing ad_auth_service"}, {"id": "delete_ad_auth_service", "object": "ad_auth_service", "operation": "delete", "name": "Delete ad_auth_service", "description": "Delete ad_auth_service"}, {"id": "list_admingroup", "object": "admingroup", "operation": "list", "name": "List admingroup", "description": "List/search admingroup objects with filters"}, {"id": "get_admingroup", "object": "admingroup", "operation": "get", "name": "Get admingroup", "description": "Get specific admingroup by reference"}, {"id": "create_admingroup", "object": "admingroup", "operation": "create", "name": "Create admingroup", "description": "Create new admingroup"}, {"id": "update_admingroup", "object": "admingroup", "operation": "update", "name": "Update admingroup", "description": "Update existing admingroup"}, {"id": "delete_admingroup", "object": "admingroup", "operation": "delete", "name": "Delete admingroup", "description": "Delete admingroup"}, {"id": "list_adminrole", "object": "adminrole", "operation": "list", "name": "List adminrole", "description": "List/search adminrole objects with filters"}, {"id": "get_adminrole", "object": "adminrole", "operation": "get", "name": "Get adminrole", "description": "Get specific adminrole by reference"}, {"id": "create_adminrole", "object": "adminrole", "operation": "create", "name": "Create adminrole", "description": "Create new adminrole"}, {"id": "update_adminrole", "object": "adminrole", "operation": "update", "name": "Update adminrole", "description": "Update existing adminrole"}, {"id": "delete_adminrole", "object": "adminrole", "operation": "delete", "name": "Delete adminrole", "description": "Delete adminrole"}, {"id": "list_adminuser", "object": "adminuser", "operation": "list", "name": "List adminuser", "description": "List/search adminuser objects with filters"}, {"id": "get_adminuser", "object": "adminuser", "operation": "get", "name": "Get adminuser", "description": "Get specific adminuser by reference"}, {"id": "create_adminuser", "object": "adminuser", "operation": "create", "name": "Create adminuser", "description": "Create new adminuser"}, {"id": "update_adminuser", "object": "adminuser", "operation": "update", "name": "Update adminuser", "description": "Update existing adminuser"}, {"id": "delete_adminuser", "object": "adminuser", "operation": "delete", "name": "Delete adminuser", "description": "Delete adminuser"}, {"id": "list_allendpoints", "object": "allendpoints", "operation": "list", "name": "List allendpoints", "description": "List/search allendpoints objects with filters"}, {"id": "get_allendpoints", "object": "allendpoints", "operation": "get", "name": "Get allendpoints", "description": "Get specific allendpoints by reference"}, {"id": "create_allendpoints", "object": "allendpoints", "operation": "create", "name": "Create allendpoints", "description": "Create new allendpoints"}, {"id": "update_allendpoints", "object": "allendpoints", "operation": "update", "name": "Update allendpoints", "description": "Update existing allendpoints"}, {"id": "delete_allendpoints", "object": "allendpoints", "operation": "delete", "name": "Delete allendpoints", "description": "Delete allendpoints"}, {"id": "list_allnsgroup", "object": "allnsgroup", "operation": "list", "name": "List allnsgroup", "description": "List/search allnsgroup objects with filters"}, {"id": "get_allnsgroup", "object": "allnsgroup", "operation": "get", "name": "Get allnsgroup", "description": "Get specific allnsgroup by reference"}, {"id": "create_allnsgroup", "object": "allnsgroup", "operation": "create", "name": "Create allnsgroup", "description": "Create new allnsgroup"}, {"id": "update_allnsgroup", "object": "allnsgroup", "operation": "update", "name": "Update allnsgroup", "description": "Update existing allnsgroup"}, {"id": "delete_allnsgroup", "object": "allnsgroup", "operation": "delete", "name": "Delete allnsgroup", "description": "Delete allnsgroup"}, {"id": "list_allrecords", "object": "allrecords", "operation": "list", "name": "List allrecords", "description": "List/search allrecords objects with filters"}, {"id": "get_allrecords", "object": "allrecords", "operation": "get", "name": "Get allrecords", "description": "Get specific allrecords by reference"}, {"id": "create_allrecords", "object": "allrecords", "operation": "create", "name": "Create allrecords", "description": "Create new allrecords"}, {"id": "update_allrecords", "object": "allrecords", "operation": "update", "name": "Update allrecords", "description": "Update existing allrecords"}, {"id": "delete_allrecords", "object": "allrecords", "operation": "delete", "name": "Delete allrecords", "description": "Delete allrecords"}, {"id": "list_allrpzrecords", "object": "allrpzrecords", "operation": "list", "name": "List allrpzrecords", "description": "List/search allrpzrecords objects with filters"}, {"id": "get_allrpzrecords", "object": "allrpzrecords", "operation": "get", "name": "Get allrpzrecords", "description": "Get specific allrpzrecords by reference"}, {"id": "create_allrpzrecords", "object": "allrpzrecords", "operation": "create", "name": "Create allrpzrecords", "description": "Create new allrpzrecords"}, {"id": "update_allrpzrecords", "object": "allrpzrecords", "operation": "update", "name": "Update allrpzrecords", "description": "Update existing allrpzrecords"}, {"id": "delete_allrpzrecords", "object": "allrpzrecords", "operation": "delete", "name": "Delete allrpzrecords", "description": "Delete allrpzrecords"}, {"id": "list_approvalworkflow", "object": "approvalworkflow", "operation": "list", "name": "List approvalworkflow", "description": "List/search approvalworkflow objects with filters"}, {"id": "get_approvalworkflow", "object": "approvalworkflow", "operation": "get", "name": "Get approvalworkflow", "description": "Get specific approvalworkflow by reference"}, {"id": "create_approvalworkflow", "object": "approvalworkflow", "operation": "create", "name": "Create approvalworkflow", "description": "Create new approvalworkflow"}, {"id": "update_approvalworkflow", "object": "approvalworkflow", "operation": "update", "name": "Update approvalworkflow", "description": "Update existing approvalworkflow"}, {"id": "delete_approvalworkflow", "object": "approvalworkflow", "operation": "delete", "name": "Delete approvalworkflow", "description": "Delete approvalworkflow"}, {"id": "list_authpolicy", "object": "authpolicy", "operation": "list", "name": "List authpolicy", "description": "List/search authpolicy objects with filters"}, {"id": "get_authpolicy", "object": "authpolicy", "operation": "get", "name": "Get authpolicy", "description": "Get specific authpolicy by reference"}, {"id": "create_authpolicy", "object": "authpolicy", "operation": "create", "name": "Create authpolicy", "description": "Create new authpolicy"}, {"id": "update_authpolicy", "object": "authpolicy", "operation": "update", "name": "Update authpolicy", "description": "Update existing authpolicy"}, {"id": "delete_authpolicy", "object": "authpolicy", "operation": "delete", "name": "Delete authpolicy", "description": "Delete authpolicy"}, {"id": "list_awsrte53taskgroup", "object": "awsrte53taskgroup", "operation": "list", "name": "List awsrte53taskgroup", "description": "List/search awsrte53taskgroup objects with filters"}, {"id": "get_awsrte53taskgroup", "object": "awsrte53taskgroup", "operation": "get", "name": "Get awsrte53taskgroup", "description": "Get specific awsrte53taskgroup by reference"}, {"id": "create_awsrte53taskgroup", "object": "awsrte53taskgroup", "operation": "create", "name": "Create awsrte53taskgroup", "description": "Create new awsrte53taskgroup"}, {"id": "update_awsrte53taskgroup", "object": "awsrte53taskgroup", "operation": "update", "name": "Update awsrte53taskgroup", "description": "Update existing awsrte53taskgroup"}, {"id": "delete_awsrte53taskgroup", "object": "awsrte53taskgroup", "operation": "delete", "name": "Delete awsrte53taskgroup", "description": "Delete awsrte53taskgroup"}, {"id": "list_awsuser", "object": "awsuser", "operation": "list", "name": "List awsuser", "description": "List/search awsuser objects with filters"}, {"id": "get_awsuser", "object": "awsuser", "operation": "get", "name": "Get awsuser", "description": "Get specific awsuser by reference"}, {"id": "create_awsuser", "object": "awsuser", "operation": "create", "name": "Create awsuser", "description": "Create new awsuser"}, {"id": "update_awsuser", "object": "awsuser", "operation": "update", "name": "Update awsuser", "description": "Update existing awsuser"}, {"id": "delete_awsuser", "object": "awsuser", "operation": "delete", "name": "Delete awsuser", "description": "Delete awsuser"}, {"id": "list_bfdtemplate", "object": "bfdtemplate", "operation": "list", "name": "List bfdtemplate", "description": "List/search bfdtemplate objects with filters"}, {"id": "get_bfdtemplate", "object": "bfdtemplate", "operation": "get", "name": "Get bfdtemplate", "description": "Get specific bfdtemplate by reference"}, {"id": "create_bfdtemplate", "object": "bfdtemplate", "operation": "create", "name": "Create bfdtemplate", "description": "Create new bfdtemplate"}, {"id": "update_bfdtemplate", "object": "bfdtemplate", "operation": "update", "name": "Update bfdtemplate", "description": "Update existing bfdtemplate"}, {"id": "delete_bfdtemplate", "object": "bfdtemplate", "operation": "delete", "name": "Delete bfdtemplate", "description": "Delete bfdtemplate"}, {"id": "list_bulkhost", "object": "bulkhost", "operation": "list", "name": "List bulkhost", "description": "List/search bulkhost objects with filters"}, {"id": "get_bulkhost", "object": "bulkhost", "operation": "get", "name": "Get bulkhost", "description": "Get specific bulkhost by reference"}, {"id": "create_bulkhost", "object": "bulkhost", "operation": "create", "name": "Create bulkhost", "description": "Create new bulkhost"}, {"id": "update_bulkhost", "object": "bulkhost", "operation": "update", "name": "Update bulkhost", "description": "Update existing bulkhost"}, {"id": "delete_bulkhost", "object": "bulkhost", "operation": "delete", "name": "Delete bulkhost", "description": "Delete bulkhost"}, {"id": "list_bulkhostnametemplate", "object": "bulkhostnametemplate", "operation": "list", "name": "List bulkhostnametemplate", "description": "List/search bulkhostnametemplate objects with filters"}, {"id": "get_bulkhostnametemplate", "object": "bulkhostnametemplate", "operation": "get", "name": "Get bulkhostnametemplate", "description": "Get specific bulkhostnametemplate by reference"}, {"id": "create_bulkhostnametemplate", "object": "bulkhostnametemplate", "operation": "create", "name": "Create bulkhostnametemplate", "description": "Create new bulkhostnametemplate"}, {"id": "update_bulkhostnametemplate", "object": "bulkhostnametemplate", "operation": "update", "name": "Update bulkhostnametemplate", "description": "Update existing bulkhostnametemplate"}, {"id": "delete_bulkhostnametemplate", "object": "bulkhostnametemplate", "operation": "delete", "name": "Delete bulkhostnametemplate", "description": "Delete bulkhostnametemplate"}, {"id": "list_cacertificate", "object": "cacertificate", "operation": "list", "name": "List cacertificate", "description": "List/search cacertificate objects with filters"}, {"id": "get_cacertificate", "object": "cacertificate", "operation": "get", "name": "Get cacertificate", "description": "Get specific cacertificate by reference"}, {"id": "create_cacertificate", "object": "cacertificate", "operation": "create", "name": "Create cacertificate", "description": "Create new cacertificate"}, {"id": "update_cacertificate", "object": "cacertificate", "operation": "update", "name": "Update cacertificate", "description": "Update existing cacertificate"}, {"id": "delete_cacertificate", "object": "cacertificate", "operation": "delete", "name": "Delete cacertificate", "description": "Delete cacertificate"}, {"id": "list_capacityreport", "object": "capacityreport", "operation": "list", "name": "List capacityreport", "description": "List/search capacityreport objects with filters"}, {"id": "get_capacityreport", "object": "capacityreport", "operation": "get", "name": "Get capacityreport", "description": "Get specific capacityreport by reference"}, {"id": "create_capacityreport", "object": "capacityreport", "operation": "create", "name": "Create capacityreport", "description": "Create new capacityreport"}, {"id": "update_capacityreport", "object": "capacityreport", "operation": "update", "name": "Update capacityreport", "description": "Update existing capacityreport"}, {"id": "delete_capacityreport", "object": "capacityreport", "operation": "delete", "name": "Delete capacityreport", "description": "Delete capacityreport"}, {"id": "list_captiveportal", "object": "captiveportal", "operation": "list", "name": "List captiveportal", "description": "List/search captiveportal objects with filters"}, {"id": "get_captiveportal", "object": "captiveportal", "operation": "get", "name": "Get captiveportal", "description": "Get specific captiveportal by reference"}, {"id": "create_captiveportal", "object": "captiveportal", "operation": "create", "name": "Create captiveportal", "description": "Create new captiveportal"}, {"id": "update_captiveportal", "object": "captiveportal", "operation": "update", "name": "Update captiveportal", "description": "Update existing captiveportal"}, {"id": "delete_captiveportal", "object": "captiveportal", "operation": "delete", "name": "Delete captiveportal", "description": "Delete captiveportal"}, {"id": "list_certificate_authservice", "object": "certificate:authservice", "operation": "list", "name": "List certificate:authservice", "description": "List/search certificate:authservice objects with filters"}, {"id": "get_certificate_authservice", "object": "certificate:authservice", "operation": "get", "name": "Get certificate:authservice", "description": "Get specific certificate:authservice by reference"}, {"id": "create_certificate_authservice", "object": "certificate:authservice", "operation": "create", "name": "Create certificate:authservice", "description": "Create new certificate:authservice"}, {"id": "update_certificate_authservice", "object": "certificate:authservice", "operation": "update", "name": "Update certificate:authservice", "description": "Update existing certificate:authservice"}, {"id": "delete_certificate_authservice", "object": "certificate:authservice", "operation": "delete", "name": "Delete certificate:authservice", "description": "Delete certificate:authservice"}, {"id": "list_csvimporttask", "object": "csvimporttask", "operation": "list", "name": "List csvimporttask", "description": "List/search csvimporttask objects with filters"}, {"id": "get_csvimporttask", "object": "csvimporttask", "operation": "get", "name": "Get csvimporttask", "description": "Get specific csvimporttask by reference"}, {"id": "create_csvimporttask", "object": "csvimporttask", "operation": "create", "name": "Create csvimporttask", "description": "Create new csvimporttask"}, {"id": "update_csvimporttask", "object": "csvimporttask", "operation": "update", "name": "Update csvimporttask", "description": "Update existing csvimporttask"}, {"id": "delete_csvimporttask", "object": "csvimporttask", "operation": "delete", "name": "Delete csvimporttask", "description": "Delete csvimporttask"}, {"id": "list_datacollectioncluster", "object": "datacollectioncluster", "operation": "list", "name": "List datacollectioncluster", "description": "List/search datacollectioncluster objects with filters"}, {"id": "get_datacollectioncluster", "object": "datacollectioncluster", "operation": "get", "name": "Get datacollectioncluster", "description": "Get specific datacollectioncluster by reference"}, {"id": "create_datacollectioncluster", "object": "datacollectioncluster", "operation": "create", "name": "Create datacollectioncluster", "description": "Create new datacollectioncluster"}, {"id": "update_datacollectioncluster", "object": "datacollectioncluster", "operation": "update", "name": "Update datacollectioncluster", "description": "Update existing datacollectioncluster"}, {"id": "delete_datacollectioncluster", "object": "datacollectioncluster", "operation": "delete", "name": "Delete datacollectioncluster", "description": "Delete datacollectioncluster"}, {"id": "list_db_objects", "object": "db_objects", "operation": "list", "name": "List db_objects", "description": "List/search db_objects objects with filters"}, {"id": "get_db_objects", "object": "db_objects", "operation": "get", "name": "Get db_objects", "description": "Get specific db_objects by reference"}, {"id": "create_db_objects", "object": "db_objects", "operation": "create", "name": "Create db_objects", "description": "Create new db_objects"}, {"id": "update_db_objects", "object": "db_objects", "operation": "update", "name": "Update db_objects", "description": "Update existing db_objects"}, {"id": "delete_db_objects", "object": "db_objects", "operation": "delete", "name": "Delete db_objects", "description": "Delete db_objects"}, {"id": "list_dbsnapshot", "object": "dbsnapshot", "operation": "list", "name": "List dbsnapshot", "description": "List/search dbsnapshot objects with filters"}, {"id": "get_dbsnapshot", "object": "dbsnapshot", "operation": "get", "name": "Get dbsnapshot", "description": "Get specific dbsnapshot by reference"}, {"id": "create_dbsnapshot", "object": "dbsnapshot", "operation": "create", "name": "Create dbsnapshot", "description": "Create new dbsnapshot"}, {"id": "update_dbsnapshot", "object": "dbsnapshot", "operation": "update", "name": "Update dbsnapshot", "description": "Update existing dbsnapshot"}, {"id": "delete_dbsnapshot", "object": "dbsnapshot", "operation": "delete", "name": "Delete dbsnapshot", "description": "Delete dbsnapshot"}, {"id": "list_ddns_principalcluster", "object": "ddns:principalcluster", "operation": "list", "name": "List ddns:principalcluster", "description": "List/search ddns:principalcluster objects with filters"}, {"id": "get_ddns_principalcluster", "object": "ddns:principalcluster", "operation": "get", "name": "Get ddns:principalcluster", "description": "Get specific ddns:principalcluster by reference"}, {"id": "create_ddns_principalcluster", "object": "ddns:principalcluster", "operation": "create", "name": "Create ddns:principalcluster", "description": "Create new ddns:principalcluster"}, {"id": "update_ddns_principalcluster", "object": "ddns:principalcluster", "operation": "update", "name": "Update ddns:principalcluster", "description": "Update existing ddns:principalcluster"}, {"id": "delete_ddns_principalcluster", "object": "ddns:principalcluster", "operation": "delete", "name": "Delete ddns:principalcluster", "description": "Delete ddns:principalcluster"}, {"id": "list_ddns_principalcluster_group", "object": "ddns:principalcluster:group", "operation": "list", "name": "List ddns:principalcluster:group", "description": "List/search ddns:principalcluster:group objects with filters"}, {"id": "get_ddns_principalcluster_group", "object": "ddns:principalcluster:group", "operation": "get", "name": "Get ddns:principalcluster:group", "description": "Get specific ddns:principalcluster:group by reference"}, {"id": "create_ddns_principalcluster_group", "object": "ddns:principalcluster:group", "operation": "create", "name": "Create ddns:principalcluster:group", "description": "Create new ddns:principalcluster:group"}, {"id": "update_ddns_principalcluster_group", "object": "ddns:principalcluster:group", "operation": "update", "name": "Update ddns:principalcluster:group", "description": "Update existing ddns:principalcluster:group"}, {"id": "delete_ddns_principalcluster_group", "object": "ddns:principalcluster:group", "operation": "delete", "name": "Delete ddns:principalcluster:group", "description": "Delete ddns:principalcluster:group"}, {"id": "list_deleted_objects", "object": "deleted_objects", "operation": "list", "name": "List deleted_objects", "description": "List/search deleted_objects objects with filters"}, {"id": "get_deleted_objects", "object": "deleted_objects", "operation": "get", "name": "Get deleted_objects", "description": "Get specific deleted_objects by reference"}, {"id": "create_deleted_objects", "object": "deleted_objects", "operation": "create", "name": "Create deleted_objects", "description": "Create new deleted_objects"}, {"id": "update_deleted_objects", "object": "deleted_objects", "operation": "update", "name": "Update deleted_objects", "description": "Update existing deleted_objects"}, {"id": "delete_deleted_objects", "object": "deleted_objects", "operation": "delete", "name": "Delete deleted_objects", "description": "Delete deleted_objects"}, {"id": "list_dhcp_statistics", "object": "dhcp:statistics", "operation": "list", "name": "List dhcp:statistics", "description": "List/search dhcp:statistics objects with filters"}, {"id": "get_dhcp_statistics", "object": "dhcp:statistics", "operation": "get", "name": "Get dhcp:statistics", "description": "Get specific dhcp:statistics by reference"}, {"id": "create_dhcp_statistics", "object": "dhcp:statistics", "operation": "create", "name": "Create dhcp:statistics", "description": "Create new dhcp:statistics"}, {"id": "update_dhcp_statistics", "object": "dhcp:statistics", "operation": "update", "name": "Update dhcp:statistics", "description": "Update existing dhcp:statistics"}, {"id": "delete_dhcp_statistics", "object": "dhcp:statistics", "operation": "delete", "name": "Delete dhcp:statistics", "description": "Delete dhcp:statistics"}, {"id": "list_dhcpfailover", "object": "dhc<PERSON><PERSON><PERSON>", "operation": "list", "name": "List dhcpfailover", "description": "List/search dhcpfailover objects with filters"}, {"id": "get_dhcpfailover", "object": "dhc<PERSON><PERSON><PERSON>", "operation": "get", "name": "Get dhcpfailover", "description": "Get specific dhcpfailover by reference"}, {"id": "create_dhcpfailover", "object": "dhc<PERSON><PERSON><PERSON>", "operation": "create", "name": "Create dhcpfailover", "description": "Create new dhcpfailover"}, {"id": "update_dhcpfailover", "object": "dhc<PERSON><PERSON><PERSON>", "operation": "update", "name": "Update dhcpfailover", "description": "Update existing dhcpfailover"}, {"id": "delete_dhcpfailover", "object": "dhc<PERSON><PERSON><PERSON>", "operation": "delete", "name": "Delete dhcpfailover", "description": "Delete dhcpfailover"}, {"id": "list_dhcpoptiondefinition", "object": "dhcpoptiondefinition", "operation": "list", "name": "List dhcpoptiondefinition", "description": "List/search dhcpoptiondefinition objects with filters"}, {"id": "get_dhcpoptiondefinition", "object": "dhcpoptiondefinition", "operation": "get", "name": "Get dhcpoptiondefinition", "description": "Get specific dhcpoptiondefinition by reference"}, {"id": "create_dhcpoptiondefinition", "object": "dhcpoptiondefinition", "operation": "create", "name": "Create dhcpoptiondefinition", "description": "Create new dhcpoptiondefinition"}, {"id": "update_dhcpoptiondefinition", "object": "dhcpoptiondefinition", "operation": "update", "name": "Update dhcpoptiondefinition", "description": "Update existing dhcpoptiondefinition"}, {"id": "delete_dhcpoptiondefinition", "object": "dhcpoptiondefinition", "operation": "delete", "name": "Delete dhcpoptiondefinition", "description": "Delete dhcpoptiondefinition"}, {"id": "list_dhcpoptionspace", "object": "dhcpoptionspace", "operation": "list", "name": "List dhcpoptionspace", "description": "List/search dhcpoptionspace objects with filters"}, {"id": "get_dhcpoptionspace", "object": "dhcpoptionspace", "operation": "get", "name": "Get dhcpoptionspace", "description": "Get specific dhcpoptionspace by reference"}, {"id": "create_dhcpoptionspace", "object": "dhcpoptionspace", "operation": "create", "name": "Create dhcpoptionspace", "description": "Create new dhcpoptionspace"}, {"id": "update_dhcpoptionspace", "object": "dhcpoptionspace", "operation": "update", "name": "Update dhcpoptionspace", "description": "Update existing dhcpoptionspace"}, {"id": "delete_dhcpoptionspace", "object": "dhcpoptionspace", "operation": "delete", "name": "Delete dhcpoptionspace", "description": "Delete dhcpoptionspace"}, {"id": "list_discovery", "object": "discovery", "operation": "list", "name": "List discovery", "description": "List/search discovery objects with filters"}, {"id": "get_discovery", "object": "discovery", "operation": "get", "name": "Get discovery", "description": "Get specific discovery by reference"}, {"id": "create_discovery", "object": "discovery", "operation": "create", "name": "Create discovery", "description": "Create new discovery"}, {"id": "update_discovery", "object": "discovery", "operation": "update", "name": "Update discovery", "description": "Update existing discovery"}, {"id": "delete_discovery", "object": "discovery", "operation": "delete", "name": "Delete discovery", "description": "Delete discovery"}, {"id": "list_discovery_credentialgroup", "object": "discovery:credentialgroup", "operation": "list", "name": "List discovery:credentialgroup", "description": "List/search discovery:credentialgroup objects with filters"}, {"id": "get_discovery_credentialgroup", "object": "discovery:credentialgroup", "operation": "get", "name": "Get discovery:credentialgroup", "description": "Get specific discovery:credentialgroup by reference"}, {"id": "create_discovery_credentialgroup", "object": "discovery:credentialgroup", "operation": "create", "name": "Create discovery:credentialgroup", "description": "Create new discovery:credentialgroup"}, {"id": "update_discovery_credentialgroup", "object": "discovery:credentialgroup", "operation": "update", "name": "Update discovery:credentialgroup", "description": "Update existing discovery:credentialgroup"}, {"id": "delete_discovery_credentialgroup", "object": "discovery:credentialgroup", "operation": "delete", "name": "Delete discovery:credentialgroup", "description": "Delete discovery:credentialgroup"}, {"id": "list_discovery_device", "object": "discovery:device", "operation": "list", "name": "List discovery:device", "description": "List/search discovery:device objects with filters"}, {"id": "get_discovery_device", "object": "discovery:device", "operation": "get", "name": "Get discovery:device", "description": "Get specific discovery:device by reference"}, {"id": "create_discovery_device", "object": "discovery:device", "operation": "create", "name": "Create discovery:device", "description": "Create new discovery:device"}, {"id": "update_discovery_device", "object": "discovery:device", "operation": "update", "name": "Update discovery:device", "description": "Update existing discovery:device"}, {"id": "delete_discovery_device", "object": "discovery:device", "operation": "delete", "name": "Delete discovery:device", "description": "Delete discovery:device"}, {"id": "list_discovery_devicecomponent", "object": "discovery:devicecomponent", "operation": "list", "name": "List discovery:devicecomponent", "description": "List/search discovery:devicecomponent objects with filters"}, {"id": "get_discovery_devicecomponent", "object": "discovery:devicecomponent", "operation": "get", "name": "Get discovery:devicecomponent", "description": "Get specific discovery:devicecomponent by reference"}, {"id": "create_discovery_devicecomponent", "object": "discovery:devicecomponent", "operation": "create", "name": "Create discovery:devicecomponent", "description": "Create new discovery:devicecomponent"}, {"id": "update_discovery_devicecomponent", "object": "discovery:devicecomponent", "operation": "update", "name": "Update discovery:devicecomponent", "description": "Update existing discovery:devicecomponent"}, {"id": "delete_discovery_devicecomponent", "object": "discovery:devicecomponent", "operation": "delete", "name": "Delete discovery:devicecomponent", "description": "Delete discovery:devicecomponent"}, {"id": "list_discovery_deviceinterface", "object": "discovery:deviceinterface", "operation": "list", "name": "List discovery:deviceinterface", "description": "List/search discovery:deviceinterface objects with filters"}, {"id": "get_discovery_deviceinterface", "object": "discovery:deviceinterface", "operation": "get", "name": "Get discovery:deviceinterface", "description": "Get specific discovery:deviceinterface by reference"}, {"id": "create_discovery_deviceinterface", "object": "discovery:deviceinterface", "operation": "create", "name": "Create discovery:deviceinterface", "description": "Create new discovery:deviceinterface"}, {"id": "update_discovery_deviceinterface", "object": "discovery:deviceinterface", "operation": "update", "name": "Update discovery:deviceinterface", "description": "Update existing discovery:deviceinterface"}, {"id": "delete_discovery_deviceinterface", "object": "discovery:deviceinterface", "operation": "delete", "name": "Delete discovery:deviceinterface", "description": "Delete discovery:deviceinterface"}, {"id": "list_discovery_deviceneighbor", "object": "discovery:<PERSON><PERSON><PERSON><PERSON>", "operation": "list", "name": "List discovery:<PERSON><PERSON><PERSON><PERSON>", "description": "List/search discovery:deviceneighbor objects with filters"}, {"id": "get_discovery_deviceneighbor", "object": "discovery:<PERSON><PERSON><PERSON><PERSON>", "operation": "get", "name": "Get discovery:device<PERSON>igh<PERSON>", "description": "Get specific discovery:deviceneighbor by reference"}, {"id": "create_discovery_deviceneighbor", "object": "discovery:<PERSON><PERSON><PERSON><PERSON>", "operation": "create", "name": "Create discovery:<PERSON><PERSON><PERSON><PERSON>", "description": "Create new discovery:<PERSON><PERSON><PERSON><PERSON>"}, {"id": "update_discovery_deviceneighbor", "object": "discovery:<PERSON><PERSON><PERSON><PERSON>", "operation": "update", "name": "Update discovery:<PERSON><PERSON><PERSON><PERSON>", "description": "Update existing discovery:<PERSON><PERSON><PERSON><PERSON>"}, {"id": "delete_discovery_deviceneighbor", "object": "discovery:<PERSON><PERSON><PERSON><PERSON>", "operation": "delete", "name": "Delete discovery:<PERSON><PERSON><PERSON><PERSON>", "description": "Delete discovery:<PERSON><PERSON><PERSON><PERSON>"}, {"id": "list_discovery_devicesupportbundle", "object": "discovery:devicesupportbundle", "operation": "list", "name": "List discovery:devicesupportbundle", "description": "List/search discovery:devicesupportbundle objects with filters"}, {"id": "get_discovery_devicesupportbundle", "object": "discovery:devicesupportbundle", "operation": "get", "name": "Get discovery:devicesupportbundle", "description": "Get specific discovery:devicesupportbundle by reference"}, {"id": "create_discovery_devicesupportbundle", "object": "discovery:devicesupportbundle", "operation": "create", "name": "Create discovery:devicesupportbundle", "description": "Create new discovery:devicesupportbundle"}, {"id": "update_discovery_devicesupportbundle", "object": "discovery:devicesupportbundle", "operation": "update", "name": "Update discovery:devicesupportbundle", "description": "Update existing discovery:devicesupportbundle"}, {"id": "delete_discovery_devicesupportbundle", "object": "discovery:devicesupportbundle", "operation": "delete", "name": "Delete discovery:devicesupportbundle", "description": "Delete discovery:devicesupportbundle"}, {"id": "list_discovery_diagnostictask", "object": "discovery:diagnostictask", "operation": "list", "name": "List discovery:diagnostictask", "description": "List/search discovery:diagnostictask objects with filters"}, {"id": "get_discovery_diagnostictask", "object": "discovery:diagnostictask", "operation": "get", "name": "Get discovery:diagnostictask", "description": "Get specific discovery:diagnostictask by reference"}, {"id": "create_discovery_diagnostictask", "object": "discovery:diagnostictask", "operation": "create", "name": "Create discovery:diagnostictask", "description": "Create new discovery:diagnostictask"}, {"id": "update_discovery_diagnostictask", "object": "discovery:diagnostictask", "operation": "update", "name": "Update discovery:diagnostictask", "description": "Update existing discovery:diagnostictask"}, {"id": "delete_discovery_diagnostictask", "object": "discovery:diagnostictask", "operation": "delete", "name": "Delete discovery:diagnostictask", "description": "Delete discovery:diagnostictask"}, {"id": "list_discovery_gridproperties", "object": "discovery:gridproperties", "operation": "list", "name": "List discovery:gridproperties", "description": "List/search discovery:gridproperties objects with filters"}, {"id": "get_discovery_gridproperties", "object": "discovery:gridproperties", "operation": "get", "name": "Get discovery:gridproperties", "description": "Get specific discovery:gridproperties by reference"}, {"id": "create_discovery_gridproperties", "object": "discovery:gridproperties", "operation": "create", "name": "Create discovery:gridproperties", "description": "Create new discovery:gridproperties"}, {"id": "update_discovery_gridproperties", "object": "discovery:gridproperties", "operation": "update", "name": "Update discovery:gridproperties", "description": "Update existing discovery:gridproperties"}, {"id": "delete_discovery_gridproperties", "object": "discovery:gridproperties", "operation": "delete", "name": "Delete discovery:gridproperties", "description": "Delete discovery:gridproperties"}, {"id": "list_discovery_memberproperties", "object": "discovery:memberproperties", "operation": "list", "name": "List discovery:memberproperties", "description": "List/search discovery:memberproperties objects with filters"}, {"id": "get_discovery_memberproperties", "object": "discovery:memberproperties", "operation": "get", "name": "Get discovery:memberproperties", "description": "Get specific discovery:memberproperties by reference"}, {"id": "create_discovery_memberproperties", "object": "discovery:memberproperties", "operation": "create", "name": "Create discovery:memberproperties", "description": "Create new discovery:memberproperties"}, {"id": "update_discovery_memberproperties", "object": "discovery:memberproperties", "operation": "update", "name": "Update discovery:memberproperties", "description": "Update existing discovery:memberproperties"}, {"id": "delete_discovery_memberproperties", "object": "discovery:memberproperties", "operation": "delete", "name": "Delete discovery:memberproperties", "description": "Delete discovery:memberproperties"}, {"id": "list_discovery_sdnnetwork", "object": "discovery:sdnnetwork", "operation": "list", "name": "List discovery:sdnnetwork", "description": "List/search discovery:sdnnetwork objects with filters"}, {"id": "get_discovery_sdnnetwork", "object": "discovery:sdnnetwork", "operation": "get", "name": "Get discovery:sdnnetwork", "description": "Get specific discovery:sdnnetwork by reference"}, {"id": "create_discovery_sdnnetwork", "object": "discovery:sdnnetwork", "operation": "create", "name": "Create discovery:sdnnetwork", "description": "Create new discovery:sdnnetwork"}, {"id": "update_discovery_sdnnetwork", "object": "discovery:sdnnetwork", "operation": "update", "name": "Update discovery:sdnnetwork", "description": "Update existing discovery:sdnnetwork"}, {"id": "delete_discovery_sdnnetwork", "object": "discovery:sdnnetwork", "operation": "delete", "name": "Delete discovery:sdnnetwork", "description": "Delete discovery:sdnnetwork"}, {"id": "list_discovery_status", "object": "discovery:status", "operation": "list", "name": "List discovery:status", "description": "List/search discovery:status objects with filters"}, {"id": "get_discovery_status", "object": "discovery:status", "operation": "get", "name": "Get discovery:status", "description": "Get specific discovery:status by reference"}, {"id": "create_discovery_status", "object": "discovery:status", "operation": "create", "name": "Create discovery:status", "description": "Create new discovery:status"}, {"id": "update_discovery_status", "object": "discovery:status", "operation": "update", "name": "Update discovery:status", "description": "Update existing discovery:status"}, {"id": "delete_discovery_status", "object": "discovery:status", "operation": "delete", "name": "Delete discovery:status", "description": "Delete discovery:status"}, {"id": "list_discovery_vrf", "object": "discovery:vrf", "operation": "list", "name": "List discovery:vrf", "description": "List/search discovery:vrf objects with filters"}, {"id": "get_discovery_vrf", "object": "discovery:vrf", "operation": "get", "name": "Get discovery:vrf", "description": "Get specific discovery:vrf by reference"}, {"id": "create_discovery_vrf", "object": "discovery:vrf", "operation": "create", "name": "Create discovery:vrf", "description": "Create new discovery:vrf"}, {"id": "update_discovery_vrf", "object": "discovery:vrf", "operation": "update", "name": "Update discovery:vrf", "description": "Update existing discovery:vrf"}, {"id": "delete_discovery_vrf", "object": "discovery:vrf", "operation": "delete", "name": "Delete discovery:vrf", "description": "Delete discovery:vrf"}, {"id": "list_discoverytask", "object": "discoverytask", "operation": "list", "name": "List discoverytask", "description": "List/search discoverytask objects with filters"}, {"id": "get_discoverytask", "object": "discoverytask", "operation": "get", "name": "Get discoverytask", "description": "Get specific discoverytask by reference"}, {"id": "create_discoverytask", "object": "discoverytask", "operation": "create", "name": "Create discoverytask", "description": "Create new discoverytask"}, {"id": "update_discoverytask", "object": "discoverytask", "operation": "update", "name": "Update discoverytask", "description": "Update existing discoverytask"}, {"id": "delete_discoverytask", "object": "discoverytask", "operation": "delete", "name": "Delete discoverytask", "description": "Delete discoverytask"}, {"id": "list_distributionschedule", "object": "distributionschedule", "operation": "list", "name": "List distributionschedule", "description": "List/search distributionschedule objects with filters"}, {"id": "get_distributionschedule", "object": "distributionschedule", "operation": "get", "name": "Get distributionschedule", "description": "Get specific distributionschedule by reference"}, {"id": "create_distributionschedule", "object": "distributionschedule", "operation": "create", "name": "Create distributionschedule", "description": "Create new distributionschedule"}, {"id": "update_distributionschedule", "object": "distributionschedule", "operation": "update", "name": "Update distributionschedule", "description": "Update existing distributionschedule"}, {"id": "delete_distributionschedule", "object": "distributionschedule", "operation": "delete", "name": "Delete distributionschedule", "description": "Delete distributionschedule"}, {"id": "list_dns64group", "object": "dns64group", "operation": "list", "name": "List dns64group", "description": "List/search dns64group objects with filters"}, {"id": "get_dns64group", "object": "dns64group", "operation": "get", "name": "Get dns64group", "description": "Get specific dns64group by reference"}, {"id": "create_dns64group", "object": "dns64group", "operation": "create", "name": "Create dns64group", "description": "Create new dns64group"}, {"id": "update_dns64group", "object": "dns64group", "operation": "update", "name": "Update dns64group", "description": "Update existing dns64group"}, {"id": "delete_dns64group", "object": "dns64group", "operation": "delete", "name": "Delete dns64group", "description": "Delete dns64group"}, {"id": "list_dtc", "object": "dtc", "operation": "list", "name": "List dtc", "description": "List/search dtc objects with filters"}, {"id": "get_dtc", "object": "dtc", "operation": "get", "name": "Get dtc", "description": "Get specific dtc by reference"}, {"id": "create_dtc", "object": "dtc", "operation": "create", "name": "Create dtc", "description": "Create new dtc"}, {"id": "update_dtc", "object": "dtc", "operation": "update", "name": "Update dtc", "description": "Update existing dtc"}, {"id": "delete_dtc", "object": "dtc", "operation": "delete", "name": "Delete dtc", "description": "Delete dtc"}, {"id": "list_dtc_allrecords", "object": "dtc:allrecords", "operation": "list", "name": "List dtc:allrecords", "description": "List/search dtc:allrecords objects with filters"}, {"id": "get_dtc_allrecords", "object": "dtc:allrecords", "operation": "get", "name": "Get dtc:allrecords", "description": "Get specific dtc:allrecords by reference"}, {"id": "create_dtc_allrecords", "object": "dtc:allrecords", "operation": "create", "name": "Create dtc:allrecords", "description": "Create new dtc:allrecords"}, {"id": "update_dtc_allrecords", "object": "dtc:allrecords", "operation": "update", "name": "Update dtc:allrecords", "description": "Update existing dtc:allrecords"}, {"id": "delete_dtc_allrecords", "object": "dtc:allrecords", "operation": "delete", "name": "Delete dtc:allrecords", "description": "Delete dtc:allrecords"}, {"id": "list_dtc_certificate", "object": "dtc:certificate", "operation": "list", "name": "List dtc:certificate", "description": "List/search dtc:certificate objects with filters"}, {"id": "get_dtc_certificate", "object": "dtc:certificate", "operation": "get", "name": "Get dtc:certificate", "description": "Get specific dtc:certificate by reference"}, {"id": "create_dtc_certificate", "object": "dtc:certificate", "operation": "create", "name": "Create dtc:certificate", "description": "Create new dtc:certificate"}, {"id": "update_dtc_certificate", "object": "dtc:certificate", "operation": "update", "name": "Update dtc:certificate", "description": "Update existing dtc:certificate"}, {"id": "delete_dtc_certificate", "object": "dtc:certificate", "operation": "delete", "name": "Delete dtc:certificate", "description": "Delete dtc:certificate"}, {"id": "list_dtc_lbdn", "object": "dtc:lbdn", "operation": "list", "name": "List dtc:lbdn", "description": "List/search dtc:lbdn objects with filters"}, {"id": "get_dtc_lbdn", "object": "dtc:lbdn", "operation": "get", "name": "Get dtc:lbdn", "description": "Get specific dtc:lbdn by reference"}, {"id": "create_dtc_lbdn", "object": "dtc:lbdn", "operation": "create", "name": "Create dtc:lbdn", "description": "Create new dtc:lbdn"}, {"id": "update_dtc_lbdn", "object": "dtc:lbdn", "operation": "update", "name": "Update dtc:lbdn", "description": "Update existing dtc:lbdn"}, {"id": "delete_dtc_lbdn", "object": "dtc:lbdn", "operation": "delete", "name": "Delete dtc:lbdn", "description": "Delete dtc:lbdn"}, {"id": "list_dtc_monitor", "object": "dtc:monitor", "operation": "list", "name": "List dtc:monitor", "description": "List/search dtc:monitor objects with filters"}, {"id": "get_dtc_monitor", "object": "dtc:monitor", "operation": "get", "name": "Get dtc:monitor", "description": "Get specific dtc:monitor by reference"}, {"id": "create_dtc_monitor", "object": "dtc:monitor", "operation": "create", "name": "Create dtc:monitor", "description": "Create new dtc:monitor"}, {"id": "update_dtc_monitor", "object": "dtc:monitor", "operation": "update", "name": "Update dtc:monitor", "description": "Update existing dtc:monitor"}, {"id": "delete_dtc_monitor", "object": "dtc:monitor", "operation": "delete", "name": "Delete dtc:monitor", "description": "Delete dtc:monitor"}, {"id": "list_dtc_monitor_http", "object": "dtc:monitor:http", "operation": "list", "name": "List dtc:monitor:http", "description": "List/search dtc:monitor:http objects with filters"}, {"id": "get_dtc_monitor_http", "object": "dtc:monitor:http", "operation": "get", "name": "Get dtc:monitor:http", "description": "Get specific dtc:monitor:http by reference"}, {"id": "create_dtc_monitor_http", "object": "dtc:monitor:http", "operation": "create", "name": "Create dtc:monitor:http", "description": "Create new dtc:monitor:http"}, {"id": "update_dtc_monitor_http", "object": "dtc:monitor:http", "operation": "update", "name": "Update dtc:monitor:http", "description": "Update existing dtc:monitor:http"}, {"id": "delete_dtc_monitor_http", "object": "dtc:monitor:http", "operation": "delete", "name": "Delete dtc:monitor:http", "description": "Delete dtc:monitor:http"}, {"id": "list_dtc_monitor_icmp", "object": "dtc:monitor:icmp", "operation": "list", "name": "List dtc:monitor:icmp", "description": "List/search dtc:monitor:icmp objects with filters"}, {"id": "get_dtc_monitor_icmp", "object": "dtc:monitor:icmp", "operation": "get", "name": "Get dtc:monitor:icmp", "description": "Get specific dtc:monitor:icmp by reference"}, {"id": "create_dtc_monitor_icmp", "object": "dtc:monitor:icmp", "operation": "create", "name": "Create dtc:monitor:icmp", "description": "Create new dtc:monitor:icmp"}, {"id": "update_dtc_monitor_icmp", "object": "dtc:monitor:icmp", "operation": "update", "name": "Update dtc:monitor:icmp", "description": "Update existing dtc:monitor:icmp"}, {"id": "delete_dtc_monitor_icmp", "object": "dtc:monitor:icmp", "operation": "delete", "name": "Delete dtc:monitor:icmp", "description": "Delete dtc:monitor:icmp"}, {"id": "list_dtc_monitor_pdp", "object": "dtc:monitor:pdp", "operation": "list", "name": "List dtc:monitor:pdp", "description": "List/search dtc:monitor:pdp objects with filters"}, {"id": "get_dtc_monitor_pdp", "object": "dtc:monitor:pdp", "operation": "get", "name": "Get dtc:monitor:pdp", "description": "Get specific dtc:monitor:pdp by reference"}, {"id": "create_dtc_monitor_pdp", "object": "dtc:monitor:pdp", "operation": "create", "name": "Create dtc:monitor:pdp", "description": "Create new dtc:monitor:pdp"}, {"id": "update_dtc_monitor_pdp", "object": "dtc:monitor:pdp", "operation": "update", "name": "Update dtc:monitor:pdp", "description": "Update existing dtc:monitor:pdp"}, {"id": "delete_dtc_monitor_pdp", "object": "dtc:monitor:pdp", "operation": "delete", "name": "Delete dtc:monitor:pdp", "description": "Delete dtc:monitor:pdp"}, {"id": "list_dtc_monitor_sip", "object": "dtc:monitor:sip", "operation": "list", "name": "List dtc:monitor:sip", "description": "List/search dtc:monitor:sip objects with filters"}, {"id": "get_dtc_monitor_sip", "object": "dtc:monitor:sip", "operation": "get", "name": "Get dtc:monitor:sip", "description": "Get specific dtc:monitor:sip by reference"}, {"id": "create_dtc_monitor_sip", "object": "dtc:monitor:sip", "operation": "create", "name": "Create dtc:monitor:sip", "description": "Create new dtc:monitor:sip"}, {"id": "update_dtc_monitor_sip", "object": "dtc:monitor:sip", "operation": "update", "name": "Update dtc:monitor:sip", "description": "Update existing dtc:monitor:sip"}, {"id": "delete_dtc_monitor_sip", "object": "dtc:monitor:sip", "operation": "delete", "name": "Delete dtc:monitor:sip", "description": "Delete dtc:monitor:sip"}, {"id": "list_dtc_monitor_snmp", "object": "dtc:monitor:snmp", "operation": "list", "name": "List dtc:monitor:snmp", "description": "List/search dtc:monitor:snmp objects with filters"}, {"id": "get_dtc_monitor_snmp", "object": "dtc:monitor:snmp", "operation": "get", "name": "Get dtc:monitor:snmp", "description": "Get specific dtc:monitor:snmp by reference"}, {"id": "create_dtc_monitor_snmp", "object": "dtc:monitor:snmp", "operation": "create", "name": "Create dtc:monitor:snmp", "description": "Create new dtc:monitor:snmp"}, {"id": "update_dtc_monitor_snmp", "object": "dtc:monitor:snmp", "operation": "update", "name": "Update dtc:monitor:snmp", "description": "Update existing dtc:monitor:snmp"}, {"id": "delete_dtc_monitor_snmp", "object": "dtc:monitor:snmp", "operation": "delete", "name": "Delete dtc:monitor:snmp", "description": "Delete dtc:monitor:snmp"}, {"id": "list_dtc_monitor_tcp", "object": "dtc:monitor:tcp", "operation": "list", "name": "List dtc:monitor:tcp", "description": "List/search dtc:monitor:tcp objects with filters"}, {"id": "get_dtc_monitor_tcp", "object": "dtc:monitor:tcp", "operation": "get", "name": "Get dtc:monitor:tcp", "description": "Get specific dtc:monitor:tcp by reference"}, {"id": "create_dtc_monitor_tcp", "object": "dtc:monitor:tcp", "operation": "create", "name": "Create dtc:monitor:tcp", "description": "Create new dtc:monitor:tcp"}, {"id": "update_dtc_monitor_tcp", "object": "dtc:monitor:tcp", "operation": "update", "name": "Update dtc:monitor:tcp", "description": "Update existing dtc:monitor:tcp"}, {"id": "delete_dtc_monitor_tcp", "object": "dtc:monitor:tcp", "operation": "delete", "name": "Delete dtc:monitor:tcp", "description": "Delete dtc:monitor:tcp"}, {"id": "list_dtc_object", "object": "dtc:object", "operation": "list", "name": "List dtc:object", "description": "List/search dtc:object objects with filters"}, {"id": "get_dtc_object", "object": "dtc:object", "operation": "get", "name": "Get dtc:object", "description": "Get specific dtc:object by reference"}, {"id": "create_dtc_object", "object": "dtc:object", "operation": "create", "name": "Create dtc:object", "description": "Create new dtc:object"}, {"id": "update_dtc_object", "object": "dtc:object", "operation": "update", "name": "Update dtc:object", "description": "Update existing dtc:object"}, {"id": "delete_dtc_object", "object": "dtc:object", "operation": "delete", "name": "Delete dtc:object", "description": "Delete dtc:object"}, {"id": "list_dtc_pool", "object": "dtc:pool", "operation": "list", "name": "List dtc:pool", "description": "List/search dtc:pool objects with filters"}, {"id": "get_dtc_pool", "object": "dtc:pool", "operation": "get", "name": "Get dtc:pool", "description": "Get specific dtc:pool by reference"}, {"id": "create_dtc_pool", "object": "dtc:pool", "operation": "create", "name": "Create dtc:pool", "description": "Create new dtc:pool"}, {"id": "update_dtc_pool", "object": "dtc:pool", "operation": "update", "name": "Update dtc:pool", "description": "Update existing dtc:pool"}, {"id": "delete_dtc_pool", "object": "dtc:pool", "operation": "delete", "name": "Delete dtc:pool", "description": "Delete dtc:pool"}, {"id": "list_dtc_record_a", "object": "dtc:record:a", "operation": "list", "name": "List dtc:record:a", "description": "List/search dtc:record:a objects with filters"}, {"id": "get_dtc_record_a", "object": "dtc:record:a", "operation": "get", "name": "Get dtc:record:a", "description": "Get specific dtc:record:a by reference"}, {"id": "create_dtc_record_a", "object": "dtc:record:a", "operation": "create", "name": "Create dtc:record:a", "description": "Create new dtc:record:a"}, {"id": "update_dtc_record_a", "object": "dtc:record:a", "operation": "update", "name": "Update dtc:record:a", "description": "Update existing dtc:record:a"}, {"id": "delete_dtc_record_a", "object": "dtc:record:a", "operation": "delete", "name": "Delete dtc:record:a", "description": "Delete dtc:record:a"}, {"id": "list_dtc_record_aaaa", "object": "dtc:record:aaaa", "operation": "list", "name": "List dtc:record:aaaa", "description": "List/search dtc:record:aaaa objects with filters"}, {"id": "get_dtc_record_aaaa", "object": "dtc:record:aaaa", "operation": "get", "name": "Get dtc:record:aaaa", "description": "Get specific dtc:record:aaaa by reference"}, {"id": "create_dtc_record_aaaa", "object": "dtc:record:aaaa", "operation": "create", "name": "Create dtc:record:aaaa", "description": "Create new dtc:record:aaaa"}, {"id": "update_dtc_record_aaaa", "object": "dtc:record:aaaa", "operation": "update", "name": "Update dtc:record:aaaa", "description": "Update existing dtc:record:aaaa"}, {"id": "delete_dtc_record_aaaa", "object": "dtc:record:aaaa", "operation": "delete", "name": "Delete dtc:record:aaaa", "description": "Delete dtc:record:aaaa"}, {"id": "list_dtc_record_cname", "object": "dtc:record:cname", "operation": "list", "name": "List dtc:record:cname", "description": "List/search dtc:record:cname objects with filters"}, {"id": "get_dtc_record_cname", "object": "dtc:record:cname", "operation": "get", "name": "Get dtc:record:cname", "description": "Get specific dtc:record:cname by reference"}, {"id": "create_dtc_record_cname", "object": "dtc:record:cname", "operation": "create", "name": "Create dtc:record:cname", "description": "Create new dtc:record:cname"}, {"id": "update_dtc_record_cname", "object": "dtc:record:cname", "operation": "update", "name": "Update dtc:record:cname", "description": "Update existing dtc:record:cname"}, {"id": "delete_dtc_record_cname", "object": "dtc:record:cname", "operation": "delete", "name": "Delete dtc:record:cname", "description": "Delete dtc:record:cname"}, {"id": "list_dtc_record_naptr", "object": "dtc:record:naptr", "operation": "list", "name": "List dtc:record:naptr", "description": "List/search dtc:record:naptr objects with filters"}, {"id": "get_dtc_record_naptr", "object": "dtc:record:naptr", "operation": "get", "name": "Get dtc:record:naptr", "description": "Get specific dtc:record:naptr by reference"}, {"id": "create_dtc_record_naptr", "object": "dtc:record:naptr", "operation": "create", "name": "Create dtc:record:naptr", "description": "Create new dtc:record:naptr"}, {"id": "update_dtc_record_naptr", "object": "dtc:record:naptr", "operation": "update", "name": "Update dtc:record:naptr", "description": "Update existing dtc:record:naptr"}, {"id": "delete_dtc_record_naptr", "object": "dtc:record:naptr", "operation": "delete", "name": "Delete dtc:record:naptr", "description": "Delete dtc:record:naptr"}, {"id": "list_dtc_record_srv", "object": "dtc:record:srv", "operation": "list", "name": "List dtc:record:srv", "description": "List/search dtc:record:srv objects with filters"}, {"id": "get_dtc_record_srv", "object": "dtc:record:srv", "operation": "get", "name": "Get dtc:record:srv", "description": "Get specific dtc:record:srv by reference"}, {"id": "create_dtc_record_srv", "object": "dtc:record:srv", "operation": "create", "name": "Create dtc:record:srv", "description": "Create new dtc:record:srv"}, {"id": "update_dtc_record_srv", "object": "dtc:record:srv", "operation": "update", "name": "Update dtc:record:srv", "description": "Update existing dtc:record:srv"}, {"id": "delete_dtc_record_srv", "object": "dtc:record:srv", "operation": "delete", "name": "Delete dtc:record:srv", "description": "Delete dtc:record:srv"}, {"id": "list_dtc_server", "object": "dtc:server", "operation": "list", "name": "List dtc:server", "description": "List/search dtc:server objects with filters"}, {"id": "get_dtc_server", "object": "dtc:server", "operation": "get", "name": "Get dtc:server", "description": "Get specific dtc:server by reference"}, {"id": "create_dtc_server", "object": "dtc:server", "operation": "create", "name": "Create dtc:server", "description": "Create new dtc:server"}, {"id": "update_dtc_server", "object": "dtc:server", "operation": "update", "name": "Update dtc:server", "description": "Update existing dtc:server"}, {"id": "delete_dtc_server", "object": "dtc:server", "operation": "delete", "name": "Delete dtc:server", "description": "Delete dtc:server"}, {"id": "list_dtc_topology", "object": "dtc:topology", "operation": "list", "name": "List dtc:topology", "description": "List/search dtc:topology objects with filters"}, {"id": "get_dtc_topology", "object": "dtc:topology", "operation": "get", "name": "Get dtc:topology", "description": "Get specific dtc:topology by reference"}, {"id": "create_dtc_topology", "object": "dtc:topology", "operation": "create", "name": "Create dtc:topology", "description": "Create new dtc:topology"}, {"id": "update_dtc_topology", "object": "dtc:topology", "operation": "update", "name": "Update dtc:topology", "description": "Update existing dtc:topology"}, {"id": "delete_dtc_topology", "object": "dtc:topology", "operation": "delete", "name": "Delete dtc:topology", "description": "Delete dtc:topology"}, {"id": "list_dtc_topology_label", "object": "dtc:topology:label", "operation": "list", "name": "List dtc:topology:label", "description": "List/search dtc:topology:label objects with filters"}, {"id": "get_dtc_topology_label", "object": "dtc:topology:label", "operation": "get", "name": "Get dtc:topology:label", "description": "Get specific dtc:topology:label by reference"}, {"id": "create_dtc_topology_label", "object": "dtc:topology:label", "operation": "create", "name": "Create dtc:topology:label", "description": "Create new dtc:topology:label"}, {"id": "update_dtc_topology_label", "object": "dtc:topology:label", "operation": "update", "name": "Update dtc:topology:label", "description": "Update existing dtc:topology:label"}, {"id": "delete_dtc_topology_label", "object": "dtc:topology:label", "operation": "delete", "name": "Delete dtc:topology:label", "description": "Delete dtc:topology:label"}, {"id": "list_dtc_topology_rule", "object": "dtc:topology:rule", "operation": "list", "name": "List dtc:topology:rule", "description": "List/search dtc:topology:rule objects with filters"}, {"id": "get_dtc_topology_rule", "object": "dtc:topology:rule", "operation": "get", "name": "Get dtc:topology:rule", "description": "Get specific dtc:topology:rule by reference"}, {"id": "create_dtc_topology_rule", "object": "dtc:topology:rule", "operation": "create", "name": "Create dtc:topology:rule", "description": "Create new dtc:topology:rule"}, {"id": "update_dtc_topology_rule", "object": "dtc:topology:rule", "operation": "update", "name": "Update dtc:topology:rule", "description": "Update existing dtc:topology:rule"}, {"id": "delete_dtc_topology_rule", "object": "dtc:topology:rule", "operation": "delete", "name": "Delete dtc:topology:rule", "description": "Delete dtc:topology:rule"}, {"id": "list_dxl_endpoint", "object": "dxl:endpoint", "operation": "list", "name": "List dxl:endpoint", "description": "List/search dxl:endpoint objects with filters"}, {"id": "get_dxl_endpoint", "object": "dxl:endpoint", "operation": "get", "name": "Get dxl:endpoint", "description": "Get specific dxl:endpoint by reference"}, {"id": "create_dxl_endpoint", "object": "dxl:endpoint", "operation": "create", "name": "Create dxl:endpoint", "description": "Create new dxl:endpoint"}, {"id": "update_dxl_endpoint", "object": "dxl:endpoint", "operation": "update", "name": "Update dxl:endpoint", "description": "Update existing dxl:endpoint"}, {"id": "delete_dxl_endpoint", "object": "dxl:endpoint", "operation": "delete", "name": "Delete dxl:endpoint", "description": "Delete dxl:endpoint"}, {"id": "list_extensibleattributedef", "object": "extensibleattributedef", "operation": "list", "name": "List extensibleattributedef", "description": "List/search extensibleattributedef objects with filters"}, {"id": "get_extensibleattributedef", "object": "extensibleattributedef", "operation": "get", "name": "Get extensibleattributedef", "description": "Get specific extensibleattributedef by reference"}, {"id": "create_extensibleattributedef", "object": "extensibleattributedef", "operation": "create", "name": "Create extensibleattributedef", "description": "Create new extensibleattributedef"}, {"id": "update_extensibleattributedef", "object": "extensibleattributedef", "operation": "update", "name": "Update extensibleattributedef", "description": "Update existing extensibleattributedef"}, {"id": "delete_extensibleattributedef", "object": "extensibleattributedef", "operation": "delete", "name": "Delete extensibleattributedef", "description": "Delete extensibleattributedef"}, {"id": "list_fileop", "object": "fileop", "operation": "list", "name": "List fileop", "description": "List/search fileop objects with filters"}, {"id": "get_fileop", "object": "fileop", "operation": "get", "name": "Get fileop", "description": "Get specific fileop by reference"}, {"id": "create_fileop", "object": "fileop", "operation": "create", "name": "Create fileop", "description": "Create new fileop"}, {"id": "update_fileop", "object": "fileop", "operation": "update", "name": "Update fileop", "description": "Update existing fileop"}, {"id": "delete_fileop", "object": "fileop", "operation": "delete", "name": "Delete fileop", "description": "Delete fileop"}, {"id": "list_filterfingerprint", "object": "filterfingerprint", "operation": "list", "name": "List filterfingerprint", "description": "List/search filterfingerprint objects with filters"}, {"id": "get_filterfingerprint", "object": "filterfingerprint", "operation": "get", "name": "Get filterfingerprint", "description": "Get specific filterfingerprint by reference"}, {"id": "create_filterfingerprint", "object": "filterfingerprint", "operation": "create", "name": "Create filterfingerprint", "description": "Create new filterfingerprint"}, {"id": "update_filterfingerprint", "object": "filterfingerprint", "operation": "update", "name": "Update filterfingerprint", "description": "Update existing filterfingerprint"}, {"id": "delete_filterfingerprint", "object": "filterfingerprint", "operation": "delete", "name": "Delete filterfingerprint", "description": "Delete filterfingerprint"}, {"id": "list_filtermac", "object": "filtermac", "operation": "list", "name": "List filtermac", "description": "List/search filtermac objects with filters"}, {"id": "get_filtermac", "object": "filtermac", "operation": "get", "name": "Get filtermac", "description": "Get specific filtermac by reference"}, {"id": "create_filtermac", "object": "filtermac", "operation": "create", "name": "Create filtermac", "description": "Create new filtermac"}, {"id": "update_filtermac", "object": "filtermac", "operation": "update", "name": "Update filtermac", "description": "Update existing filtermac"}, {"id": "delete_filtermac", "object": "filtermac", "operation": "delete", "name": "Delete filtermac", "description": "Delete filtermac"}, {"id": "list_filternac", "object": "filternac", "operation": "list", "name": "List filternac", "description": "List/search filternac objects with filters"}, {"id": "get_filternac", "object": "filternac", "operation": "get", "name": "Get filternac", "description": "Get specific filternac by reference"}, {"id": "create_filternac", "object": "filternac", "operation": "create", "name": "Create filternac", "description": "Create new filternac"}, {"id": "update_filternac", "object": "filternac", "operation": "update", "name": "Update filternac", "description": "Update existing filternac"}, {"id": "delete_filternac", "object": "filternac", "operation": "delete", "name": "Delete filternac", "description": "Delete filternac"}, {"id": "list_filteroption", "object": "filteroption", "operation": "list", "name": "List filteroption", "description": "List/search filteroption objects with filters"}, {"id": "get_filteroption", "object": "filteroption", "operation": "get", "name": "Get filteroption", "description": "Get specific filteroption by reference"}, {"id": "create_filteroption", "object": "filteroption", "operation": "create", "name": "Create filteroption", "description": "Create new filteroption"}, {"id": "update_filteroption", "object": "filteroption", "operation": "update", "name": "Update filteroption", "description": "Update existing filteroption"}, {"id": "delete_filteroption", "object": "filteroption", "operation": "delete", "name": "Delete filteroption", "description": "Delete filteroption"}, {"id": "list_filterrelayagent", "object": "filterrelayagent", "operation": "list", "name": "List filterrelayagent", "description": "List/search filterrelayagent objects with filters"}, {"id": "get_filterrelayagent", "object": "filterrelayagent", "operation": "get", "name": "Get filterrelayagent", "description": "Get specific filterrelayagent by reference"}, {"id": "create_filterrelayagent", "object": "filterrelayagent", "operation": "create", "name": "Create filterrelayagent", "description": "Create new filterrelayagent"}, {"id": "update_filterrelayagent", "object": "filterrelayagent", "operation": "update", "name": "Update filterrelayagent", "description": "Update existing filterrelayagent"}, {"id": "delete_filterrelayagent", "object": "filterrelayagent", "operation": "delete", "name": "Delete filterrelayagent", "description": "Delete filterrelayagent"}, {"id": "list_fingerprint", "object": "fingerprint", "operation": "list", "name": "List fingerprint", "description": "List/search fingerprint objects with filters"}, {"id": "get_fingerprint", "object": "fingerprint", "operation": "get", "name": "Get fingerprint", "description": "Get specific fingerprint by reference"}, {"id": "create_fingerprint", "object": "fingerprint", "operation": "create", "name": "Create fingerprint", "description": "Create new fingerprint"}, {"id": "update_fingerprint", "object": "fingerprint", "operation": "update", "name": "Update fingerprint", "description": "Update existing fingerprint"}, {"id": "delete_fingerprint", "object": "fingerprint", "operation": "delete", "name": "Delete fingerprint", "description": "Delete fingerprint"}, {"id": "list_fixedaddress", "object": "fixedaddress", "operation": "list", "name": "List fixedaddress", "description": "List/search fixedaddress objects with filters"}, {"id": "get_fixedaddress", "object": "fixedaddress", "operation": "get", "name": "Get fixedaddress", "description": "Get specific fixedaddress by reference"}, {"id": "create_fixedaddress", "object": "fixedaddress", "operation": "create", "name": "Create fixedaddress", "description": "Create new fixedaddress"}, {"id": "update_fixedaddress", "object": "fixedaddress", "operation": "update", "name": "Update fixedaddress", "description": "Update existing fixedaddress"}, {"id": "delete_fixedaddress", "object": "fixedaddress", "operation": "delete", "name": "Delete fixedaddress", "description": "Delete fixedaddress"}, {"id": "list_fixedaddresstemplate", "object": "fixedaddresstemplate", "operation": "list", "name": "List fixedaddresstemplate", "description": "List/search fixedaddresstemplate objects with filters"}, {"id": "get_fixedaddresstemplate", "object": "fixedaddresstemplate", "operation": "get", "name": "Get fixedaddresstemplate", "description": "Get specific fixedaddresstemplate by reference"}, {"id": "create_fixedaddresstemplate", "object": "fixedaddresstemplate", "operation": "create", "name": "Create fixedaddresstemplate", "description": "Create new fixedaddresstemplate"}, {"id": "update_fixedaddresstemplate", "object": "fixedaddresstemplate", "operation": "update", "name": "Update fixedaddresstemplate", "description": "Update existing fixedaddresstemplate"}, {"id": "delete_fixedaddresstemplate", "object": "fixedaddresstemplate", "operation": "delete", "name": "Delete fixedaddresstemplate", "description": "Delete fixedaddresstemplate"}, {"id": "list_ftpuser", "object": "ftpuser", "operation": "list", "name": "List ftpuser", "description": "List/search ftpuser objects with filters"}, {"id": "get_ftpuser", "object": "ftpuser", "operation": "get", "name": "Get ftpuser", "description": "Get specific ftpuser by reference"}, {"id": "create_ftpuser", "object": "ftpuser", "operation": "create", "name": "Create ftpuser", "description": "Create new ftpuser"}, {"id": "update_ftpuser", "object": "ftpuser", "operation": "update", "name": "Update ftpuser", "description": "Update existing ftpuser"}, {"id": "delete_ftpuser", "object": "ftpuser", "operation": "delete", "name": "Delete ftpuser", "description": "Delete ftpuser"}, {"id": "list_gmcgroup", "object": "gmcgroup", "operation": "list", "name": "List gmcgroup", "description": "List/search gmcgroup objects with filters"}, {"id": "get_gmcgroup", "object": "gmcgroup", "operation": "get", "name": "Get gmcgroup", "description": "Get specific gmcgroup by reference"}, {"id": "create_gmcgroup", "object": "gmcgroup", "operation": "create", "name": "Create gmcgroup", "description": "Create new gmcgroup"}, {"id": "update_gmcgroup", "object": "gmcgroup", "operation": "update", "name": "Update gmcgroup", "description": "Update existing gmcgroup"}, {"id": "delete_gmcgroup", "object": "gmcgroup", "operation": "delete", "name": "Delete gmcgroup", "description": "Delete gmcgroup"}, {"id": "list_gmcschedule", "object": "gmcschedule", "operation": "list", "name": "List gmcschedule", "description": "List/search gmcschedule objects with filters"}, {"id": "get_gmcschedule", "object": "gmcschedule", "operation": "get", "name": "Get gmcschedule", "description": "Get specific gmcschedule by reference"}, {"id": "create_gmcschedule", "object": "gmcschedule", "operation": "create", "name": "Create gmcschedule", "description": "Create new gmcschedule"}, {"id": "update_gmcschedule", "object": "gmcschedule", "operation": "update", "name": "Update gmcschedule", "description": "Update existing gmcschedule"}, {"id": "delete_gmcschedule", "object": "gmcschedule", "operation": "delete", "name": "Delete gmcschedule", "description": "Delete gmcschedule"}, {"id": "list_grid", "object": "grid", "operation": "list", "name": "List grid", "description": "List/search grid objects with filters"}, {"id": "get_grid", "object": "grid", "operation": "get", "name": "Get grid", "description": "Get specific grid by reference"}, {"id": "create_grid", "object": "grid", "operation": "create", "name": "Create grid", "description": "Create new grid"}, {"id": "update_grid", "object": "grid", "operation": "update", "name": "Update grid", "description": "Update existing grid"}, {"id": "delete_grid", "object": "grid", "operation": "delete", "name": "Delete grid", "description": "Delete grid"}, {"id": "list_grid_cloudapi", "object": "grid:cloudapi", "operation": "list", "name": "List grid:cloudapi", "description": "List/search grid:cloudapi objects with filters"}, {"id": "get_grid_cloudapi", "object": "grid:cloudapi", "operation": "get", "name": "Get grid:cloudapi", "description": "Get specific grid:cloudapi by reference"}, {"id": "create_grid_cloudapi", "object": "grid:cloudapi", "operation": "create", "name": "Create grid:cloudapi", "description": "Create new grid:cloudapi"}, {"id": "update_grid_cloudapi", "object": "grid:cloudapi", "operation": "update", "name": "Update grid:cloudapi", "description": "Update existing grid:cloudapi"}, {"id": "delete_grid_cloudapi", "object": "grid:cloudapi", "operation": "delete", "name": "Delete grid:cloudapi", "description": "Delete grid:cloudapi"}, {"id": "list_grid_cloudapi_cloudstatistics", "object": "grid:cloudapi:cloudstatistics", "operation": "list", "name": "List grid:cloudapi:cloudstatistics", "description": "List/search grid:cloudapi:cloudstatistics objects with filters"}, {"id": "get_grid_cloudapi_cloudstatistics", "object": "grid:cloudapi:cloudstatistics", "operation": "get", "name": "Get grid:cloudapi:cloudstatistics", "description": "Get specific grid:cloudapi:cloudstatistics by reference"}, {"id": "create_grid_cloudapi_cloudstatistics", "object": "grid:cloudapi:cloudstatistics", "operation": "create", "name": "Create grid:cloudapi:cloudstatistics", "description": "Create new grid:cloudapi:cloudstatistics"}, {"id": "update_grid_cloudapi_cloudstatistics", "object": "grid:cloudapi:cloudstatistics", "operation": "update", "name": "Update grid:cloudapi:cloudstatistics", "description": "Update existing grid:cloudapi:cloudstatistics"}, {"id": "delete_grid_cloudapi_cloudstatistics", "object": "grid:cloudapi:cloudstatistics", "operation": "delete", "name": "Delete grid:cloudapi:cloudstatistics", "description": "Delete grid:cloudapi:cloudstatistics"}, {"id": "list_grid_cloudapi_tenant", "object": "grid:cloudapi:tenant", "operation": "list", "name": "List grid:cloudapi:tenant", "description": "List/search grid:cloudapi:tenant objects with filters"}, {"id": "get_grid_cloudapi_tenant", "object": "grid:cloudapi:tenant", "operation": "get", "name": "Get grid:cloudapi:tenant", "description": "Get specific grid:cloudapi:tenant by reference"}, {"id": "create_grid_cloudapi_tenant", "object": "grid:cloudapi:tenant", "operation": "create", "name": "Create grid:cloudapi:tenant", "description": "Create new grid:cloudapi:tenant"}, {"id": "update_grid_cloudapi_tenant", "object": "grid:cloudapi:tenant", "operation": "update", "name": "Update grid:cloudapi:tenant", "description": "Update existing grid:cloudapi:tenant"}, {"id": "delete_grid_cloudapi_tenant", "object": "grid:cloudapi:tenant", "operation": "delete", "name": "Delete grid:cloudapi:tenant", "description": "Delete grid:cloudapi:tenant"}, {"id": "list_grid_cloudapi_vm", "object": "grid:cloudapi:vm", "operation": "list", "name": "List grid:cloudapi:vm", "description": "List/search grid:cloudapi:vm objects with filters"}, {"id": "get_grid_cloudapi_vm", "object": "grid:cloudapi:vm", "operation": "get", "name": "Get grid:cloudapi:vm", "description": "Get specific grid:cloudapi:vm by reference"}, {"id": "create_grid_cloudapi_vm", "object": "grid:cloudapi:vm", "operation": "create", "name": "Create grid:cloudapi:vm", "description": "Create new grid:cloudapi:vm"}, {"id": "update_grid_cloudapi_vm", "object": "grid:cloudapi:vm", "operation": "update", "name": "Update grid:cloudapi:vm", "description": "Update existing grid:cloudapi:vm"}, {"id": "delete_grid_cloudapi_vm", "object": "grid:cloudapi:vm", "operation": "delete", "name": "Delete grid:cloudapi:vm", "description": "Delete grid:cloudapi:vm"}, {"id": "list_grid_cloudapi_vmaddress", "object": "grid:cloudapi:vmaddress", "operation": "list", "name": "List grid:cloudapi:vmaddress", "description": "List/search grid:cloudapi:vmaddress objects with filters"}, {"id": "get_grid_cloudapi_vmaddress", "object": "grid:cloudapi:vmaddress", "operation": "get", "name": "Get grid:cloudapi:vmaddress", "description": "Get specific grid:cloudapi:vmaddress by reference"}, {"id": "create_grid_cloudapi_vmaddress", "object": "grid:cloudapi:vmaddress", "operation": "create", "name": "Create grid:cloudapi:vmaddress", "description": "Create new grid:cloudapi:vmaddress"}, {"id": "update_grid_cloudapi_vmaddress", "object": "grid:cloudapi:vmaddress", "operation": "update", "name": "Update grid:cloudapi:vmaddress", "description": "Update existing grid:cloudapi:vmaddress"}, {"id": "delete_grid_cloudapi_vmaddress", "object": "grid:cloudapi:vmaddress", "operation": "delete", "name": "Delete grid:cloudapi:vmaddress", "description": "Delete grid:cloudapi:vmaddress"}, {"id": "list_grid_dashboard", "object": "grid:dashboard", "operation": "list", "name": "List grid:dashboard", "description": "List/search grid:dashboard objects with filters"}, {"id": "get_grid_dashboard", "object": "grid:dashboard", "operation": "get", "name": "Get grid:dashboard", "description": "Get specific grid:dashboard by reference"}, {"id": "create_grid_dashboard", "object": "grid:dashboard", "operation": "create", "name": "Create grid:dashboard", "description": "Create new grid:dashboard"}, {"id": "update_grid_dashboard", "object": "grid:dashboard", "operation": "update", "name": "Update grid:dashboard", "description": "Update existing grid:dashboard"}, {"id": "delete_grid_dashboard", "object": "grid:dashboard", "operation": "delete", "name": "Delete grid:dashboard", "description": "Delete grid:dashboard"}, {"id": "list_grid_dhcpproperties", "object": "grid:dhcpproperties", "operation": "list", "name": "List grid:dhcpproperties", "description": "List/search grid:dhcpproperties objects with filters"}, {"id": "get_grid_dhcpproperties", "object": "grid:dhcpproperties", "operation": "get", "name": "Get grid:dhcpproperties", "description": "Get specific grid:dhcpproperties by reference"}, {"id": "create_grid_dhcpproperties", "object": "grid:dhcpproperties", "operation": "create", "name": "Create grid:dhcpproperties", "description": "Create new grid:dhcpproperties"}, {"id": "update_grid_dhcpproperties", "object": "grid:dhcpproperties", "operation": "update", "name": "Update grid:dhcpproperties", "description": "Update existing grid:dhcpproperties"}, {"id": "delete_grid_dhcpproperties", "object": "grid:dhcpproperties", "operation": "delete", "name": "Delete grid:dhcpproperties", "description": "Delete grid:dhcpproperties"}, {"id": "list_grid_dns", "object": "grid:dns", "operation": "list", "name": "List grid:dns", "description": "List/search grid:dns objects with filters"}, {"id": "get_grid_dns", "object": "grid:dns", "operation": "get", "name": "Get grid:dns", "description": "Get specific grid:dns by reference"}, {"id": "create_grid_dns", "object": "grid:dns", "operation": "create", "name": "Create grid:dns", "description": "Create new grid:dns"}, {"id": "update_grid_dns", "object": "grid:dns", "operation": "update", "name": "Update grid:dns", "description": "Update existing grid:dns"}, {"id": "delete_grid_dns", "object": "grid:dns", "operation": "delete", "name": "Delete grid:dns", "description": "Delete grid:dns"}, {"id": "list_grid_filedistribution", "object": "grid:filedistribution", "operation": "list", "name": "List grid:filedistribution", "description": "List/search grid:filedistribution objects with filters"}, {"id": "get_grid_filedistribution", "object": "grid:filedistribution", "operation": "get", "name": "Get grid:filedistribution", "description": "Get specific grid:filedistribution by reference"}, {"id": "create_grid_filedistribution", "object": "grid:filedistribution", "operation": "create", "name": "Create grid:filedistribution", "description": "Create new grid:filedistribution"}, {"id": "update_grid_filedistribution", "object": "grid:filedistribution", "operation": "update", "name": "Update grid:filedistribution", "description": "Update existing grid:filedistribution"}, {"id": "delete_grid_filedistribution", "object": "grid:filedistribution", "operation": "delete", "name": "Delete grid:filedistribution", "description": "Delete grid:filedistribution"}, {"id": "list_grid_license_pool", "object": "grid:license_pool", "operation": "list", "name": "List grid:license_pool", "description": "List/search grid:license_pool objects with filters"}, {"id": "get_grid_license_pool", "object": "grid:license_pool", "operation": "get", "name": "Get grid:license_pool", "description": "Get specific grid:license_pool by reference"}, {"id": "create_grid_license_pool", "object": "grid:license_pool", "operation": "create", "name": "Create grid:license_pool", "description": "Create new grid:license_pool"}, {"id": "update_grid_license_pool", "object": "grid:license_pool", "operation": "update", "name": "Update grid:license_pool", "description": "Update existing grid:license_pool"}, {"id": "delete_grid_license_pool", "object": "grid:license_pool", "operation": "delete", "name": "Delete grid:license_pool", "description": "Delete grid:license_pool"}, {"id": "list_grid_license_pool_container", "object": "grid:license_pool_container", "operation": "list", "name": "List grid:license_pool_container", "description": "List/search grid:license_pool_container objects with filters"}, {"id": "get_grid_license_pool_container", "object": "grid:license_pool_container", "operation": "get", "name": "Get grid:license_pool_container", "description": "Get specific grid:license_pool_container by reference"}, {"id": "create_grid_license_pool_container", "object": "grid:license_pool_container", "operation": "create", "name": "Create grid:license_pool_container", "description": "Create new grid:license_pool_container"}, {"id": "update_grid_license_pool_container", "object": "grid:license_pool_container", "operation": "update", "name": "Update grid:license_pool_container", "description": "Update existing grid:license_pool_container"}, {"id": "delete_grid_license_pool_container", "object": "grid:license_pool_container", "operation": "delete", "name": "Delete grid:license_pool_container", "description": "Delete grid:license_pool_container"}, {"id": "list_grid_maxminddbinfo", "object": "grid:maxminddbinfo", "operation": "list", "name": "List grid:maxminddbinfo", "description": "List/search grid:maxminddbinfo objects with filters"}, {"id": "get_grid_maxminddbinfo", "object": "grid:maxminddbinfo", "operation": "get", "name": "Get grid:maxminddbinfo", "description": "Get specific grid:maxminddbinfo by reference"}, {"id": "create_grid_maxminddbinfo", "object": "grid:maxminddbinfo", "operation": "create", "name": "Create grid:max<PERSON><PERSON><PERSON><PERSON>", "description": "Create new grid:maxminddbinfo"}, {"id": "update_grid_maxminddbinfo", "object": "grid:maxminddbinfo", "operation": "update", "name": "Update grid:max<PERSON><PERSON>bin<PERSON>", "description": "Update existing grid:maxminddbinfo"}, {"id": "delete_grid_maxminddbinfo", "object": "grid:maxminddbinfo", "operation": "delete", "name": "Delete grid:max<PERSON>dbin<PERSON>", "description": "Delete grid:max<PERSON>dbin<PERSON>"}, {"id": "list_grid_member_cloudapi", "object": "grid:member:cloudapi", "operation": "list", "name": "List grid:member:<PERSON><PERSON><PERSON>", "description": "List/search grid:member:cloudapi objects with filters"}, {"id": "get_grid_member_cloudapi", "object": "grid:member:cloudapi", "operation": "get", "name": "Get grid:member:<PERSON>ap<PERSON>", "description": "Get specific grid:member:cloudapi by reference"}, {"id": "create_grid_member_cloudapi", "object": "grid:member:cloudapi", "operation": "create", "name": "Create grid:member:<PERSON><PERSON><PERSON>", "description": "Create new grid:member:<PERSON>ap<PERSON>"}, {"id": "update_grid_member_cloudapi", "object": "grid:member:cloudapi", "operation": "update", "name": "Update grid:member:<PERSON><PERSON><PERSON>", "description": "Update existing grid:member:<PERSON>ap<PERSON>"}, {"id": "delete_grid_member_cloudapi", "object": "grid:member:cloudapi", "operation": "delete", "name": "Delete grid:member:<PERSON><PERSON><PERSON>", "description": "Delete grid:member:<PERSON><PERSON><PERSON>"}, {"id": "list_grid_servicerestart_group", "object": "grid:servicerestart:group", "operation": "list", "name": "List grid:servicerestart:group", "description": "List/search grid:servicerestart:group objects with filters"}, {"id": "get_grid_servicerestart_group", "object": "grid:servicerestart:group", "operation": "get", "name": "Get grid:servicerestart:group", "description": "Get specific grid:servicerestart:group by reference"}, {"id": "create_grid_servicerestart_group", "object": "grid:servicerestart:group", "operation": "create", "name": "Create grid:servicerestart:group", "description": "Create new grid:servicerestart:group"}, {"id": "update_grid_servicerestart_group", "object": "grid:servicerestart:group", "operation": "update", "name": "Update grid:servicerestart:group", "description": "Update existing grid:servicerestart:group"}, {"id": "delete_grid_servicerestart_group", "object": "grid:servicerestart:group", "operation": "delete", "name": "Delete grid:servicerestart:group", "description": "Delete grid:servicerestart:group"}, {"id": "list_grid_servicerestart_group_order", "object": "grid:servicerestart:group:order", "operation": "list", "name": "List grid:servicerestart:group:order", "description": "List/search grid:servicerestart:group:order objects with filters"}, {"id": "get_grid_servicerestart_group_order", "object": "grid:servicerestart:group:order", "operation": "get", "name": "Get grid:servicerestart:group:order", "description": "Get specific grid:servicerestart:group:order by reference"}, {"id": "create_grid_servicerestart_group_order", "object": "grid:servicerestart:group:order", "operation": "create", "name": "Create grid:servicerestart:group:order", "description": "Create new grid:servicerestart:group:order"}, {"id": "update_grid_servicerestart_group_order", "object": "grid:servicerestart:group:order", "operation": "update", "name": "Update grid:servicerestart:group:order", "description": "Update existing grid:servicerestart:group:order"}, {"id": "delete_grid_servicerestart_group_order", "object": "grid:servicerestart:group:order", "operation": "delete", "name": "Delete grid:servicerestart:group:order", "description": "Delete grid:servicerestart:group:order"}, {"id": "list_grid_servicerestart_request", "object": "grid:servicerestart:request", "operation": "list", "name": "List grid:servicerestart:request", "description": "List/search grid:servicerestart:request objects with filters"}, {"id": "get_grid_servicerestart_request", "object": "grid:servicerestart:request", "operation": "get", "name": "Get grid:servicerestart:request", "description": "Get specific grid:servicerestart:request by reference"}, {"id": "create_grid_servicerestart_request", "object": "grid:servicerestart:request", "operation": "create", "name": "Create grid:servicerestart:request", "description": "Create new grid:servicerestart:request"}, {"id": "update_grid_servicerestart_request", "object": "grid:servicerestart:request", "operation": "update", "name": "Update grid:servicerestart:request", "description": "Update existing grid:servicerestart:request"}, {"id": "delete_grid_servicerestart_request", "object": "grid:servicerestart:request", "operation": "delete", "name": "Delete grid:servicerestart:request", "description": "Delete grid:servicerestart:request"}, {"id": "list_grid_servicerestart_request_changedobject", "object": "grid:servicerestart:request:changedobject", "operation": "list", "name": "List grid:servicerestart:request:changedobject", "description": "List/search grid:servicerestart:request:changedobject objects with filters"}, {"id": "get_grid_servicerestart_request_changedobject", "object": "grid:servicerestart:request:changedobject", "operation": "get", "name": "Get grid:servicerestart:request:changedobject", "description": "Get specific grid:servicerestart:request:changedobject by reference"}, {"id": "create_grid_servicerestart_request_changedobject", "object": "grid:servicerestart:request:changedobject", "operation": "create", "name": "Create grid:servicerestart:request:changedobject", "description": "Create new grid:servicerestart:request:changedobject"}, {"id": "update_grid_servicerestart_request_changedobject", "object": "grid:servicerestart:request:changedobject", "operation": "update", "name": "Update grid:servicerestart:request:changedobject", "description": "Update existing grid:servicerestart:request:changedobject"}, {"id": "delete_grid_servicerestart_request_changedobject", "object": "grid:servicerestart:request:changedobject", "operation": "delete", "name": "Delete grid:servicerestart:request:changedobject", "description": "Delete grid:servicerestart:request:changedobject"}, {"id": "list_grid_servicerestart_status", "object": "grid:servicerestart:status", "operation": "list", "name": "List grid:servicerestart:status", "description": "List/search grid:servicerestart:status objects with filters"}, {"id": "get_grid_servicerestart_status", "object": "grid:servicerestart:status", "operation": "get", "name": "Get grid:servicerestart:status", "description": "Get specific grid:servicerestart:status by reference"}, {"id": "create_grid_servicerestart_status", "object": "grid:servicerestart:status", "operation": "create", "name": "Create grid:servicerestart:status", "description": "Create new grid:servicerestart:status"}, {"id": "update_grid_servicerestart_status", "object": "grid:servicerestart:status", "operation": "update", "name": "Update grid:servicerestart:status", "description": "Update existing grid:servicerestart:status"}, {"id": "delete_grid_servicerestart_status", "object": "grid:servicerestart:status", "operation": "delete", "name": "Delete grid:servicerestart:status", "description": "Delete grid:servicerestart:status"}, {"id": "list_grid_threatanalytics", "object": "grid:threatanalytics", "operation": "list", "name": "List grid:threatanalytics", "description": "List/search grid:threatanalytics objects with filters"}, {"id": "get_grid_threatanalytics", "object": "grid:threatanalytics", "operation": "get", "name": "Get grid:threatanalytics", "description": "Get specific grid:threatanalytics by reference"}, {"id": "create_grid_threatanalytics", "object": "grid:threatanalytics", "operation": "create", "name": "Create grid:threatanalytics", "description": "Create new grid:threatanalytics"}, {"id": "update_grid_threatanalytics", "object": "grid:threatanalytics", "operation": "update", "name": "Update grid:threatanalytics", "description": "Update existing grid:threatanalytics"}, {"id": "delete_grid_threatanalytics", "object": "grid:threatanalytics", "operation": "delete", "name": "Delete grid:threatanalytics", "description": "Delete grid:threatanalytics"}, {"id": "list_grid_threatprotection", "object": "grid:threatprotection", "operation": "list", "name": "List grid:threatprotection", "description": "List/search grid:threatprotection objects with filters"}, {"id": "get_grid_threatprotection", "object": "grid:threatprotection", "operation": "get", "name": "Get grid:threatprotection", "description": "Get specific grid:threatprotection by reference"}, {"id": "create_grid_threatprotection", "object": "grid:threatprotection", "operation": "create", "name": "Create grid:threatprotection", "description": "Create new grid:threatprotection"}, {"id": "update_grid_threatprotection", "object": "grid:threatprotection", "operation": "update", "name": "Update grid:threatprotection", "description": "Update existing grid:threatprotection"}, {"id": "delete_grid_threatprotection", "object": "grid:threatprotection", "operation": "delete", "name": "Delete grid:threatprotection", "description": "Delete grid:threatprotection"}, {"id": "list_grid_x509certificate", "object": "grid:x509certificate", "operation": "list", "name": "List grid:x509certificate", "description": "List/search grid:x509certificate objects with filters"}, {"id": "get_grid_x509certificate", "object": "grid:x509certificate", "operation": "get", "name": "Get grid:x509certificate", "description": "Get specific grid:x509certificate by reference"}, {"id": "create_grid_x509certificate", "object": "grid:x509certificate", "operation": "create", "name": "Create grid:x509certificate", "description": "Create new grid:x509certificate"}, {"id": "update_grid_x509certificate", "object": "grid:x509certificate", "operation": "update", "name": "Update grid:x509certificate", "description": "Update existing grid:x509certificate"}, {"id": "delete_grid_x509certificate", "object": "grid:x509certificate", "operation": "delete", "name": "Delete grid:x509certificate", "description": "Delete grid:x509certificate"}, {"id": "list_hostnamerewritepolicy", "object": "hostnamerewritepolicy", "operation": "list", "name": "List hostnamerewritepolicy", "description": "List/search hostnamerewritepolicy objects with filters"}, {"id": "get_hostnamerewritepolicy", "object": "hostnamerewritepolicy", "operation": "get", "name": "Get hostnamerewritepolicy", "description": "Get specific hostnamerewritepolicy by reference"}, {"id": "create_hostnamerewritepolicy", "object": "hostnamerewritepolicy", "operation": "create", "name": "Create hostnamerewritepolicy", "description": "Create new hostnamerewritepolicy"}, {"id": "update_hostnamerewritepolicy", "object": "hostnamerewritepolicy", "operation": "update", "name": "Update hostnamerewritepolicy", "description": "Update existing hostnamerewritepolicy"}, {"id": "delete_hostnamerewritepolicy", "object": "hostnamerewritepolicy", "operation": "delete", "name": "Delete hostnamerewritepolicy", "description": "Delete hostnamerewritepolicy"}, {"id": "list_hsm_allgroups", "object": "hsm:allgroups", "operation": "list", "name": "List hsm:allgroups", "description": "List/search hsm:allgroups objects with filters"}, {"id": "get_hsm_allgroups", "object": "hsm:allgroups", "operation": "get", "name": "Get hsm:allgroups", "description": "Get specific hsm:allgroups by reference"}, {"id": "create_hsm_allgroups", "object": "hsm:allgroups", "operation": "create", "name": "Create hsm:allgroups", "description": "Create new hsm:allgroups"}, {"id": "update_hsm_allgroups", "object": "hsm:allgroups", "operation": "update", "name": "Update hsm:allgroups", "description": "Update existing hsm:allgroups"}, {"id": "delete_hsm_allgroups", "object": "hsm:allgroups", "operation": "delete", "name": "Delete hsm:allgroups", "description": "Delete hsm:allgroups"}, {"id": "list_hsm_entrustnshieldgroup", "object": "hsm:entrustnshieldgroup", "operation": "list", "name": "List hsm:entrustnshieldgroup", "description": "List/search hsm:entrustnshieldgroup objects with filters"}, {"id": "get_hsm_entrustnshieldgroup", "object": "hsm:entrustnshieldgroup", "operation": "get", "name": "Get hsm:entrustnshieldgroup", "description": "Get specific hsm:entrustnshieldgroup by reference"}, {"id": "create_hsm_entrustnshieldgroup", "object": "hsm:entrustnshieldgroup", "operation": "create", "name": "Create hsm:entrustnshieldgroup", "description": "Create new hsm:entrustnshieldgroup"}, {"id": "update_hsm_entrustnshieldgroup", "object": "hsm:entrustnshieldgroup", "operation": "update", "name": "Update hsm:entrustnshieldgroup", "description": "Update existing hsm:entrustnshieldgroup"}, {"id": "delete_hsm_entrustnshieldgroup", "object": "hsm:entrustnshieldgroup", "operation": "delete", "name": "Delete hsm:entrustnshieldgroup", "description": "Delete hsm:entrustnshieldgroup"}, {"id": "list_hsm_thaleslunagroup", "object": "hsm:thaleslunagroup", "operation": "list", "name": "List hsm:thaleslunagroup", "description": "List/search hsm:thaleslunagroup objects with filters"}, {"id": "get_hsm_thaleslunagroup", "object": "hsm:thaleslunagroup", "operation": "get", "name": "Get hsm:thaleslunagroup", "description": "Get specific hsm:thaleslunagroup by reference"}, {"id": "create_hsm_thaleslunagroup", "object": "hsm:thaleslunagroup", "operation": "create", "name": "Create hsm:thaleslunagroup", "description": "Create new hsm:thaleslunagroup"}, {"id": "update_hsm_thaleslunagroup", "object": "hsm:thaleslunagroup", "operation": "update", "name": "Update hsm:thaleslunagroup", "description": "Update existing hsm:thaleslunagroup"}, {"id": "delete_hsm_thaleslunagroup", "object": "hsm:thaleslunagroup", "operation": "delete", "name": "Delete hsm:thaleslunagroup", "description": "Delete hsm:thaleslunagroup"}, {"id": "list_ipam_statistics", "object": "ipam:statistics", "operation": "list", "name": "List ipam:statistics", "description": "List/search ipam:statistics objects with filters"}, {"id": "get_ipam_statistics", "object": "ipam:statistics", "operation": "get", "name": "Get ipam:statistics", "description": "Get specific ipam:statistics by reference"}, {"id": "create_ipam_statistics", "object": "ipam:statistics", "operation": "create", "name": "Create ipam:statistics", "description": "Create new ipam:statistics"}, {"id": "update_ipam_statistics", "object": "ipam:statistics", "operation": "update", "name": "Update ipam:statistics", "description": "Update existing ipam:statistics"}, {"id": "delete_ipam_statistics", "object": "ipam:statistics", "operation": "delete", "name": "Delete ipam:statistics", "description": "Delete ipam:statistics"}, {"id": "list_ipv4address", "object": "ipv4address", "operation": "list", "name": "List ipv4address", "description": "List/search ipv4address objects with filters"}, {"id": "get_ipv4address", "object": "ipv4address", "operation": "get", "name": "Get ipv4address", "description": "Get specific ipv4address by reference"}, {"id": "create_ipv4address", "object": "ipv4address", "operation": "create", "name": "Create ipv4address", "description": "Create new ipv4address"}, {"id": "update_ipv4address", "object": "ipv4address", "operation": "update", "name": "Update ipv4address", "description": "Update existing ipv4address"}, {"id": "delete_ipv4address", "object": "ipv4address", "operation": "delete", "name": "Delete ipv4address", "description": "Delete ipv4address"}, {"id": "list_ipv6address", "object": "ipv6address", "operation": "list", "name": "List ipv6address", "description": "List/search ipv6address objects with filters"}, {"id": "get_ipv6address", "object": "ipv6address", "operation": "get", "name": "Get ipv6address", "description": "Get specific ipv6address by reference"}, {"id": "create_ipv6address", "object": "ipv6address", "operation": "create", "name": "Create ipv6address", "description": "Create new ipv6address"}, {"id": "update_ipv6address", "object": "ipv6address", "operation": "update", "name": "Update ipv6address", "description": "Update existing ipv6address"}, {"id": "delete_ipv6address", "object": "ipv6address", "operation": "delete", "name": "Delete ipv6address", "description": "Delete ipv6address"}, {"id": "list_ipv6dhcpoptiondefinition", "object": "ipv6dhcpoptiondefinition", "operation": "list", "name": "List ipv6dhcpoptiondefinition", "description": "List/search ipv6dhcpoptiondefinition objects with filters"}, {"id": "get_ipv6dhcpoptiondefinition", "object": "ipv6dhcpoptiondefinition", "operation": "get", "name": "Get ipv6dhcpoptiondefinition", "description": "Get specific ipv6dhcpoptiondefinition by reference"}, {"id": "create_ipv6dhcpoptiondefinition", "object": "ipv6dhcpoptiondefinition", "operation": "create", "name": "Create ipv6dhcpoptiondefinition", "description": "Create new ipv6dhcpoptiondefinition"}, {"id": "update_ipv6dhcpoptiondefinition", "object": "ipv6dhcpoptiondefinition", "operation": "update", "name": "Update ipv6dhcpoptiondefinition", "description": "Update existing ipv6dhcpoptiondefinition"}, {"id": "delete_ipv6dhcpoptiondefinition", "object": "ipv6dhcpoptiondefinition", "operation": "delete", "name": "Delete ipv6dhcpoptiondefinition", "description": "Delete ipv6dhcpoptiondefinition"}, {"id": "list_ipv6dhcpoptionspace", "object": "ipv6dhcpoptionspace", "operation": "list", "name": "List ipv6dhcpoptionspace", "description": "List/search ipv6dhcpoptionspace objects with filters"}, {"id": "get_ipv6dhcpoptionspace", "object": "ipv6dhcpoptionspace", "operation": "get", "name": "Get ipv6dhcpoptionspace", "description": "Get specific ipv6dhcpoptionspace by reference"}, {"id": "create_ipv6dhcpoptionspace", "object": "ipv6dhcpoptionspace", "operation": "create", "name": "Create ipv6dhcpoptionspace", "description": "Create new ipv6dhcpoptionspace"}, {"id": "update_ipv6dhcpoptionspace", "object": "ipv6dhcpoptionspace", "operation": "update", "name": "Update ipv6dhcpoptionspace", "description": "Update existing ipv6dhcpoptionspace"}, {"id": "delete_ipv6dhcpoptionspace", "object": "ipv6dhcpoptionspace", "operation": "delete", "name": "Delete ipv6dhcpoptionspace", "description": "Delete ipv6dhcpoptionspace"}, {"id": "list_ipv6filteroption", "object": "ipv6filteroption", "operation": "list", "name": "List ipv6filteroption", "description": "List/search ipv6filteroption objects with filters"}, {"id": "get_ipv6filteroption", "object": "ipv6filteroption", "operation": "get", "name": "Get ipv6filteroption", "description": "Get specific ipv6filteroption by reference"}, {"id": "create_ipv6filteroption", "object": "ipv6filteroption", "operation": "create", "name": "Create ipv6filteroption", "description": "Create new ipv6filteroption"}, {"id": "update_ipv6filteroption", "object": "ipv6filteroption", "operation": "update", "name": "Update ipv6filteroption", "description": "Update existing ipv6filteroption"}, {"id": "delete_ipv6filteroption", "object": "ipv6filteroption", "operation": "delete", "name": "Delete ipv6filteroption", "description": "Delete ipv6filteroption"}, {"id": "list_ipv6fixedaddress", "object": "ipv6fixedaddress", "operation": "list", "name": "List ipv6fixedaddress", "description": "List/search ipv6fixedaddress objects with filters"}, {"id": "get_ipv6fixedaddress", "object": "ipv6fixedaddress", "operation": "get", "name": "Get ipv6fixedaddress", "description": "Get specific ipv6fixedaddress by reference"}, {"id": "create_ipv6fixedaddress", "object": "ipv6fixedaddress", "operation": "create", "name": "Create ipv6fixedaddress", "description": "Create new ipv6fixedaddress"}, {"id": "update_ipv6fixedaddress", "object": "ipv6fixedaddress", "operation": "update", "name": "Update ipv6fixedaddress", "description": "Update existing ipv6fixedaddress"}, {"id": "delete_ipv6fixedaddress", "object": "ipv6fixedaddress", "operation": "delete", "name": "Delete ipv6fixedaddress", "description": "Delete ipv6fixedaddress"}, {"id": "list_ipv6fixedaddresstemplate", "object": "ipv6fixedaddresstemplate", "operation": "list", "name": "List ipv6fixedaddresstemplate", "description": "List/search ipv6fixedaddresstemplate objects with filters"}, {"id": "get_ipv6fixedaddresstemplate", "object": "ipv6fixedaddresstemplate", "operation": "get", "name": "Get ipv6fixedaddresstemplate", "description": "Get specific ipv6fixedaddresstemplate by reference"}, {"id": "create_ipv6fixedaddresstemplate", "object": "ipv6fixedaddresstemplate", "operation": "create", "name": "Create ipv6fixedaddresstemplate", "description": "Create new ipv6fixedaddresstemplate"}, {"id": "update_ipv6fixedaddresstemplate", "object": "ipv6fixedaddresstemplate", "operation": "update", "name": "Update ipv6fixedaddresstemplate", "description": "Update existing ipv6fixedaddresstemplate"}, {"id": "delete_ipv6fixedaddresstemplate", "object": "ipv6fixedaddresstemplate", "operation": "delete", "name": "Delete ipv6fixedaddresstemplate", "description": "Delete ipv6fixedaddresstemplate"}, {"id": "list_ipv6network", "object": "ipv6network", "operation": "list", "name": "List ipv6network", "description": "List/search ipv6network objects with filters"}, {"id": "get_ipv6network", "object": "ipv6network", "operation": "get", "name": "Get ipv6network", "description": "Get specific ipv6network by reference"}, {"id": "create_ipv6network", "object": "ipv6network", "operation": "create", "name": "Create ipv6network", "description": "Create new ipv6network"}, {"id": "update_ipv6network", "object": "ipv6network", "operation": "update", "name": "Update ipv6network", "description": "Update existing ipv6network"}, {"id": "delete_ipv6network", "object": "ipv6network", "operation": "delete", "name": "Delete ipv6network", "description": "Delete ipv6network"}, {"id": "list_ipv6networkcontainer", "object": "ipv6networkcontainer", "operation": "list", "name": "List ipv6networkcontainer", "description": "List/search ipv6networkcontainer objects with filters"}, {"id": "get_ipv6networkcontainer", "object": "ipv6networkcontainer", "operation": "get", "name": "Get ipv6networkcontainer", "description": "Get specific ipv6networkcontainer by reference"}, {"id": "create_ipv6networkcontainer", "object": "ipv6networkcontainer", "operation": "create", "name": "Create ipv6networkcontainer", "description": "Create new ipv6networkcontainer"}, {"id": "update_ipv6networkcontainer", "object": "ipv6networkcontainer", "operation": "update", "name": "Update ipv6networkcontainer", "description": "Update existing ipv6networkcontainer"}, {"id": "delete_ipv6networkcontainer", "object": "ipv6networkcontainer", "operation": "delete", "name": "Delete ipv6networkcontainer", "description": "Delete ipv6networkcontainer"}, {"id": "list_ipv6networktemplate", "object": "ipv6networktemplate", "operation": "list", "name": "List ipv6networktemplate", "description": "List/search ipv6networktemplate objects with filters"}, {"id": "get_ipv6networktemplate", "object": "ipv6networktemplate", "operation": "get", "name": "Get ipv6networktemplate", "description": "Get specific ipv6networktemplate by reference"}, {"id": "create_ipv6networktemplate", "object": "ipv6networktemplate", "operation": "create", "name": "Create ipv6networktemplate", "description": "Create new ipv6networktemplate"}, {"id": "update_ipv6networktemplate", "object": "ipv6networktemplate", "operation": "update", "name": "Update ipv6networktemplate", "description": "Update existing ipv6networktemplate"}, {"id": "delete_ipv6networktemplate", "object": "ipv6networktemplate", "operation": "delete", "name": "Delete ipv6networktemplate", "description": "Delete ipv6networktemplate"}, {"id": "list_ipv6range", "object": "ipv6range", "operation": "list", "name": "List ipv6range", "description": "List/search ipv6range objects with filters"}, {"id": "get_ipv6range", "object": "ipv6range", "operation": "get", "name": "Get ipv6range", "description": "Get specific ipv6range by reference"}, {"id": "create_ipv6range", "object": "ipv6range", "operation": "create", "name": "Create ipv6range", "description": "Create new ipv6range"}, {"id": "update_ipv6range", "object": "ipv6range", "operation": "update", "name": "Update ipv6range", "description": "Update existing ipv6range"}, {"id": "delete_ipv6range", "object": "ipv6range", "operation": "delete", "name": "Delete ipv6range", "description": "Delete ipv6range"}, {"id": "list_ipv6rangetemplate", "object": "ipv6rangetemplate", "operation": "list", "name": "List ipv6rangetemplate", "description": "List/search ipv6rangetemplate objects with filters"}, {"id": "get_ipv6rangetemplate", "object": "ipv6rangetemplate", "operation": "get", "name": "Get ipv6rangetemplate", "description": "Get specific ipv6rangetemplate by reference"}, {"id": "create_ipv6rangetemplate", "object": "ipv6rangetemplate", "operation": "create", "name": "Create ipv6rangetemplate", "description": "Create new ipv6rangetemplate"}, {"id": "update_ipv6rangetemplate", "object": "ipv6rangetemplate", "operation": "update", "name": "Update ipv6rangetemplate", "description": "Update existing ipv6rangetemplate"}, {"id": "delete_ipv6rangetemplate", "object": "ipv6rangetemplate", "operation": "delete", "name": "Delete ipv6rangetemplate", "description": "Delete ipv6rangetemplate"}, {"id": "list_ipv6sharednetwork", "object": "ipv6sharednetwork", "operation": "list", "name": "List ipv6sharednetwork", "description": "List/search ipv6sharednetwork objects with filters"}, {"id": "get_ipv6sharednetwork", "object": "ipv6sharednetwork", "operation": "get", "name": "Get ipv6sharednetwork", "description": "Get specific ipv6sharednetwork by reference"}, {"id": "create_ipv6sharednetwork", "object": "ipv6sharednetwork", "operation": "create", "name": "Create ipv6sharednetwork", "description": "Create new ipv6sharednetwork"}, {"id": "update_ipv6sharednetwork", "object": "ipv6sharednetwork", "operation": "update", "name": "Update ipv6sharednetwork", "description": "Update existing ipv6sharednetwork"}, {"id": "delete_ipv6sharednetwork", "object": "ipv6sharednetwork", "operation": "delete", "name": "Delete ipv6sharednetwork", "description": "Delete ipv6sharednetwork"}, {"id": "list_kerberoskey", "object": "<PERSON>er<PERSON><PERSON><PERSON>", "operation": "list", "name": "List kerberoskey", "description": "List/search kerberoskey objects with filters"}, {"id": "get_kerberoskey", "object": "<PERSON>er<PERSON><PERSON><PERSON>", "operation": "get", "name": "Get ker<PERSON><PERSON><PERSON>", "description": "Get specific kerberoskey by reference"}, {"id": "create_kerberoskey", "object": "<PERSON>er<PERSON><PERSON><PERSON>", "operation": "create", "name": "Create kerberoskey", "description": "Create new kerberoskey"}, {"id": "update_kerberoskey", "object": "<PERSON>er<PERSON><PERSON><PERSON>", "operation": "update", "name": "Update kerber<PERSON><PERSON>", "description": "Update existing kerberoskey"}, {"id": "delete_kerberoskey", "object": "<PERSON>er<PERSON><PERSON><PERSON>", "operation": "delete", "name": "Delete kerberoskey", "description": "Delete kerberoskey"}, {"id": "list_ldap_auth_service", "object": "ldap_auth_service", "operation": "list", "name": "List ldap_auth_service", "description": "List/search ldap_auth_service objects with filters"}, {"id": "get_ldap_auth_service", "object": "ldap_auth_service", "operation": "get", "name": "Get ldap_auth_service", "description": "Get specific ldap_auth_service by reference"}, {"id": "create_ldap_auth_service", "object": "ldap_auth_service", "operation": "create", "name": "Create ldap_auth_service", "description": "Create new ldap_auth_service"}, {"id": "update_ldap_auth_service", "object": "ldap_auth_service", "operation": "update", "name": "Update ldap_auth_service", "description": "Update existing ldap_auth_service"}, {"id": "delete_ldap_auth_service", "object": "ldap_auth_service", "operation": "delete", "name": "Delete ldap_auth_service", "description": "Delete ldap_auth_service"}, {"id": "list_lease", "object": "lease", "operation": "list", "name": "List lease", "description": "List/search lease objects with filters"}, {"id": "get_lease", "object": "lease", "operation": "get", "name": "Get lease", "description": "Get specific lease by reference"}, {"id": "create_lease", "object": "lease", "operation": "create", "name": "Create lease", "description": "Create new lease"}, {"id": "update_lease", "object": "lease", "operation": "update", "name": "Update lease", "description": "Update existing lease"}, {"id": "delete_lease", "object": "lease", "operation": "delete", "name": "Delete lease", "description": "Delete lease"}, {"id": "list_license_gridwide", "object": "license:gridwide", "operation": "list", "name": "List license:gridwide", "description": "List/search license:gridwide objects with filters"}, {"id": "get_license_gridwide", "object": "license:gridwide", "operation": "get", "name": "Get license:gridwide", "description": "Get specific license:gridwide by reference"}, {"id": "create_license_gridwide", "object": "license:gridwide", "operation": "create", "name": "Create license:gridwide", "description": "Create new license:gridwide"}, {"id": "update_license_gridwide", "object": "license:gridwide", "operation": "update", "name": "Update license:gridwide", "description": "Update existing license:gridwide"}, {"id": "delete_license_gridwide", "object": "license:gridwide", "operation": "delete", "name": "Delete license:gridwide", "description": "Delete license:gridwide"}, {"id": "list_localuser_authservice", "object": "localuser:authservice", "operation": "list", "name": "List localuser:authservice", "description": "List/search localuser:authservice objects with filters"}, {"id": "get_localuser_authservice", "object": "localuser:authservice", "operation": "get", "name": "Get localuser:authservice", "description": "Get specific localuser:authservice by reference"}, {"id": "create_localuser_authservice", "object": "localuser:authservice", "operation": "create", "name": "Create localuser:authservice", "description": "Create new localuser:authservice"}, {"id": "update_localuser_authservice", "object": "localuser:authservice", "operation": "update", "name": "Update localuser:authservice", "description": "Update existing localuser:authservice"}, {"id": "delete_localuser_authservice", "object": "localuser:authservice", "operation": "delete", "name": "Delete localuser:authservice", "description": "Delete localuser:authservice"}, {"id": "list_macfilteraddress", "object": "macfilteraddress", "operation": "list", "name": "List macfilteraddress", "description": "List/search macfilteraddress objects with filters"}, {"id": "get_macfilteraddress", "object": "macfilteraddress", "operation": "get", "name": "Get macfilteraddress", "description": "Get specific macfilteraddress by reference"}, {"id": "create_macfilteraddress", "object": "macfilteraddress", "operation": "create", "name": "Create macfilteraddress", "description": "Create new macfilteraddress"}, {"id": "update_macfilteraddress", "object": "macfilteraddress", "operation": "update", "name": "Update macfilteraddress", "description": "Update existing macfilteraddress"}, {"id": "delete_macfilteraddress", "object": "macfilteraddress", "operation": "delete", "name": "Delete macfilteraddress", "description": "Delete macfilteraddress"}, {"id": "list_mastergrid", "object": "mastergrid", "operation": "list", "name": "List mastergrid", "description": "List/search mastergrid objects with filters"}, {"id": "get_mastergrid", "object": "mastergrid", "operation": "get", "name": "Get mastergrid", "description": "Get specific mastergrid by reference"}, {"id": "create_mastergrid", "object": "mastergrid", "operation": "create", "name": "Create mastergrid", "description": "Create new mastergrid"}, {"id": "update_mastergrid", "object": "mastergrid", "operation": "update", "name": "Update mastergrid", "description": "Update existing mastergrid"}, {"id": "delete_mastergrid", "object": "mastergrid", "operation": "delete", "name": "Delete mastergrid", "description": "Delete mastergrid"}, {"id": "list_member", "object": "member", "operation": "list", "name": "List member", "description": "List/search member objects with filters"}, {"id": "get_member", "object": "member", "operation": "get", "name": "Get member", "description": "Get specific member by reference"}, {"id": "create_member", "object": "member", "operation": "create", "name": "Create member", "description": "Create new member"}, {"id": "update_member", "object": "member", "operation": "update", "name": "Update member", "description": "Update existing member"}, {"id": "delete_member", "object": "member", "operation": "delete", "name": "Delete member", "description": "Delete member"}, {"id": "list_member_dhcpproperties", "object": "member:dhcpproperties", "operation": "list", "name": "List member:dhcpproperties", "description": "List/search member:dhcpproperties objects with filters"}, {"id": "get_member_dhcpproperties", "object": "member:dhcpproperties", "operation": "get", "name": "Get member:dhcpproperties", "description": "Get specific member:dhcpproperties by reference"}, {"id": "create_member_dhcpproperties", "object": "member:dhcpproperties", "operation": "create", "name": "Create member:dhcpproperties", "description": "Create new member:dhcpproperties"}, {"id": "update_member_dhcpproperties", "object": "member:dhcpproperties", "operation": "update", "name": "Update member:dhcpproperties", "description": "Update existing member:dhcpproperties"}, {"id": "delete_member_dhcpproperties", "object": "member:dhcpproperties", "operation": "delete", "name": "Delete member:dhcpproperties", "description": "Delete member:dhcpproperties"}, {"id": "list_member_dns", "object": "member:dns", "operation": "list", "name": "List member:dns", "description": "List/search member:dns objects with filters"}, {"id": "get_member_dns", "object": "member:dns", "operation": "get", "name": "Get member:dns", "description": "Get specific member:dns by reference"}, {"id": "create_member_dns", "object": "member:dns", "operation": "create", "name": "Create member:dns", "description": "Create new member:dns"}, {"id": "update_member_dns", "object": "member:dns", "operation": "update", "name": "Update member:dns", "description": "Update existing member:dns"}, {"id": "delete_member_dns", "object": "member:dns", "operation": "delete", "name": "Delete member:dns", "description": "Delete member:dns"}, {"id": "list_member_filedistribution", "object": "member:filedistribution", "operation": "list", "name": "List member:filedistribution", "description": "List/search member:filedistribution objects with filters"}, {"id": "get_member_filedistribution", "object": "member:filedistribution", "operation": "get", "name": "Get member:filedistribution", "description": "Get specific member:filedistribution by reference"}, {"id": "create_member_filedistribution", "object": "member:filedistribution", "operation": "create", "name": "Create member:filedistribution", "description": "Create new member:filedistribution"}, {"id": "update_member_filedistribution", "object": "member:filedistribution", "operation": "update", "name": "Update member:filedistribution", "description": "Update existing member:filedistribution"}, {"id": "delete_member_filedistribution", "object": "member:filedistribution", "operation": "delete", "name": "Delete member:filedistribution", "description": "Delete member:filedistribution"}, {"id": "list_member_license", "object": "member:license", "operation": "list", "name": "List member:license", "description": "List/search member:license objects with filters"}, {"id": "get_member_license", "object": "member:license", "operation": "get", "name": "Get member:license", "description": "Get specific member:license by reference"}, {"id": "create_member_license", "object": "member:license", "operation": "create", "name": "Create member:license", "description": "Create new member:license"}, {"id": "update_member_license", "object": "member:license", "operation": "update", "name": "Update member:license", "description": "Update existing member:license"}, {"id": "delete_member_license", "object": "member:license", "operation": "delete", "name": "Delete member:license", "description": "Delete member:license"}, {"id": "list_member_parentalcontrol", "object": "member:parentalcontrol", "operation": "list", "name": "List member:parentalcontrol", "description": "List/search member:parentalcontrol objects with filters"}, {"id": "get_member_parentalcontrol", "object": "member:parentalcontrol", "operation": "get", "name": "Get member:parentalcontrol", "description": "Get specific member:parentalcontrol by reference"}, {"id": "create_member_parentalcontrol", "object": "member:parentalcontrol", "operation": "create", "name": "Create member:parental<PERSON><PERSON>l", "description": "Create new member:parental<PERSON><PERSON>l"}, {"id": "update_member_parentalcontrol", "object": "member:parentalcontrol", "operation": "update", "name": "Update member:<PERSON><PERSON><PERSON>l", "description": "Update existing member:parental<PERSON><PERSON>l"}, {"id": "delete_member_parentalcontrol", "object": "member:parentalcontrol", "operation": "delete", "name": "Delete member:parental<PERSON><PERSON>l", "description": "Delete member:parental<PERSON><PERSON>l"}, {"id": "list_member_threatanalytics", "object": "member:threatanalytics", "operation": "list", "name": "List member:threatanalytics", "description": "List/search member:threatanalytics objects with filters"}, {"id": "get_member_threatanalytics", "object": "member:threatanalytics", "operation": "get", "name": "Get member:threatanalytics", "description": "Get specific member:threatanalytics by reference"}, {"id": "create_member_threatanalytics", "object": "member:threatanalytics", "operation": "create", "name": "Create member:threatanalytics", "description": "Create new member:threatanalytics"}, {"id": "update_member_threatanalytics", "object": "member:threatanalytics", "operation": "update", "name": "Update member:threatanalytics", "description": "Update existing member:threatanalytics"}, {"id": "delete_member_threatanalytics", "object": "member:threatanalytics", "operation": "delete", "name": "Delete member:threatanalytics", "description": "Delete member:threatanalytics"}, {"id": "list_member_threatprotection", "object": "member:threatprotection", "operation": "list", "name": "List member:threatprotection", "description": "List/search member:threatprotection objects with filters"}, {"id": "get_member_threatprotection", "object": "member:threatprotection", "operation": "get", "name": "Get member:threatprotection", "description": "Get specific member:threatprotection by reference"}, {"id": "create_member_threatprotection", "object": "member:threatprotection", "operation": "create", "name": "Create member:threatprotection", "description": "Create new member:threatprotection"}, {"id": "update_member_threatprotection", "object": "member:threatprotection", "operation": "update", "name": "Update member:threatprotection", "description": "Update existing member:threatprotection"}, {"id": "delete_member_threatprotection", "object": "member:threatprotection", "operation": "delete", "name": "Delete member:threatprotection", "description": "Delete member:threatprotection"}, {"id": "list_memberclouddnssync", "object": "memberclouddnssync", "operation": "list", "name": "List memberclouddnssync", "description": "List/search memberclouddnssync objects with filters"}, {"id": "get_memberclouddnssync", "object": "memberclouddnssync", "operation": "get", "name": "Get memberclouddnssync", "description": "Get specific memberclouddnssync by reference"}, {"id": "create_memberclouddnssync", "object": "memberclouddnssync", "operation": "create", "name": "Create memberclouddnssync", "description": "Create new memberclouddnssync"}, {"id": "update_memberclouddnssync", "object": "memberclouddnssync", "operation": "update", "name": "Update memberclouddnssync", "description": "Update existing memberclouddnssync"}, {"id": "delete_memberclouddnssync", "object": "memberclouddnssync", "operation": "delete", "name": "Delete memberclouddnssync", "description": "Delete memberclouddnssync"}, {"id": "list_memberdfp", "object": "memberdfp", "operation": "list", "name": "List memberdfp", "description": "List/search memberdfp objects with filters"}, {"id": "get_memberdfp", "object": "memberdfp", "operation": "get", "name": "Get memberdfp", "description": "Get specific memberdfp by reference"}, {"id": "create_memberdfp", "object": "memberdfp", "operation": "create", "name": "Create memberdfp", "description": "Create new memberdfp"}, {"id": "update_memberdfp", "object": "memberdfp", "operation": "update", "name": "Update memberdfp", "description": "Update existing memberdfp"}, {"id": "delete_memberdfp", "object": "memberdfp", "operation": "delete", "name": "Delete memberdfp", "description": "Delete memberdfp"}, {"id": "list_msserver", "object": "msserver", "operation": "list", "name": "List msserver", "description": "List/search msserver objects with filters"}, {"id": "get_msserver", "object": "msserver", "operation": "get", "name": "Get m<PERSON>ver", "description": "Get specific msserver by reference"}, {"id": "create_msserver", "object": "msserver", "operation": "create", "name": "Create msserver", "description": "Create new msserver"}, {"id": "update_msserver", "object": "msserver", "operation": "update", "name": "Update msserver", "description": "Update existing msserver"}, {"id": "delete_msserver", "object": "msserver", "operation": "delete", "name": "Delete msserver", "description": "Delete msserver"}, {"id": "list_msserver_adsites_domain", "object": "msserver:adsites:domain", "operation": "list", "name": "List msserver:adsites:domain", "description": "List/search msserver:adsites:domain objects with filters"}, {"id": "get_msserver_adsites_domain", "object": "msserver:adsites:domain", "operation": "get", "name": "Get msserver:adsites:domain", "description": "Get specific msserver:adsites:domain by reference"}, {"id": "create_msserver_adsites_domain", "object": "msserver:adsites:domain", "operation": "create", "name": "Create msserver:adsites:domain", "description": "Create new msserver:adsites:domain"}, {"id": "update_msserver_adsites_domain", "object": "msserver:adsites:domain", "operation": "update", "name": "Update msserver:adsites:domain", "description": "Update existing msserver:adsites:domain"}, {"id": "delete_msserver_adsites_domain", "object": "msserver:adsites:domain", "operation": "delete", "name": "Delete msserver:adsites:domain", "description": "Delete msserver:adsites:domain"}, {"id": "list_msserver_adsites_site", "object": "msserver:adsites:site", "operation": "list", "name": "List msserver:adsites:site", "description": "List/search msserver:adsites:site objects with filters"}, {"id": "get_msserver_adsites_site", "object": "msserver:adsites:site", "operation": "get", "name": "Get msserver:adsites:site", "description": "Get specific msserver:adsites:site by reference"}, {"id": "create_msserver_adsites_site", "object": "msserver:adsites:site", "operation": "create", "name": "Create msserver:adsites:site", "description": "Create new msserver:adsites:site"}, {"id": "update_msserver_adsites_site", "object": "msserver:adsites:site", "operation": "update", "name": "Update msserver:adsites:site", "description": "Update existing msserver:adsites:site"}, {"id": "delete_msserver_adsites_site", "object": "msserver:adsites:site", "operation": "delete", "name": "Delete msserver:adsites:site", "description": "Delete msserver:adsites:site"}, {"id": "list_msserver_dhcp", "object": "msserver:dhcp", "operation": "list", "name": "List msserver:dhcp", "description": "List/search msserver:dhcp objects with filters"}, {"id": "get_msserver_dhcp", "object": "msserver:dhcp", "operation": "get", "name": "Get msserver:dhcp", "description": "Get specific msserver:dhcp by reference"}, {"id": "create_msserver_dhcp", "object": "msserver:dhcp", "operation": "create", "name": "Create msserver:dhcp", "description": "Create new msserver:dhcp"}, {"id": "update_msserver_dhcp", "object": "msserver:dhcp", "operation": "update", "name": "Update msserver:dhcp", "description": "Update existing msserver:dhcp"}, {"id": "delete_msserver_dhcp", "object": "msserver:dhcp", "operation": "delete", "name": "Delete msserver:dhcp", "description": "Delete msserver:dhcp"}, {"id": "list_msserver_dns", "object": "msserver:dns", "operation": "list", "name": "List msserver:dns", "description": "List/search msserver:dns objects with filters"}, {"id": "get_msserver_dns", "object": "msserver:dns", "operation": "get", "name": "Get msserver:dns", "description": "Get specific msserver:dns by reference"}, {"id": "create_msserver_dns", "object": "msserver:dns", "operation": "create", "name": "Create msserver:dns", "description": "Create new msserver:dns"}, {"id": "update_msserver_dns", "object": "msserver:dns", "operation": "update", "name": "Update msserver:dns", "description": "Update existing msserver:dns"}, {"id": "delete_msserver_dns", "object": "msserver:dns", "operation": "delete", "name": "Delete msserver:dns", "description": "Delete msserver:dns"}, {"id": "list_mssuperscope", "object": "mssuperscope", "operation": "list", "name": "List mssuperscope", "description": "List/search mssuperscope objects with filters"}, {"id": "get_mssuperscope", "object": "mssuperscope", "operation": "get", "name": "Get mssuperscope", "description": "Get specific mssuperscope by reference"}, {"id": "create_mssuperscope", "object": "mssuperscope", "operation": "create", "name": "Create mssuperscope", "description": "Create new mssuperscope"}, {"id": "update_mssuperscope", "object": "mssuperscope", "operation": "update", "name": "Update mssuperscope", "description": "Update existing mssuperscope"}, {"id": "delete_mssuperscope", "object": "mssuperscope", "operation": "delete", "name": "Delete mssuperscope", "description": "Delete mssuperscope"}, {"id": "list_namedacl", "object": "<PERSON><PERSON><PERSON>", "operation": "list", "name": "List namedacl", "description": "List/search namedacl objects with filters"}, {"id": "get_namedacl", "object": "<PERSON><PERSON><PERSON>", "operation": "get", "name": "Get <PERSON><PERSON><PERSON>", "description": "Get specific namedacl by reference"}, {"id": "create_namedacl", "object": "<PERSON><PERSON><PERSON>", "operation": "create", "name": "Create <PERSON><PERSON><PERSON>", "description": "Create new namedacl"}, {"id": "update_namedacl", "object": "<PERSON><PERSON><PERSON>", "operation": "update", "name": "Update named<PERSON><PERSON>", "description": "Update existing namedacl"}, {"id": "delete_namedacl", "object": "<PERSON><PERSON><PERSON>", "operation": "delete", "name": "Delete namedacl", "description": "Delete namedacl"}, {"id": "list_natgroup", "object": "natgroup", "operation": "list", "name": "List natgroup", "description": "List/search natgroup objects with filters"}, {"id": "get_natgroup", "object": "natgroup", "operation": "get", "name": "Get natgroup", "description": "Get specific natgroup by reference"}, {"id": "create_natgroup", "object": "natgroup", "operation": "create", "name": "Create natgroup", "description": "Create new natgroup"}, {"id": "update_natgroup", "object": "natgroup", "operation": "update", "name": "Update natgroup", "description": "Update existing natgroup"}, {"id": "delete_natgroup", "object": "natgroup", "operation": "delete", "name": "Delete natgroup", "description": "Delete natgroup"}, {"id": "list_network", "object": "network", "operation": "list", "name": "List network", "description": "List/search network objects with filters"}, {"id": "get_network", "object": "network", "operation": "get", "name": "Get network", "description": "Get specific network by reference"}, {"id": "create_network", "object": "network", "operation": "create", "name": "Create network", "description": "Create new network"}, {"id": "update_network", "object": "network", "operation": "update", "name": "Update network", "description": "Update existing network"}, {"id": "delete_network", "object": "network", "operation": "delete", "name": "Delete network", "description": "Delete network"}, {"id": "list_network_discovery", "object": "network_discovery", "operation": "list", "name": "List network_discovery", "description": "List/search network_discovery objects with filters"}, {"id": "get_network_discovery", "object": "network_discovery", "operation": "get", "name": "Get network_discovery", "description": "Get specific network_discovery by reference"}, {"id": "create_network_discovery", "object": "network_discovery", "operation": "create", "name": "Create network_discovery", "description": "Create new network_discovery"}, {"id": "update_network_discovery", "object": "network_discovery", "operation": "update", "name": "Update network_discovery", "description": "Update existing network_discovery"}, {"id": "delete_network_discovery", "object": "network_discovery", "operation": "delete", "name": "Delete network_discovery", "description": "Delete network_discovery"}, {"id": "list_networkcontainer", "object": "networkcontainer", "operation": "list", "name": "List networkcontainer", "description": "List/search networkcontainer objects with filters"}, {"id": "get_networkcontainer", "object": "networkcontainer", "operation": "get", "name": "Get networkcontainer", "description": "Get specific networkcontainer by reference"}, {"id": "create_networkcontainer", "object": "networkcontainer", "operation": "create", "name": "Create networkcontainer", "description": "Create new networkcontainer"}, {"id": "update_networkcontainer", "object": "networkcontainer", "operation": "update", "name": "Update networkcontainer", "description": "Update existing networkcontainer"}, {"id": "delete_networkcontainer", "object": "networkcontainer", "operation": "delete", "name": "Delete networkcontainer", "description": "Delete networkcontainer"}, {"id": "list_networktemplate", "object": "networktemplate", "operation": "list", "name": "List networktemplate", "description": "List/search networktemplate objects with filters"}, {"id": "get_networktemplate", "object": "networktemplate", "operation": "get", "name": "Get networktemplate", "description": "Get specific networktemplate by reference"}, {"id": "create_networktemplate", "object": "networktemplate", "operation": "create", "name": "Create networktemplate", "description": "Create new networktemplate"}, {"id": "update_networktemplate", "object": "networktemplate", "operation": "update", "name": "Update networktemplate", "description": "Update existing networktemplate"}, {"id": "delete_networktemplate", "object": "networktemplate", "operation": "delete", "name": "Delete networktemplate", "description": "Delete networktemplate"}, {"id": "list_networkuser", "object": "networkuser", "operation": "list", "name": "List networkuser", "description": "List/search networkuser objects with filters"}, {"id": "get_networkuser", "object": "networkuser", "operation": "get", "name": "Get networkuser", "description": "Get specific networkuser by reference"}, {"id": "create_networkuser", "object": "networkuser", "operation": "create", "name": "Create networkuser", "description": "Create new networkuser"}, {"id": "update_networkuser", "object": "networkuser", "operation": "update", "name": "Update networkuser", "description": "Update existing networkuser"}, {"id": "delete_networkuser", "object": "networkuser", "operation": "delete", "name": "Delete networkuser", "description": "Delete networkuser"}, {"id": "list_networkview", "object": "networkview", "operation": "list", "name": "List networkview", "description": "List/search networkview objects with filters"}, {"id": "get_networkview", "object": "networkview", "operation": "get", "name": "Get networkview", "description": "Get specific networkview by reference"}, {"id": "create_networkview", "object": "networkview", "operation": "create", "name": "Create networkview", "description": "Create new networkview"}, {"id": "update_networkview", "object": "networkview", "operation": "update", "name": "Update networkview", "description": "Update existing networkview"}, {"id": "delete_networkview", "object": "networkview", "operation": "delete", "name": "Delete networkview", "description": "Delete networkview"}, {"id": "list_notification_rest_endpoint", "object": "notification:rest:endpoint", "operation": "list", "name": "List notification:rest:endpoint", "description": "List/search notification:rest:endpoint objects with filters"}, {"id": "get_notification_rest_endpoint", "object": "notification:rest:endpoint", "operation": "get", "name": "Get notification:rest:endpoint", "description": "Get specific notification:rest:endpoint by reference"}, {"id": "create_notification_rest_endpoint", "object": "notification:rest:endpoint", "operation": "create", "name": "Create notification:rest:endpoint", "description": "Create new notification:rest:endpoint"}, {"id": "update_notification_rest_endpoint", "object": "notification:rest:endpoint", "operation": "update", "name": "Update notification:rest:endpoint", "description": "Update existing notification:rest:endpoint"}, {"id": "delete_notification_rest_endpoint", "object": "notification:rest:endpoint", "operation": "delete", "name": "Delete notification:rest:endpoint", "description": "Delete notification:rest:endpoint"}, {"id": "list_notification_rest_template", "object": "notification:rest:template", "operation": "list", "name": "List notification:rest:template", "description": "List/search notification:rest:template objects with filters"}, {"id": "get_notification_rest_template", "object": "notification:rest:template", "operation": "get", "name": "Get notification:rest:template", "description": "Get specific notification:rest:template by reference"}, {"id": "create_notification_rest_template", "object": "notification:rest:template", "operation": "create", "name": "Create notification:rest:template", "description": "Create new notification:rest:template"}, {"id": "update_notification_rest_template", "object": "notification:rest:template", "operation": "update", "name": "Update notification:rest:template", "description": "Update existing notification:rest:template"}, {"id": "delete_notification_rest_template", "object": "notification:rest:template", "operation": "delete", "name": "Delete notification:rest:template", "description": "Delete notification:rest:template"}, {"id": "list_notification_rule", "object": "notification:rule", "operation": "list", "name": "List notification:rule", "description": "List/search notification:rule objects with filters"}, {"id": "get_notification_rule", "object": "notification:rule", "operation": "get", "name": "Get notification:rule", "description": "Get specific notification:rule by reference"}, {"id": "create_notification_rule", "object": "notification:rule", "operation": "create", "name": "Create notification:rule", "description": "Create new notification:rule"}, {"id": "update_notification_rule", "object": "notification:rule", "operation": "update", "name": "Update notification:rule", "description": "Update existing notification:rule"}, {"id": "delete_notification_rule", "object": "notification:rule", "operation": "delete", "name": "Delete notification:rule", "description": "Delete notification:rule"}, {"id": "list_nsgroup", "object": "nsgroup", "operation": "list", "name": "List nsgroup", "description": "List/search nsgroup objects with filters"}, {"id": "get_nsgroup", "object": "nsgroup", "operation": "get", "name": "Get nsgroup", "description": "Get specific nsgroup by reference"}, {"id": "create_nsgroup", "object": "nsgroup", "operation": "create", "name": "Create nsgroup", "description": "Create new nsgroup"}, {"id": "update_nsgroup", "object": "nsgroup", "operation": "update", "name": "Update nsgroup", "description": "Update existing nsgroup"}, {"id": "delete_nsgroup", "object": "nsgroup", "operation": "delete", "name": "Delete nsgroup", "description": "Delete nsgroup"}, {"id": "list_nsgroup_delegation", "object": "nsgroup:delegation", "operation": "list", "name": "List nsgroup:delegation", "description": "List/search nsgroup:delegation objects with filters"}, {"id": "get_nsgroup_delegation", "object": "nsgroup:delegation", "operation": "get", "name": "Get nsgroup:delegation", "description": "Get specific nsgroup:delegation by reference"}, {"id": "create_nsgroup_delegation", "object": "nsgroup:delegation", "operation": "create", "name": "Create nsgroup:delegation", "description": "Create new nsgroup:delegation"}, {"id": "update_nsgroup_delegation", "object": "nsgroup:delegation", "operation": "update", "name": "Update nsgroup:delegation", "description": "Update existing nsgroup:delegation"}, {"id": "delete_nsgroup_delegation", "object": "nsgroup:delegation", "operation": "delete", "name": "Delete nsgroup:delegation", "description": "Delete nsgroup:delegation"}, {"id": "list_nsgroup_forwardingmember", "object": "nsgroup:forwardingmember", "operation": "list", "name": "List nsgroup:forwardingmember", "description": "List/search nsgroup:forwardingmember objects with filters"}, {"id": "get_nsgroup_forwardingmember", "object": "nsgroup:forwardingmember", "operation": "get", "name": "Get nsgroup:forwardingmember", "description": "Get specific nsgroup:forwardingmember by reference"}, {"id": "create_nsgroup_forwardingmember", "object": "nsgroup:forwardingmember", "operation": "create", "name": "Create nsgroup:forwardingmember", "description": "Create new nsgroup:forwardingmember"}, {"id": "update_nsgroup_forwardingmember", "object": "nsgroup:forwardingmember", "operation": "update", "name": "Update nsgroup:forwardingmember", "description": "Update existing nsgroup:forwardingmember"}, {"id": "delete_nsgroup_forwardingmember", "object": "nsgroup:forwardingmember", "operation": "delete", "name": "Delete nsgroup:forwardingmember", "description": "Delete nsgroup:forwardingmember"}, {"id": "list_nsgroup_forwardstubserver", "object": "nsgroup:forwardstubserver", "operation": "list", "name": "List nsgroup:forwardstubserver", "description": "List/search nsgroup:forwardstubserver objects with filters"}, {"id": "get_nsgroup_forwardstubserver", "object": "nsgroup:forwardstubserver", "operation": "get", "name": "Get nsgroup:forwardstubserver", "description": "Get specific nsgroup:forwardstubserver by reference"}, {"id": "create_nsgroup_forwardstubserver", "object": "nsgroup:forwardstubserver", "operation": "create", "name": "Create nsgroup:forwardstubserver", "description": "Create new nsgroup:forwardstubserver"}, {"id": "update_nsgroup_forwardstubserver", "object": "nsgroup:forwardstubserver", "operation": "update", "name": "Update nsgroup:forwardstubserver", "description": "Update existing nsgroup:forwardstubserver"}, {"id": "delete_nsgroup_forwardstubserver", "object": "nsgroup:forwardstubserver", "operation": "delete", "name": "Delete nsgroup:forwardstubserver", "description": "Delete nsgroup:forwardstubserver"}, {"id": "list_nsgroup_stubmember", "object": "nsgroup:stubmember", "operation": "list", "name": "List nsgroup:stubmember", "description": "List/search nsgroup:stubmember objects with filters"}, {"id": "get_nsgroup_stubmember", "object": "nsgroup:stubmember", "operation": "get", "name": "Get nsgroup:stubmember", "description": "Get specific nsgroup:stubmember by reference"}, {"id": "create_nsgroup_stubmember", "object": "nsgroup:stubmember", "operation": "create", "name": "Create nsgroup:stubmember", "description": "Create new nsgroup:stubmember"}, {"id": "update_nsgroup_stubmember", "object": "nsgroup:stubmember", "operation": "update", "name": "Update nsgroup:stubmember", "description": "Update existing nsgroup:stubmember"}, {"id": "delete_nsgroup_stubmember", "object": "nsgroup:stubmember", "operation": "delete", "name": "Delete nsgroup:stubmember", "description": "Delete nsgroup:stubmember"}, {"id": "list_orderedranges", "object": "orderedranges", "operation": "list", "name": "List orderedranges", "description": "List/search orderedranges objects with filters"}, {"id": "get_orderedranges", "object": "orderedranges", "operation": "get", "name": "Get orderedranges", "description": "Get specific orderedranges by reference"}, {"id": "create_orderedranges", "object": "orderedranges", "operation": "create", "name": "Create orderedranges", "description": "Create new orderedranges"}, {"id": "update_orderedranges", "object": "orderedranges", "operation": "update", "name": "Update orderedranges", "description": "Update existing orderedranges"}, {"id": "delete_orderedranges", "object": "orderedranges", "operation": "delete", "name": "Delete orderedranges", "description": "Delete orderedranges"}, {"id": "list_orderedresponsepolicyzones", "object": "orderedresponsepolicyzones", "operation": "list", "name": "List orderedresponsepolicyzones", "description": "List/search orderedresponsepolicyzones objects with filters"}, {"id": "get_orderedresponsepolicyzones", "object": "orderedresponsepolicyzones", "operation": "get", "name": "Get orderedresponsepolicyzones", "description": "Get specific orderedresponsepolicyzones by reference"}, {"id": "create_orderedresponsepolicyzones", "object": "orderedresponsepolicyzones", "operation": "create", "name": "Create orderedresponsepolicyzones", "description": "Create new orderedresponsepolicyzones"}, {"id": "update_orderedresponsepolicyzones", "object": "orderedresponsepolicyzones", "operation": "update", "name": "Update orderedresponsepolicyzones", "description": "Update existing orderedresponsepolicyzones"}, {"id": "delete_orderedresponsepolicyzones", "object": "orderedresponsepolicyzones", "operation": "delete", "name": "Delete orderedresponsepolicyzones", "description": "Delete orderedresponsepolicyzones"}, {"id": "list_outbound_cloudclient", "object": "outbound:cloudclient", "operation": "list", "name": "List outbound:cloudclient", "description": "List/search outbound:cloudclient objects with filters"}, {"id": "get_outbound_cloudclient", "object": "outbound:cloudclient", "operation": "get", "name": "Get outbound:cloudclient", "description": "Get specific outbound:cloudclient by reference"}, {"id": "create_outbound_cloudclient", "object": "outbound:cloudclient", "operation": "create", "name": "Create outbound:cloudclient", "description": "Create new outbound:cloudclient"}, {"id": "update_outbound_cloudclient", "object": "outbound:cloudclient", "operation": "update", "name": "Update outbound:cloudclient", "description": "Update existing outbound:cloudclient"}, {"id": "delete_outbound_cloudclient", "object": "outbound:cloudclient", "operation": "delete", "name": "Delete outbound:cloudclient", "description": "Delete outbound:cloudclient"}, {"id": "list_parentalcontrol_avp", "object": "parentalcontrol:avp", "operation": "list", "name": "List parentalcontrol:avp", "description": "List/search parentalcontrol:avp objects with filters"}, {"id": "get_parentalcontrol_avp", "object": "parentalcontrol:avp", "operation": "get", "name": "Get parentalcontrol:avp", "description": "Get specific parentalcontrol:avp by reference"}, {"id": "create_parentalcontrol_avp", "object": "parentalcontrol:avp", "operation": "create", "name": "Create parentalcontrol:avp", "description": "Create new parentalcontrol:avp"}, {"id": "update_parentalcontrol_avp", "object": "parentalcontrol:avp", "operation": "update", "name": "Update parentalcontrol:avp", "description": "Update existing parentalcontrol:avp"}, {"id": "delete_parentalcontrol_avp", "object": "parentalcontrol:avp", "operation": "delete", "name": "Delete parentalcontrol:avp", "description": "Delete parentalcontrol:avp"}, {"id": "list_parentalcontrol_blockingpolicy", "object": "parentalcontrol:blockingpolicy", "operation": "list", "name": "List parentalcontrol:blockingpolicy", "description": "List/search parentalcontrol:blockingpolicy objects with filters"}, {"id": "get_parentalcontrol_blockingpolicy", "object": "parentalcontrol:blockingpolicy", "operation": "get", "name": "Get parentalcontrol:blockingpolicy", "description": "Get specific parentalcontrol:blockingpolicy by reference"}, {"id": "create_parentalcontrol_blockingpolicy", "object": "parentalcontrol:blockingpolicy", "operation": "create", "name": "Create parentalcontrol:blockingpolicy", "description": "Create new parentalcontrol:blockingpolicy"}, {"id": "update_parentalcontrol_blockingpolicy", "object": "parentalcontrol:blockingpolicy", "operation": "update", "name": "Update parentalcontrol:blockingpolicy", "description": "Update existing parentalcontrol:blockingpolicy"}, {"id": "delete_parentalcontrol_blockingpolicy", "object": "parentalcontrol:blockingpolicy", "operation": "delete", "name": "Delete parentalcontrol:blockingpolicy", "description": "Delete parentalcontrol:blockingpolicy"}, {"id": "list_parentalcontrol_subscriber", "object": "parentalcontrol:subscriber", "operation": "list", "name": "List parentalcontrol:subscriber", "description": "List/search parentalcontrol:subscriber objects with filters"}, {"id": "get_parentalcontrol_subscriber", "object": "parentalcontrol:subscriber", "operation": "get", "name": "Get parentalcontrol:subscriber", "description": "Get specific parentalcontrol:subscriber by reference"}, {"id": "create_parentalcontrol_subscriber", "object": "parentalcontrol:subscriber", "operation": "create", "name": "Create parentalcontrol:subscriber", "description": "Create new parentalcontrol:subscriber"}, {"id": "update_parentalcontrol_subscriber", "object": "parentalcontrol:subscriber", "operation": "update", "name": "Update parentalcontrol:subscriber", "description": "Update existing parentalcontrol:subscriber"}, {"id": "delete_parentalcontrol_subscriber", "object": "parentalcontrol:subscriber", "operation": "delete", "name": "Delete parentalcontrol:subscriber", "description": "Delete parentalcontrol:subscriber"}, {"id": "list_parentalcontrol_subscriberrecord", "object": "parentalcontrol:subscriberrecord", "operation": "list", "name": "List parentalcontrol:subscriberrecord", "description": "List/search parentalcontrol:subscriberrecord objects with filters"}, {"id": "get_parentalcontrol_subscriberrecord", "object": "parentalcontrol:subscriberrecord", "operation": "get", "name": "Get parentalcontrol:subscriberrecord", "description": "Get specific parentalcontrol:subscriberrecord by reference"}, {"id": "create_parentalcontrol_subscriberrecord", "object": "parentalcontrol:subscriberrecord", "operation": "create", "name": "Create parentalcontrol:subscriberrecord", "description": "Create new parentalcontrol:subscriberrecord"}, {"id": "update_parentalcontrol_subscriberrecord", "object": "parentalcontrol:subscriberrecord", "operation": "update", "name": "Update parentalcontrol:subscriberrecord", "description": "Update existing parentalcontrol:subscriberrecord"}, {"id": "delete_parentalcontrol_subscriberrecord", "object": "parentalcontrol:subscriberrecord", "operation": "delete", "name": "Delete parentalcontrol:subscriberrecord", "description": "Delete parentalcontrol:subscriberrecord"}, {"id": "list_parentalcontrol_subscribersite", "object": "parentalcontrol:subscribersite", "operation": "list", "name": "List parentalcontrol:subscribersite", "description": "List/search parentalcontrol:subscribersite objects with filters"}, {"id": "get_parentalcontrol_subscribersite", "object": "parentalcontrol:subscribersite", "operation": "get", "name": "Get parentalcontrol:subscribersite", "description": "Get specific parentalcontrol:subscribersite by reference"}, {"id": "create_parentalcontrol_subscribersite", "object": "parentalcontrol:subscribersite", "operation": "create", "name": "Create parentalcontrol:subscribersite", "description": "Create new parentalcontrol:subscribersite"}, {"id": "update_parentalcontrol_subscribersite", "object": "parentalcontrol:subscribersite", "operation": "update", "name": "Update parentalcontrol:subscribersite", "description": "Update existing parentalcontrol:subscribersite"}, {"id": "delete_parentalcontrol_subscribersite", "object": "parentalcontrol:subscribersite", "operation": "delete", "name": "Delete parentalcontrol:subscribersite", "description": "Delete parentalcontrol:subscribersite"}, {"id": "list_permission", "object": "permission", "operation": "list", "name": "List permission", "description": "List/search permission objects with filters"}, {"id": "get_permission", "object": "permission", "operation": "get", "name": "Get permission", "description": "Get specific permission by reference"}, {"id": "create_permission", "object": "permission", "operation": "create", "name": "Create permission", "description": "Create new permission"}, {"id": "update_permission", "object": "permission", "operation": "update", "name": "Update permission", "description": "Update existing permission"}, {"id": "delete_permission", "object": "permission", "operation": "delete", "name": "Delete permission", "description": "Delete permission"}, {"id": "list_pxgrid_endpoint", "object": "pxgrid:endpoint", "operation": "list", "name": "List pxgrid:endpoint", "description": "List/search pxgrid:endpoint objects with filters"}, {"id": "get_pxgrid_endpoint", "object": "pxgrid:endpoint", "operation": "get", "name": "Get pxgrid:endpoint", "description": "Get specific pxgrid:endpoint by reference"}, {"id": "create_pxgrid_endpoint", "object": "pxgrid:endpoint", "operation": "create", "name": "Create pxgrid:endpoint", "description": "Create new pxgrid:endpoint"}, {"id": "update_pxgrid_endpoint", "object": "pxgrid:endpoint", "operation": "update", "name": "Update pxgrid:endpoint", "description": "Update existing pxgrid:endpoint"}, {"id": "delete_pxgrid_endpoint", "object": "pxgrid:endpoint", "operation": "delete", "name": "Delete pxgrid:endpoint", "description": "Delete pxgrid:endpoint"}, {"id": "list_radius_authservice", "object": "radius:authservice", "operation": "list", "name": "List radius:authservice", "description": "List/search radius:authservice objects with filters"}, {"id": "get_radius_authservice", "object": "radius:authservice", "operation": "get", "name": "Get radius:authservice", "description": "Get specific radius:authservice by reference"}, {"id": "create_radius_authservice", "object": "radius:authservice", "operation": "create", "name": "Create radius:authservice", "description": "Create new radius:authservice"}, {"id": "update_radius_authservice", "object": "radius:authservice", "operation": "update", "name": "Update radius:authservice", "description": "Update existing radius:authservice"}, {"id": "delete_radius_authservice", "object": "radius:authservice", "operation": "delete", "name": "Delete radius:authservice", "description": "Delete radius:authservice"}, {"id": "list_range", "object": "range", "operation": "list", "name": "List range", "description": "List/search range objects with filters"}, {"id": "get_range", "object": "range", "operation": "get", "name": "Get range", "description": "Get specific range by reference"}, {"id": "create_range", "object": "range", "operation": "create", "name": "Create range", "description": "Create new range"}, {"id": "update_range", "object": "range", "operation": "update", "name": "Update range", "description": "Update existing range"}, {"id": "delete_range", "object": "range", "operation": "delete", "name": "Delete range", "description": "Delete range"}, {"id": "list_rangetemplate", "object": "rangetemplate", "operation": "list", "name": "List rangetemplate", "description": "List/search rangetemplate objects with filters"}, {"id": "get_rangetemplate", "object": "rangetemplate", "operation": "get", "name": "Get rangetemplate", "description": "Get specific rangetemplate by reference"}, {"id": "create_rangetemplate", "object": "rangetemplate", "operation": "create", "name": "Create rangetemplate", "description": "Create new rangetemplate"}, {"id": "update_rangetemplate", "object": "rangetemplate", "operation": "update", "name": "Update rangetemplate", "description": "Update existing rangetemplate"}, {"id": "delete_rangetemplate", "object": "rangetemplate", "operation": "delete", "name": "Delete rangetemplate", "description": "Delete rangetemplate"}, {"id": "list_record_a", "object": "record:a", "operation": "list", "name": "List record:a", "description": "List/search record:a objects with filters"}, {"id": "get_record_a", "object": "record:a", "operation": "get", "name": "Get record:a", "description": "Get specific record:a by reference"}, {"id": "create_record_a", "object": "record:a", "operation": "create", "name": "Create record:a", "description": "Create new record:a"}, {"id": "update_record_a", "object": "record:a", "operation": "update", "name": "Update record:a", "description": "Update existing record:a"}, {"id": "delete_record_a", "object": "record:a", "operation": "delete", "name": "Delete record:a", "description": "Delete record:a"}, {"id": "list_record_aaaa", "object": "record:aaaa", "operation": "list", "name": "List record:aaaa", "description": "List/search record:aaaa objects with filters"}, {"id": "get_record_aaaa", "object": "record:aaaa", "operation": "get", "name": "Get record:aaaa", "description": "Get specific record:aaaa by reference"}, {"id": "create_record_aaaa", "object": "record:aaaa", "operation": "create", "name": "Create record:aaaa", "description": "Create new record:aaaa"}, {"id": "update_record_aaaa", "object": "record:aaaa", "operation": "update", "name": "Update record:aaaa", "description": "Update existing record:aaaa"}, {"id": "delete_record_aaaa", "object": "record:aaaa", "operation": "delete", "name": "Delete record:aaaa", "description": "Delete record:aaaa"}, {"id": "list_record_alias", "object": "record:alias", "operation": "list", "name": "List record:alias", "description": "List/search record:alias objects with filters"}, {"id": "get_record_alias", "object": "record:alias", "operation": "get", "name": "Get record:alias", "description": "Get specific record:alias by reference"}, {"id": "create_record_alias", "object": "record:alias", "operation": "create", "name": "Create record:alias", "description": "Create new record:alias"}, {"id": "update_record_alias", "object": "record:alias", "operation": "update", "name": "Update record:alias", "description": "Update existing record:alias"}, {"id": "delete_record_alias", "object": "record:alias", "operation": "delete", "name": "Delete record:alias", "description": "Delete record:alias"}, {"id": "list_record_caa", "object": "record:caa", "operation": "list", "name": "List record:caa", "description": "List/search record:caa objects with filters"}, {"id": "get_record_caa", "object": "record:caa", "operation": "get", "name": "Get record:caa", "description": "Get specific record:caa by reference"}, {"id": "create_record_caa", "object": "record:caa", "operation": "create", "name": "Create record:caa", "description": "Create new record:caa"}, {"id": "update_record_caa", "object": "record:caa", "operation": "update", "name": "Update record:caa", "description": "Update existing record:caa"}, {"id": "delete_record_caa", "object": "record:caa", "operation": "delete", "name": "Delete record:caa", "description": "Delete record:caa"}, {"id": "list_record_cname", "object": "record:cname", "operation": "list", "name": "List record:cname", "description": "List/search record:cname objects with filters"}, {"id": "get_record_cname", "object": "record:cname", "operation": "get", "name": "Get record:cname", "description": "Get specific record:cname by reference"}, {"id": "create_record_cname", "object": "record:cname", "operation": "create", "name": "Create record:cname", "description": "Create new record:cname"}, {"id": "update_record_cname", "object": "record:cname", "operation": "update", "name": "Update record:cname", "description": "Update existing record:cname"}, {"id": "delete_record_cname", "object": "record:cname", "operation": "delete", "name": "Delete record:cname", "description": "Delete record:cname"}, {"id": "list_record_dhcid", "object": "record:dhcid", "operation": "list", "name": "List record:dhcid", "description": "List/search record:dhcid objects with filters"}, {"id": "get_record_dhcid", "object": "record:dhcid", "operation": "get", "name": "Get record:dhcid", "description": "Get specific record:dhcid by reference"}, {"id": "create_record_dhcid", "object": "record:dhcid", "operation": "create", "name": "Create record:dhcid", "description": "Create new record:dhcid"}, {"id": "update_record_dhcid", "object": "record:dhcid", "operation": "update", "name": "Update record:dhcid", "description": "Update existing record:dhcid"}, {"id": "delete_record_dhcid", "object": "record:dhcid", "operation": "delete", "name": "Delete record:dhcid", "description": "Delete record:dhcid"}, {"id": "list_record_dname", "object": "record:dname", "operation": "list", "name": "List record:dname", "description": "List/search record:dname objects with filters"}, {"id": "get_record_dname", "object": "record:dname", "operation": "get", "name": "Get record:dname", "description": "Get specific record:dname by reference"}, {"id": "create_record_dname", "object": "record:dname", "operation": "create", "name": "Create record:dname", "description": "Create new record:dname"}, {"id": "update_record_dname", "object": "record:dname", "operation": "update", "name": "Update record:dname", "description": "Update existing record:dname"}, {"id": "delete_record_dname", "object": "record:dname", "operation": "delete", "name": "Delete record:dname", "description": "Delete record:dname"}, {"id": "list_record_dnskey", "object": "record:dns<PERSON>", "operation": "list", "name": "List record:dnskey", "description": "List/search record:dnskey objects with filters"}, {"id": "get_record_dnskey", "object": "record:dns<PERSON>", "operation": "get", "name": "Get record:dns<PERSON>", "description": "Get specific record:dnskey by reference"}, {"id": "create_record_dnskey", "object": "record:dns<PERSON>", "operation": "create", "name": "Create record:dnskey", "description": "Create new record:dns<PERSON>"}, {"id": "update_record_dnskey", "object": "record:dns<PERSON>", "operation": "update", "name": "Update record:dns<PERSON>", "description": "Update existing record:d<PERSON><PERSON>"}, {"id": "delete_record_dnskey", "object": "record:dns<PERSON>", "operation": "delete", "name": "Delete record:dns<PERSON>", "description": "Delete record:dns<PERSON>"}, {"id": "list_record_ds", "object": "record:ds", "operation": "list", "name": "List record:ds", "description": "List/search record:ds objects with filters"}, {"id": "get_record_ds", "object": "record:ds", "operation": "get", "name": "Get record:ds", "description": "Get specific record:ds by reference"}, {"id": "create_record_ds", "object": "record:ds", "operation": "create", "name": "Create record:ds", "description": "Create new record:ds"}, {"id": "update_record_ds", "object": "record:ds", "operation": "update", "name": "Update record:ds", "description": "Update existing record:ds"}, {"id": "delete_record_ds", "object": "record:ds", "operation": "delete", "name": "Delete record:ds", "description": "Delete record:ds"}, {"id": "list_record_dtclbdn", "object": "record:dtclbdn", "operation": "list", "name": "List record:dtclbdn", "description": "List/search record:dtclbdn objects with filters"}, {"id": "get_record_dtclbdn", "object": "record:dtclbdn", "operation": "get", "name": "Get record:dtclbdn", "description": "Get specific record:dtclbdn by reference"}, {"id": "create_record_dtclbdn", "object": "record:dtclbdn", "operation": "create", "name": "Create record:dtclbdn", "description": "Create new record:dtclbdn"}, {"id": "update_record_dtclbdn", "object": "record:dtclbdn", "operation": "update", "name": "Update record:dtclbdn", "description": "Update existing record:dtclbdn"}, {"id": "delete_record_dtclbdn", "object": "record:dtclbdn", "operation": "delete", "name": "Delete record:dtclbdn", "description": "Delete record:dtclbdn"}, {"id": "list_record_host", "object": "record:host", "operation": "list", "name": "List record:host", "description": "List/search record:host objects with filters"}, {"id": "get_record_host", "object": "record:host", "operation": "get", "name": "Get record:host", "description": "Get specific record:host by reference"}, {"id": "create_record_host", "object": "record:host", "operation": "create", "name": "Create record:host", "description": "Create new record:host"}, {"id": "update_record_host", "object": "record:host", "operation": "update", "name": "Update record:host", "description": "Update existing record:host"}, {"id": "delete_record_host", "object": "record:host", "operation": "delete", "name": "Delete record:host", "description": "Delete record:host"}, {"id": "list_record_host_ipv4addr", "object": "record:host_ipv4addr", "operation": "list", "name": "List record:host_ipv4addr", "description": "List/search record:host_ipv4addr objects with filters"}, {"id": "get_record_host_ipv4addr", "object": "record:host_ipv4addr", "operation": "get", "name": "Get record:host_ipv4addr", "description": "Get specific record:host_ipv4addr by reference"}, {"id": "create_record_host_ipv4addr", "object": "record:host_ipv4addr", "operation": "create", "name": "Create record:host_ipv4addr", "description": "Create new record:host_ipv4addr"}, {"id": "update_record_host_ipv4addr", "object": "record:host_ipv4addr", "operation": "update", "name": "Update record:host_ipv4addr", "description": "Update existing record:host_ipv4addr"}, {"id": "delete_record_host_ipv4addr", "object": "record:host_ipv4addr", "operation": "delete", "name": "Delete record:host_ipv4addr", "description": "Delete record:host_ipv4addr"}, {"id": "list_record_host_ipv6addr", "object": "record:host_ipv6addr", "operation": "list", "name": "List record:host_ipv6addr", "description": "List/search record:host_ipv6addr objects with filters"}, {"id": "get_record_host_ipv6addr", "object": "record:host_ipv6addr", "operation": "get", "name": "Get record:host_ipv6addr", "description": "Get specific record:host_ipv6addr by reference"}, {"id": "create_record_host_ipv6addr", "object": "record:host_ipv6addr", "operation": "create", "name": "Create record:host_ipv6addr", "description": "Create new record:host_ipv6addr"}, {"id": "update_record_host_ipv6addr", "object": "record:host_ipv6addr", "operation": "update", "name": "Update record:host_ipv6addr", "description": "Update existing record:host_ipv6addr"}, {"id": "delete_record_host_ipv6addr", "object": "record:host_ipv6addr", "operation": "delete", "name": "Delete record:host_ipv6addr", "description": "Delete record:host_ipv6addr"}, {"id": "list_record_mx", "object": "record:mx", "operation": "list", "name": "List record:mx", "description": "List/search record:mx objects with filters"}, {"id": "get_record_mx", "object": "record:mx", "operation": "get", "name": "Get record:mx", "description": "Get specific record:mx by reference"}, {"id": "create_record_mx", "object": "record:mx", "operation": "create", "name": "Create record:mx", "description": "Create new record:mx"}, {"id": "update_record_mx", "object": "record:mx", "operation": "update", "name": "Update record:mx", "description": "Update existing record:mx"}, {"id": "delete_record_mx", "object": "record:mx", "operation": "delete", "name": "Delete record:mx", "description": "Delete record:mx"}, {"id": "list_record_naptr", "object": "record:naptr", "operation": "list", "name": "List record:naptr", "description": "List/search record:naptr objects with filters"}, {"id": "get_record_naptr", "object": "record:naptr", "operation": "get", "name": "Get record:naptr", "description": "Get specific record:naptr by reference"}, {"id": "create_record_naptr", "object": "record:naptr", "operation": "create", "name": "Create record:naptr", "description": "Create new record:naptr"}, {"id": "update_record_naptr", "object": "record:naptr", "operation": "update", "name": "Update record:naptr", "description": "Update existing record:naptr"}, {"id": "delete_record_naptr", "object": "record:naptr", "operation": "delete", "name": "Delete record:naptr", "description": "Delete record:naptr"}, {"id": "list_record_ns", "object": "record:ns", "operation": "list", "name": "List record:ns", "description": "List/search record:ns objects with filters"}, {"id": "get_record_ns", "object": "record:ns", "operation": "get", "name": "Get record:ns", "description": "Get specific record:ns by reference"}, {"id": "create_record_ns", "object": "record:ns", "operation": "create", "name": "Create record:ns", "description": "Create new record:ns"}, {"id": "update_record_ns", "object": "record:ns", "operation": "update", "name": "Update record:ns", "description": "Update existing record:ns"}, {"id": "delete_record_ns", "object": "record:ns", "operation": "delete", "name": "Delete record:ns", "description": "Delete record:ns"}, {"id": "list_record_nsec", "object": "record:nsec", "operation": "list", "name": "List record:nsec", "description": "List/search record:nsec objects with filters"}, {"id": "get_record_nsec", "object": "record:nsec", "operation": "get", "name": "Get record:nsec", "description": "Get specific record:nsec by reference"}, {"id": "create_record_nsec", "object": "record:nsec", "operation": "create", "name": "Create record:nsec", "description": "Create new record:nsec"}, {"id": "update_record_nsec", "object": "record:nsec", "operation": "update", "name": "Update record:nsec", "description": "Update existing record:nsec"}, {"id": "delete_record_nsec", "object": "record:nsec", "operation": "delete", "name": "Delete record:nsec", "description": "Delete record:nsec"}, {"id": "list_record_nsec3", "object": "record:nsec3", "operation": "list", "name": "List record:nsec3", "description": "List/search record:nsec3 objects with filters"}, {"id": "get_record_nsec3", "object": "record:nsec3", "operation": "get", "name": "Get record:nsec3", "description": "Get specific record:nsec3 by reference"}, {"id": "create_record_nsec3", "object": "record:nsec3", "operation": "create", "name": "Create record:nsec3", "description": "Create new record:nsec3"}, {"id": "update_record_nsec3", "object": "record:nsec3", "operation": "update", "name": "Update record:nsec3", "description": "Update existing record:nsec3"}, {"id": "delete_record_nsec3", "object": "record:nsec3", "operation": "delete", "name": "Delete record:nsec3", "description": "Delete record:nsec3"}, {"id": "list_record_nsec3param", "object": "record:nsec3param", "operation": "list", "name": "List record:nsec3param", "description": "List/search record:nsec3param objects with filters"}, {"id": "get_record_nsec3param", "object": "record:nsec3param", "operation": "get", "name": "Get record:nsec3param", "description": "Get specific record:nsec3param by reference"}, {"id": "create_record_nsec3param", "object": "record:nsec3param", "operation": "create", "name": "Create record:nsec3param", "description": "Create new record:nsec3param"}, {"id": "update_record_nsec3param", "object": "record:nsec3param", "operation": "update", "name": "Update record:nsec3param", "description": "Update existing record:nsec3param"}, {"id": "delete_record_nsec3param", "object": "record:nsec3param", "operation": "delete", "name": "Delete record:nsec3param", "description": "Delete record:nsec3param"}, {"id": "list_record_ptr", "object": "record:ptr", "operation": "list", "name": "List record:ptr", "description": "List/search record:ptr objects with filters"}, {"id": "get_record_ptr", "object": "record:ptr", "operation": "get", "name": "Get record:ptr", "description": "Get specific record:ptr by reference"}, {"id": "create_record_ptr", "object": "record:ptr", "operation": "create", "name": "Create record:ptr", "description": "Create new record:ptr"}, {"id": "update_record_ptr", "object": "record:ptr", "operation": "update", "name": "Update record:ptr", "description": "Update existing record:ptr"}, {"id": "delete_record_ptr", "object": "record:ptr", "operation": "delete", "name": "Delete record:ptr", "description": "Delete record:ptr"}, {"id": "list_record_rpz_a", "object": "record:rpz:a", "operation": "list", "name": "List record:rpz:a", "description": "List/search record:rpz:a objects with filters"}, {"id": "get_record_rpz_a", "object": "record:rpz:a", "operation": "get", "name": "Get record:rpz:a", "description": "Get specific record:rpz:a by reference"}, {"id": "create_record_rpz_a", "object": "record:rpz:a", "operation": "create", "name": "Create record:rpz:a", "description": "Create new record:rpz:a"}, {"id": "update_record_rpz_a", "object": "record:rpz:a", "operation": "update", "name": "Update record:rpz:a", "description": "Update existing record:rpz:a"}, {"id": "delete_record_rpz_a", "object": "record:rpz:a", "operation": "delete", "name": "Delete record:rpz:a", "description": "Delete record:rpz:a"}, {"id": "list_record_rpz_a_ipaddress", "object": "record:rpz:a:ipaddress", "operation": "list", "name": "List record:rpz:a:ipaddress", "description": "List/search record:rpz:a:ipaddress objects with filters"}, {"id": "get_record_rpz_a_ipaddress", "object": "record:rpz:a:ipaddress", "operation": "get", "name": "Get record:rpz:a:ipaddress", "description": "Get specific record:rpz:a:ipaddress by reference"}, {"id": "create_record_rpz_a_ipaddress", "object": "record:rpz:a:ipaddress", "operation": "create", "name": "Create record:rpz:a:ipaddress", "description": "Create new record:rpz:a:ipaddress"}, {"id": "update_record_rpz_a_ipaddress", "object": "record:rpz:a:ipaddress", "operation": "update", "name": "Update record:rpz:a:ipaddress", "description": "Update existing record:rpz:a:ipaddress"}, {"id": "delete_record_rpz_a_ipaddress", "object": "record:rpz:a:ipaddress", "operation": "delete", "name": "Delete record:rpz:a:ipaddress", "description": "Delete record:rpz:a:ipaddress"}, {"id": "list_record_rpz_aaaa", "object": "record:rpz:aaaa", "operation": "list", "name": "List record:rpz:aaaa", "description": "List/search record:rpz:aaaa objects with filters"}, {"id": "get_record_rpz_aaaa", "object": "record:rpz:aaaa", "operation": "get", "name": "Get record:rpz:aaaa", "description": "Get specific record:rpz:aaaa by reference"}, {"id": "create_record_rpz_aaaa", "object": "record:rpz:aaaa", "operation": "create", "name": "Create record:rpz:aaaa", "description": "Create new record:rpz:aaaa"}, {"id": "update_record_rpz_aaaa", "object": "record:rpz:aaaa", "operation": "update", "name": "Update record:rpz:aaaa", "description": "Update existing record:rpz:aaaa"}, {"id": "delete_record_rpz_aaaa", "object": "record:rpz:aaaa", "operation": "delete", "name": "Delete record:rpz:aaaa", "description": "Delete record:rpz:aaaa"}, {"id": "list_record_rpz_aaaa_ipaddress", "object": "record:rpz:aaaa:ipad<PERSON>", "operation": "list", "name": "List record:rpz:aaaa:ipad<PERSON>", "description": "List/search record:rpz:aaaa:ipaddress objects with filters"}, {"id": "get_record_rpz_aaaa_ipaddress", "object": "record:rpz:aaaa:ipad<PERSON>", "operation": "get", "name": "Get record:rpz:aaaa:ipaddress", "description": "Get specific record:rpz:aaaa:ipaddress by reference"}, {"id": "create_record_rpz_aaaa_ipaddress", "object": "record:rpz:aaaa:ipad<PERSON>", "operation": "create", "name": "Create record:rpz:aaaa:ipad<PERSON>", "description": "Create new record:rpz:aaaa:ipad<PERSON>"}, {"id": "update_record_rpz_aaaa_ipaddress", "object": "record:rpz:aaaa:ipad<PERSON>", "operation": "update", "name": "Update record:rpz:aaaa:ipad<PERSON>", "description": "Update existing record:rpz:aaaa:ipad<PERSON>"}, {"id": "delete_record_rpz_aaaa_ipaddress", "object": "record:rpz:aaaa:ipad<PERSON>", "operation": "delete", "name": "Delete record:rpz:aaaa:ipaddress", "description": "Delete record:rpz:aaaa:ipaddress"}, {"id": "list_record_rpz_cname", "object": "record:rpz:cname", "operation": "list", "name": "List record:rpz:cname", "description": "List/search record:rpz:cname objects with filters"}, {"id": "get_record_rpz_cname", "object": "record:rpz:cname", "operation": "get", "name": "Get record:rpz:cname", "description": "Get specific record:rpz:cname by reference"}, {"id": "create_record_rpz_cname", "object": "record:rpz:cname", "operation": "create", "name": "Create record:rpz:cname", "description": "Create new record:rpz:cname"}, {"id": "update_record_rpz_cname", "object": "record:rpz:cname", "operation": "update", "name": "Update record:rpz:cname", "description": "Update existing record:rpz:cname"}, {"id": "delete_record_rpz_cname", "object": "record:rpz:cname", "operation": "delete", "name": "Delete record:rpz:cname", "description": "Delete record:rpz:cname"}, {"id": "list_record_rpz_cname_clientipaddress", "object": "record:rpz:cname:clientipaddress", "operation": "list", "name": "List record:rpz:cname:clientipaddress", "description": "List/search record:rpz:cname:clientipaddress objects with filters"}, {"id": "get_record_rpz_cname_clientipaddress", "object": "record:rpz:cname:clientipaddress", "operation": "get", "name": "Get record:rpz:cname:clientipaddress", "description": "Get specific record:rpz:cname:clientipaddress by reference"}, {"id": "create_record_rpz_cname_clientipaddress", "object": "record:rpz:cname:clientipaddress", "operation": "create", "name": "Create record:rpz:cname:clientipaddress", "description": "Create new record:rpz:cname:clientipaddress"}, {"id": "update_record_rpz_cname_clientipaddress", "object": "record:rpz:cname:clientipaddress", "operation": "update", "name": "Update record:rpz:cname:clientipaddress", "description": "Update existing record:rpz:cname:clientipaddress"}, {"id": "delete_record_rpz_cname_clientipaddress", "object": "record:rpz:cname:clientipaddress", "operation": "delete", "name": "Delete record:rpz:cname:clientipaddress", "description": "Delete record:rpz:cname:clientipaddress"}, {"id": "list_record_rpz_cname_clientipaddressdn", "object": "record:rpz:cname:clientipaddressdn", "operation": "list", "name": "List record:rpz:cname:clientipaddressdn", "description": "List/search record:rpz:cname:clientipaddressdn objects with filters"}, {"id": "get_record_rpz_cname_clientipaddressdn", "object": "record:rpz:cname:clientipaddressdn", "operation": "get", "name": "Get record:rpz:cname:clientipaddressdn", "description": "Get specific record:rpz:cname:clientipaddressdn by reference"}, {"id": "create_record_rpz_cname_clientipaddressdn", "object": "record:rpz:cname:clientipaddressdn", "operation": "create", "name": "Create record:rpz:cname:clientipaddressdn", "description": "Create new record:rpz:cname:clientipaddressdn"}, {"id": "update_record_rpz_cname_clientipaddressdn", "object": "record:rpz:cname:clientipaddressdn", "operation": "update", "name": "Update record:rpz:cname:clientipaddressdn", "description": "Update existing record:rpz:cname:clientipaddressdn"}, {"id": "delete_record_rpz_cname_clientipaddressdn", "object": "record:rpz:cname:clientipaddressdn", "operation": "delete", "name": "Delete record:rpz:cname:clientipaddressdn", "description": "Delete record:rpz:cname:clientipaddressdn"}, {"id": "list_record_rpz_cname_ipaddress", "object": "record:rpz:cname:ipaddress", "operation": "list", "name": "List record:rpz:cname:ipaddress", "description": "List/search record:rpz:cname:ipaddress objects with filters"}, {"id": "get_record_rpz_cname_ipaddress", "object": "record:rpz:cname:ipaddress", "operation": "get", "name": "Get record:rpz:cname:ipaddress", "description": "Get specific record:rpz:cname:ipaddress by reference"}, {"id": "create_record_rpz_cname_ipaddress", "object": "record:rpz:cname:ipaddress", "operation": "create", "name": "Create record:rpz:cname:ipaddress", "description": "Create new record:rpz:cname:ipaddress"}, {"id": "update_record_rpz_cname_ipaddress", "object": "record:rpz:cname:ipaddress", "operation": "update", "name": "Update record:rpz:cname:ipaddress", "description": "Update existing record:rpz:cname:ipaddress"}, {"id": "delete_record_rpz_cname_ipaddress", "object": "record:rpz:cname:ipaddress", "operation": "delete", "name": "Delete record:rpz:cname:ipaddress", "description": "Delete record:rpz:cname:ipaddress"}, {"id": "list_record_rpz_cname_ipaddressdn", "object": "record:rpz:cname:ipaddressdn", "operation": "list", "name": "List record:rpz:cname:ipaddressdn", "description": "List/search record:rpz:cname:ipaddressdn objects with filters"}, {"id": "get_record_rpz_cname_ipaddressdn", "object": "record:rpz:cname:ipaddressdn", "operation": "get", "name": "Get record:rpz:cname:ipaddressdn", "description": "Get specific record:rpz:cname:ipaddressdn by reference"}, {"id": "create_record_rpz_cname_ipaddressdn", "object": "record:rpz:cname:ipaddressdn", "operation": "create", "name": "Create record:rpz:cname:ipaddressdn", "description": "Create new record:rpz:cname:ipaddressdn"}, {"id": "update_record_rpz_cname_ipaddressdn", "object": "record:rpz:cname:ipaddressdn", "operation": "update", "name": "Update record:rpz:cname:ipaddressdn", "description": "Update existing record:rpz:cname:ipaddressdn"}, {"id": "delete_record_rpz_cname_ipaddressdn", "object": "record:rpz:cname:ipaddressdn", "operation": "delete", "name": "Delete record:rpz:cname:ipaddressdn", "description": "Delete record:rpz:cname:ipaddressdn"}, {"id": "list_record_rpz_mx", "object": "record:rpz:mx", "operation": "list", "name": "List record:rpz:mx", "description": "List/search record:rpz:mx objects with filters"}, {"id": "get_record_rpz_mx", "object": "record:rpz:mx", "operation": "get", "name": "Get record:rpz:mx", "description": "Get specific record:rpz:mx by reference"}, {"id": "create_record_rpz_mx", "object": "record:rpz:mx", "operation": "create", "name": "Create record:rpz:mx", "description": "Create new record:rpz:mx"}, {"id": "update_record_rpz_mx", "object": "record:rpz:mx", "operation": "update", "name": "Update record:rpz:mx", "description": "Update existing record:rpz:mx"}, {"id": "delete_record_rpz_mx", "object": "record:rpz:mx", "operation": "delete", "name": "Delete record:rpz:mx", "description": "Delete record:rpz:mx"}, {"id": "list_record_rpz_naptr", "object": "record:rpz:naptr", "operation": "list", "name": "List record:rpz:naptr", "description": "List/search record:rpz:naptr objects with filters"}, {"id": "get_record_rpz_naptr", "object": "record:rpz:naptr", "operation": "get", "name": "Get record:rpz:naptr", "description": "Get specific record:rpz:naptr by reference"}, {"id": "create_record_rpz_naptr", "object": "record:rpz:naptr", "operation": "create", "name": "Create record:rpz:naptr", "description": "Create new record:rpz:naptr"}, {"id": "update_record_rpz_naptr", "object": "record:rpz:naptr", "operation": "update", "name": "Update record:rpz:naptr", "description": "Update existing record:rpz:naptr"}, {"id": "delete_record_rpz_naptr", "object": "record:rpz:naptr", "operation": "delete", "name": "Delete record:rpz:naptr", "description": "Delete record:rpz:naptr"}, {"id": "list_record_rpz_ptr", "object": "record:rpz:ptr", "operation": "list", "name": "List record:rpz:ptr", "description": "List/search record:rpz:ptr objects with filters"}, {"id": "get_record_rpz_ptr", "object": "record:rpz:ptr", "operation": "get", "name": "Get record:rpz:ptr", "description": "Get specific record:rpz:ptr by reference"}, {"id": "create_record_rpz_ptr", "object": "record:rpz:ptr", "operation": "create", "name": "Create record:rpz:ptr", "description": "Create new record:rpz:ptr"}, {"id": "update_record_rpz_ptr", "object": "record:rpz:ptr", "operation": "update", "name": "Update record:rpz:ptr", "description": "Update existing record:rpz:ptr"}, {"id": "delete_record_rpz_ptr", "object": "record:rpz:ptr", "operation": "delete", "name": "Delete record:rpz:ptr", "description": "Delete record:rpz:ptr"}, {"id": "list_record_rpz_srv", "object": "record:rpz:srv", "operation": "list", "name": "List record:rpz:srv", "description": "List/search record:rpz:srv objects with filters"}, {"id": "get_record_rpz_srv", "object": "record:rpz:srv", "operation": "get", "name": "Get record:rpz:srv", "description": "Get specific record:rpz:srv by reference"}, {"id": "create_record_rpz_srv", "object": "record:rpz:srv", "operation": "create", "name": "Create record:rpz:srv", "description": "Create new record:rpz:srv"}, {"id": "update_record_rpz_srv", "object": "record:rpz:srv", "operation": "update", "name": "Update record:rpz:srv", "description": "Update existing record:rpz:srv"}, {"id": "delete_record_rpz_srv", "object": "record:rpz:srv", "operation": "delete", "name": "Delete record:rpz:srv", "description": "Delete record:rpz:srv"}, {"id": "list_record_rpz_txt", "object": "record:rpz:txt", "operation": "list", "name": "List record:rpz:txt", "description": "List/search record:rpz:txt objects with filters"}, {"id": "get_record_rpz_txt", "object": "record:rpz:txt", "operation": "get", "name": "Get record:rpz:txt", "description": "Get specific record:rpz:txt by reference"}, {"id": "create_record_rpz_txt", "object": "record:rpz:txt", "operation": "create", "name": "Create record:rpz:txt", "description": "Create new record:rpz:txt"}, {"id": "update_record_rpz_txt", "object": "record:rpz:txt", "operation": "update", "name": "Update record:rpz:txt", "description": "Update existing record:rpz:txt"}, {"id": "delete_record_rpz_txt", "object": "record:rpz:txt", "operation": "delete", "name": "Delete record:rpz:txt", "description": "Delete record:rpz:txt"}, {"id": "list_record_rrsig", "object": "record:rrsig", "operation": "list", "name": "List record:rrsig", "description": "List/search record:rrsig objects with filters"}, {"id": "get_record_rrsig", "object": "record:rrsig", "operation": "get", "name": "Get record:rrsig", "description": "Get specific record:rrsig by reference"}, {"id": "create_record_rrsig", "object": "record:rrsig", "operation": "create", "name": "Create record:rrsig", "description": "Create new record:rrsig"}, {"id": "update_record_rrsig", "object": "record:rrsig", "operation": "update", "name": "Update record:rrsig", "description": "Update existing record:rrsig"}, {"id": "delete_record_rrsig", "object": "record:rrsig", "operation": "delete", "name": "Delete record:rrsig", "description": "Delete record:rrsig"}, {"id": "list_record_srv", "object": "record:srv", "operation": "list", "name": "List record:srv", "description": "List/search record:srv objects with filters"}, {"id": "get_record_srv", "object": "record:srv", "operation": "get", "name": "Get record:srv", "description": "Get specific record:srv by reference"}, {"id": "create_record_srv", "object": "record:srv", "operation": "create", "name": "Create record:srv", "description": "Create new record:srv"}, {"id": "update_record_srv", "object": "record:srv", "operation": "update", "name": "Update record:srv", "description": "Update existing record:srv"}, {"id": "delete_record_srv", "object": "record:srv", "operation": "delete", "name": "Delete record:srv", "description": "Delete record:srv"}, {"id": "list_record_tlsa", "object": "record:tlsa", "operation": "list", "name": "List record:tlsa", "description": "List/search record:tlsa objects with filters"}, {"id": "get_record_tlsa", "object": "record:tlsa", "operation": "get", "name": "Get record:tlsa", "description": "Get specific record:tlsa by reference"}, {"id": "create_record_tlsa", "object": "record:tlsa", "operation": "create", "name": "Create record:tlsa", "description": "Create new record:tlsa"}, {"id": "update_record_tlsa", "object": "record:tlsa", "operation": "update", "name": "Update record:tlsa", "description": "Update existing record:tlsa"}, {"id": "delete_record_tlsa", "object": "record:tlsa", "operation": "delete", "name": "Delete record:tlsa", "description": "Delete record:tlsa"}, {"id": "list_record_txt", "object": "record:txt", "operation": "list", "name": "List record:txt", "description": "List/search record:txt objects with filters"}, {"id": "get_record_txt", "object": "record:txt", "operation": "get", "name": "Get record:txt", "description": "Get specific record:txt by reference"}, {"id": "create_record_txt", "object": "record:txt", "operation": "create", "name": "Create record:txt", "description": "Create new record:txt"}, {"id": "update_record_txt", "object": "record:txt", "operation": "update", "name": "Update record:txt", "description": "Update existing record:txt"}, {"id": "delete_record_txt", "object": "record:txt", "operation": "delete", "name": "Delete record:txt", "description": "Delete record:txt"}, {"id": "list_record_unknown", "object": "record:unknown", "operation": "list", "name": "List record:unknown", "description": "List/search record:unknown objects with filters"}, {"id": "get_record_unknown", "object": "record:unknown", "operation": "get", "name": "Get record:unknown", "description": "Get specific record:unknown by reference"}, {"id": "create_record_unknown", "object": "record:unknown", "operation": "create", "name": "Create record:unknown", "description": "Create new record:unknown"}, {"id": "update_record_unknown", "object": "record:unknown", "operation": "update", "name": "Update record:unknown", "description": "Update existing record:unknown"}, {"id": "delete_record_unknown", "object": "record:unknown", "operation": "delete", "name": "Delete record:unknown", "description": "Delete record:unknown"}, {"id": "list_recordnamepolicy", "object": "recordnamepolicy", "operation": "list", "name": "List recordnamepolicy", "description": "List/search recordnamepolicy objects with filters"}, {"id": "get_recordnamepolicy", "object": "recordnamepolicy", "operation": "get", "name": "Get recordnamepolicy", "description": "Get specific recordnamepolicy by reference"}, {"id": "create_recordnamepolicy", "object": "recordnamepolicy", "operation": "create", "name": "Create recordnamepolicy", "description": "Create new recordnamepolicy"}, {"id": "update_recordnamepolicy", "object": "recordnamepolicy", "operation": "update", "name": "Update recordnamepolicy", "description": "Update existing recordnamepolicy"}, {"id": "delete_recordnamepolicy", "object": "recordnamepolicy", "operation": "delete", "name": "Delete recordnamepolicy", "description": "Delete recordnamepolicy"}, {"id": "list_request", "object": "request", "operation": "list", "name": "List request", "description": "List/search request objects with filters"}, {"id": "get_request", "object": "request", "operation": "get", "name": "Get request", "description": "Get specific request by reference"}, {"id": "create_request", "object": "request", "operation": "create", "name": "Create request", "description": "Create new request"}, {"id": "update_request", "object": "request", "operation": "update", "name": "Update request", "description": "Update existing request"}, {"id": "delete_request", "object": "request", "operation": "delete", "name": "Delete request", "description": "Delete request"}, {"id": "list_restartservicestatus", "object": "restartservicestatus", "operation": "list", "name": "List restartservicestatus", "description": "List/search restartservicestatus objects with filters"}, {"id": "get_restartservicestatus", "object": "restartservicestatus", "operation": "get", "name": "Get restartservicestatus", "description": "Get specific restartservicestatus by reference"}, {"id": "create_restartservicestatus", "object": "restartservicestatus", "operation": "create", "name": "Create restartservicestatus", "description": "Create new restartservicestatus"}, {"id": "update_restartservicestatus", "object": "restartservicestatus", "operation": "update", "name": "Update restartservicestatus", "description": "Update existing restartservicestatus"}, {"id": "delete_restartservicestatus", "object": "restartservicestatus", "operation": "delete", "name": "Delete restartservicestatus", "description": "Delete restartservicestatus"}, {"id": "list_rir", "object": "rir", "operation": "list", "name": "List rir", "description": "List/search rir objects with filters"}, {"id": "get_rir", "object": "rir", "operation": "get", "name": "Get rir", "description": "Get specific rir by reference"}, {"id": "create_rir", "object": "rir", "operation": "create", "name": "Create rir", "description": "Create new rir"}, {"id": "update_rir", "object": "rir", "operation": "update", "name": "Update rir", "description": "Update existing rir"}, {"id": "delete_rir", "object": "rir", "operation": "delete", "name": "Delete rir", "description": "Delete rir"}, {"id": "list_rir_organization", "object": "rir:organization", "operation": "list", "name": "List rir:organization", "description": "List/search rir:organization objects with filters"}, {"id": "get_rir_organization", "object": "rir:organization", "operation": "get", "name": "Get rir:organization", "description": "Get specific rir:organization by reference"}, {"id": "create_rir_organization", "object": "rir:organization", "operation": "create", "name": "Create rir:organization", "description": "Create new rir:organization"}, {"id": "update_rir_organization", "object": "rir:organization", "operation": "update", "name": "Update rir:organization", "description": "Update existing rir:organization"}, {"id": "delete_rir_organization", "object": "rir:organization", "operation": "delete", "name": "Delete rir:organization", "description": "Delete rir:organization"}, {"id": "list_roaminghost", "object": "roaminghost", "operation": "list", "name": "List roaminghost", "description": "List/search roaminghost objects with filters"}, {"id": "get_roaminghost", "object": "roaminghost", "operation": "get", "name": "Get roaminghost", "description": "Get specific roaminghost by reference"}, {"id": "create_roaminghost", "object": "roaminghost", "operation": "create", "name": "Create roaminghost", "description": "Create new roaminghost"}, {"id": "update_roaminghost", "object": "roaminghost", "operation": "update", "name": "Update roaminghost", "description": "Update existing roaminghost"}, {"id": "delete_roaminghost", "object": "roaminghost", "operation": "delete", "name": "Delete roaminghost", "description": "Delete roaminghost"}, {"id": "list_ruleset", "object": "ruleset", "operation": "list", "name": "List ruleset", "description": "List/search ruleset objects with filters"}, {"id": "get_ruleset", "object": "ruleset", "operation": "get", "name": "Get ruleset", "description": "Get specific ruleset by reference"}, {"id": "create_ruleset", "object": "ruleset", "operation": "create", "name": "Create ruleset", "description": "Create new ruleset"}, {"id": "update_ruleset", "object": "ruleset", "operation": "update", "name": "Update ruleset", "description": "Update existing ruleset"}, {"id": "delete_ruleset", "object": "ruleset", "operation": "delete", "name": "Delete ruleset", "description": "Delete ruleset"}, {"id": "list_saml_authservice", "object": "saml:authservice", "operation": "list", "name": "List saml:authservice", "description": "List/search saml:authservice objects with filters"}, {"id": "get_saml_authservice", "object": "saml:authservice", "operation": "get", "name": "Get saml:authservice", "description": "Get specific saml:authservice by reference"}, {"id": "create_saml_authservice", "object": "saml:authservice", "operation": "create", "name": "Create saml:authservice", "description": "Create new saml:authservice"}, {"id": "update_saml_authservice", "object": "saml:authservice", "operation": "update", "name": "Update saml:authservice", "description": "Update existing saml:authservice"}, {"id": "delete_saml_authservice", "object": "saml:authservice", "operation": "delete", "name": "Delete saml:authservice", "description": "Delete saml:authservice"}, {"id": "list_scavengingtask", "object": "scavengingtask", "operation": "list", "name": "List scavengingtask", "description": "List/search scavengingtask objects with filters"}, {"id": "get_scavengingtask", "object": "scavengingtask", "operation": "get", "name": "Get scavengingtask", "description": "Get specific scavengingtask by reference"}, {"id": "create_scavengingtask", "object": "scavengingtask", "operation": "create", "name": "Create scavengingtask", "description": "Create new scavengingtask"}, {"id": "update_scavengingtask", "object": "scavengingtask", "operation": "update", "name": "Update scavengingtask", "description": "Update existing scavengingtask"}, {"id": "delete_scavengingtask", "object": "scavengingtask", "operation": "delete", "name": "Delete scavengingtask", "description": "Delete scavengingtask"}, {"id": "list_scheduledtask", "object": "scheduledtask", "operation": "list", "name": "List scheduledtask", "description": "List/search scheduledtask objects with filters"}, {"id": "get_scheduledtask", "object": "scheduledtask", "operation": "get", "name": "Get scheduledtask", "description": "Get specific scheduledtask by reference"}, {"id": "create_scheduledtask", "object": "scheduledtask", "operation": "create", "name": "Create scheduledtask", "description": "Create new scheduledtask"}, {"id": "update_scheduledtask", "object": "scheduledtask", "operation": "update", "name": "Update scheduledtask", "description": "Update existing scheduledtask"}, {"id": "delete_scheduledtask", "object": "scheduledtask", "operation": "delete", "name": "Delete scheduledtask", "description": "Delete scheduledtask"}, {"id": "list_search", "object": "search", "operation": "list", "name": "List search", "description": "List/search search objects with filters"}, {"id": "get_search", "object": "search", "operation": "get", "name": "Get search", "description": "Get specific search by reference"}, {"id": "create_search", "object": "search", "operation": "create", "name": "Create search", "description": "Create new search"}, {"id": "update_search", "object": "search", "operation": "update", "name": "Update search", "description": "Update existing search"}, {"id": "delete_search", "object": "search", "operation": "delete", "name": "Delete search", "description": "Delete search"}, {"id": "list_sharednetwork", "object": "sharednetwork", "operation": "list", "name": "List sharednetwork", "description": "List/search sharednetwork objects with filters"}, {"id": "get_sharednetwork", "object": "sharednetwork", "operation": "get", "name": "Get sharednetwork", "description": "Get specific sharednetwork by reference"}, {"id": "create_sharednetwork", "object": "sharednetwork", "operation": "create", "name": "Create sharednetwork", "description": "Create new sharednetwork"}, {"id": "update_sharednetwork", "object": "sharednetwork", "operation": "update", "name": "Update sharednetwork", "description": "Update existing sharednetwork"}, {"id": "delete_sharednetwork", "object": "sharednetwork", "operation": "delete", "name": "Delete sharednetwork", "description": "Delete sharednetwork"}, {"id": "list_sharedrecord_a", "object": "sharedrecord:a", "operation": "list", "name": "List sharedrecord:a", "description": "List/search sharedrecord:a objects with filters"}, {"id": "get_sharedrecord_a", "object": "sharedrecord:a", "operation": "get", "name": "Get sharedrecord:a", "description": "Get specific sharedrecord:a by reference"}, {"id": "create_sharedrecord_a", "object": "sharedrecord:a", "operation": "create", "name": "Create sharedrecord:a", "description": "Create new sharedrecord:a"}, {"id": "update_sharedrecord_a", "object": "sharedrecord:a", "operation": "update", "name": "Update sharedrecord:a", "description": "Update existing sharedrecord:a"}, {"id": "delete_sharedrecord_a", "object": "sharedrecord:a", "operation": "delete", "name": "Delete sharedrecord:a", "description": "Delete sharedrecord:a"}, {"id": "list_sharedrecord_aaaa", "object": "sharedrecord:aaaa", "operation": "list", "name": "List sharedrecord:aaaa", "description": "List/search sharedrecord:aaaa objects with filters"}, {"id": "get_sharedrecord_aaaa", "object": "sharedrecord:aaaa", "operation": "get", "name": "Get sharedrecord:aaaa", "description": "Get specific sharedrecord:aaaa by reference"}, {"id": "create_sharedrecord_aaaa", "object": "sharedrecord:aaaa", "operation": "create", "name": "Create sharedrecord:aaaa", "description": "Create new sharedrecord:aaaa"}, {"id": "update_sharedrecord_aaaa", "object": "sharedrecord:aaaa", "operation": "update", "name": "Update sharedrecord:aaaa", "description": "Update existing sharedrecord:aaaa"}, {"id": "delete_sharedrecord_aaaa", "object": "sharedrecord:aaaa", "operation": "delete", "name": "Delete sharedrecord:aaaa", "description": "Delete sharedrecord:aaaa"}, {"id": "list_sharedrecord_cname", "object": "sharedrecord:cname", "operation": "list", "name": "List sharedrecord:cname", "description": "List/search sharedrecord:cname objects with filters"}, {"id": "get_sharedrecord_cname", "object": "sharedrecord:cname", "operation": "get", "name": "Get sharedrecord:cname", "description": "Get specific sharedrecord:cname by reference"}, {"id": "create_sharedrecord_cname", "object": "sharedrecord:cname", "operation": "create", "name": "Create sharedrecord:cname", "description": "Create new sharedrecord:cname"}, {"id": "update_sharedrecord_cname", "object": "sharedrecord:cname", "operation": "update", "name": "Update sharedrecord:cname", "description": "Update existing sharedrecord:cname"}, {"id": "delete_sharedrecord_cname", "object": "sharedrecord:cname", "operation": "delete", "name": "Delete sharedrecord:cname", "description": "Delete sharedrecord:cname"}, {"id": "list_sharedrecord_mx", "object": "sharedrecord:mx", "operation": "list", "name": "List sharedrecord:mx", "description": "List/search sharedrecord:mx objects with filters"}, {"id": "get_sharedrecord_mx", "object": "sharedrecord:mx", "operation": "get", "name": "Get sharedrecord:mx", "description": "Get specific sharedrecord:mx by reference"}, {"id": "create_sharedrecord_mx", "object": "sharedrecord:mx", "operation": "create", "name": "Create sharedrecord:mx", "description": "Create new sharedrecord:mx"}, {"id": "update_sharedrecord_mx", "object": "sharedrecord:mx", "operation": "update", "name": "Update sharedrecord:mx", "description": "Update existing sharedrecord:mx"}, {"id": "delete_sharedrecord_mx", "object": "sharedrecord:mx", "operation": "delete", "name": "Delete sharedrecord:mx", "description": "Delete sharedrecord:mx"}, {"id": "list_sharedrecord_srv", "object": "sharedrecord:srv", "operation": "list", "name": "List sharedrecord:srv", "description": "List/search sharedrecord:srv objects with filters"}, {"id": "get_sharedrecord_srv", "object": "sharedrecord:srv", "operation": "get", "name": "Get sharedrecord:srv", "description": "Get specific sharedrecord:srv by reference"}, {"id": "create_sharedrecord_srv", "object": "sharedrecord:srv", "operation": "create", "name": "Create sharedrecord:srv", "description": "Create new sharedrecord:srv"}, {"id": "update_sharedrecord_srv", "object": "sharedrecord:srv", "operation": "update", "name": "Update sharedrecord:srv", "description": "Update existing sharedrecord:srv"}, {"id": "delete_sharedrecord_srv", "object": "sharedrecord:srv", "operation": "delete", "name": "Delete sharedrecord:srv", "description": "Delete sharedrecord:srv"}, {"id": "list_sharedrecord_txt", "object": "sharedrecord:txt", "operation": "list", "name": "List sharedrecord:txt", "description": "List/search sharedrecord:txt objects with filters"}, {"id": "get_sharedrecord_txt", "object": "sharedrecord:txt", "operation": "get", "name": "Get sharedrecord:txt", "description": "Get specific sharedrecord:txt by reference"}, {"id": "create_sharedrecord_txt", "object": "sharedrecord:txt", "operation": "create", "name": "Create sharedrecord:txt", "description": "Create new sharedrecord:txt"}, {"id": "update_sharedrecord_txt", "object": "sharedrecord:txt", "operation": "update", "name": "Update sharedrecord:txt", "description": "Update existing sharedrecord:txt"}, {"id": "delete_sharedrecord_txt", "object": "sharedrecord:txt", "operation": "delete", "name": "Delete sharedrecord:txt", "description": "Delete sharedrecord:txt"}, {"id": "list_sharedrecordgroup", "object": "sharedrecordgroup", "operation": "list", "name": "List sharedrecordgroup", "description": "List/search sharedrecordgroup objects with filters"}, {"id": "get_sharedrecordgroup", "object": "sharedrecordgroup", "operation": "get", "name": "Get sharedrecordgroup", "description": "Get specific sharedrecordgroup by reference"}, {"id": "create_sharedrecordgroup", "object": "sharedrecordgroup", "operation": "create", "name": "Create sharedrecordgroup", "description": "Create new sharedrecordgroup"}, {"id": "update_sharedrecordgroup", "object": "sharedrecordgroup", "operation": "update", "name": "Update sharedrecordgroup", "description": "Update existing sharedrecordgroup"}, {"id": "delete_sharedrecordgroup", "object": "sharedrecordgroup", "operation": "delete", "name": "Delete sharedrecordgroup", "description": "Delete sharedrecordgroup"}, {"id": "list_smartfolder_children", "object": "smartfolder:children", "operation": "list", "name": "List smartfolder:children", "description": "List/search smartfolder:children objects with filters"}, {"id": "get_smartfolder_children", "object": "smartfolder:children", "operation": "get", "name": "Get smartfolder:children", "description": "Get specific smartfolder:children by reference"}, {"id": "create_smartfolder_children", "object": "smartfolder:children", "operation": "create", "name": "Create smartfolder:children", "description": "Create new smartfolder:children"}, {"id": "update_smartfolder_children", "object": "smartfolder:children", "operation": "update", "name": "Update smartfolder:children", "description": "Update existing smartfolder:children"}, {"id": "delete_smartfolder_children", "object": "smartfolder:children", "operation": "delete", "name": "Delete smartfolder:children", "description": "Delete smartfolder:children"}, {"id": "list_smartfolder_global", "object": "smartfolder:global", "operation": "list", "name": "List smartfolder:global", "description": "List/search smartfolder:global objects with filters"}, {"id": "get_smartfolder_global", "object": "smartfolder:global", "operation": "get", "name": "Get smartfolder:global", "description": "Get specific smartfolder:global by reference"}, {"id": "create_smartfolder_global", "object": "smartfolder:global", "operation": "create", "name": "Create smartfolder:global", "description": "Create new smartfolder:global"}, {"id": "update_smartfolder_global", "object": "smartfolder:global", "operation": "update", "name": "Update smartfolder:global", "description": "Update existing smartfolder:global"}, {"id": "delete_smartfolder_global", "object": "smartfolder:global", "operation": "delete", "name": "Delete smartfolder:global", "description": "Delete smartfolder:global"}, {"id": "list_smartfolder_personal", "object": "smartfolder:personal", "operation": "list", "name": "List smartfolder:personal", "description": "List/search smartfolder:personal objects with filters"}, {"id": "get_smartfolder_personal", "object": "smartfolder:personal", "operation": "get", "name": "Get smartfolder:personal", "description": "Get specific smartfolder:personal by reference"}, {"id": "create_smartfolder_personal", "object": "smartfolder:personal", "operation": "create", "name": "Create smartfolder:personal", "description": "Create new smartfolder:personal"}, {"id": "update_smartfolder_personal", "object": "smartfolder:personal", "operation": "update", "name": "Update smartfolder:personal", "description": "Update existing smartfolder:personal"}, {"id": "delete_smartfolder_personal", "object": "smartfolder:personal", "operation": "delete", "name": "Delete smartfolder:personal", "description": "Delete smartfolder:personal"}, {"id": "list_snmpuser", "object": "snmpuser", "operation": "list", "name": "List snmpuser", "description": "List/search snmpuser objects with filters"}, {"id": "get_snmpuser", "object": "snmpuser", "operation": "get", "name": "Get snmpuser", "description": "Get specific snmpuser by reference"}, {"id": "create_snmpuser", "object": "snmpuser", "operation": "create", "name": "Create snmpuser", "description": "Create new snmpuser"}, {"id": "update_snmpuser", "object": "snmpuser", "operation": "update", "name": "Update snmpuser", "description": "Update existing snmpuser"}, {"id": "delete_snmpuser", "object": "snmpuser", "operation": "delete", "name": "Delete snmpuser", "description": "Delete snmpuser"}, {"id": "list_superhost", "object": "superhost", "operation": "list", "name": "List superhost", "description": "List/search superhost objects with filters"}, {"id": "get_superhost", "object": "superhost", "operation": "get", "name": "Get superhost", "description": "Get specific superhost by reference"}, {"id": "create_superhost", "object": "superhost", "operation": "create", "name": "Create superhost", "description": "Create new superhost"}, {"id": "update_superhost", "object": "superhost", "operation": "update", "name": "Update superhost", "description": "Update existing superhost"}, {"id": "delete_superhost", "object": "superhost", "operation": "delete", "name": "Delete superhost", "description": "Delete superhost"}, {"id": "list_superhostchild", "object": "superhostchild", "operation": "list", "name": "List superhostchild", "description": "List/search superhostchild objects with filters"}, {"id": "get_superhostchild", "object": "superhostchild", "operation": "get", "name": "Get superhostchild", "description": "Get specific superhostchild by reference"}, {"id": "create_superhostchild", "object": "superhostchild", "operation": "create", "name": "Create superhostchild", "description": "Create new superhostchild"}, {"id": "update_superhostchild", "object": "superhostchild", "operation": "update", "name": "Update superhostchild", "description": "Update existing superhostchild"}, {"id": "delete_superhostchild", "object": "superhostchild", "operation": "delete", "name": "Delete superhostchild", "description": "Delete superhostchild"}, {"id": "list_syslog_endpoint", "object": "syslog:endpoint", "operation": "list", "name": "List syslog:endpoint", "description": "List/search syslog:endpoint objects with filters"}, {"id": "get_syslog_endpoint", "object": "syslog:endpoint", "operation": "get", "name": "Get syslog:endpoint", "description": "Get specific syslog:endpoint by reference"}, {"id": "create_syslog_endpoint", "object": "syslog:endpoint", "operation": "create", "name": "Create syslog:endpoint", "description": "Create new syslog:endpoint"}, {"id": "update_syslog_endpoint", "object": "syslog:endpoint", "operation": "update", "name": "Update syslog:endpoint", "description": "Update existing syslog:endpoint"}, {"id": "delete_syslog_endpoint", "object": "syslog:endpoint", "operation": "delete", "name": "Delete syslog:endpoint", "description": "Delete syslog:endpoint"}, {"id": "list_tacacsplus_authservice", "object": "tacacsplus:authservice", "operation": "list", "name": "List tacacsplus:authservice", "description": "List/search tacacsplus:authservice objects with filters"}, {"id": "get_tacacsplus_authservice", "object": "tacacsplus:authservice", "operation": "get", "name": "Get tacacsplus:authservice", "description": "Get specific tacacsplus:authservice by reference"}, {"id": "create_tacacsplus_authservice", "object": "tacacsplus:authservice", "operation": "create", "name": "Create tacacsplus:authservice", "description": "Create new tacacsplus:authservice"}, {"id": "update_tacacsplus_authservice", "object": "tacacsplus:authservice", "operation": "update", "name": "Update tacacsplus:authservice", "description": "Update existing tacacsplus:authservice"}, {"id": "delete_tacacsplus_authservice", "object": "tacacsplus:authservice", "operation": "delete", "name": "Delete tacacsplus:authservice", "description": "Delete tacacsplus:authservice"}, {"id": "list_taxii", "object": "taxii", "operation": "list", "name": "List taxii", "description": "List/search taxii objects with filters"}, {"id": "get_taxii", "object": "taxii", "operation": "get", "name": "Get taxii", "description": "Get specific taxii by reference"}, {"id": "create_taxii", "object": "taxii", "operation": "create", "name": "Create taxii", "description": "Create new taxii"}, {"id": "update_taxii", "object": "taxii", "operation": "update", "name": "Update taxii", "description": "Update existing taxii"}, {"id": "delete_taxii", "object": "taxii", "operation": "delete", "name": "Delete taxii", "description": "Delete taxii"}, {"id": "list_tftpfiledir", "object": "tftpfiledir", "operation": "list", "name": "List tftpfiledir", "description": "List/search tftpfiledir objects with filters"}, {"id": "get_tftpfiledir", "object": "tftpfiledir", "operation": "get", "name": "Get tftpfiledir", "description": "Get specific tftpfiledir by reference"}, {"id": "create_tftpfiledir", "object": "tftpfiledir", "operation": "create", "name": "Create tftpfiledir", "description": "Create new tftpfiledir"}, {"id": "update_tftpfiledir", "object": "tftpfiledir", "operation": "update", "name": "Update tftpfiledir", "description": "Update existing tftpfiledir"}, {"id": "delete_tftpfiledir", "object": "tftpfiledir", "operation": "delete", "name": "Delete tftpfiledir", "description": "Delete tftpfiledir"}, {"id": "list_threatanalytics_analytics_whitelist", "object": "threatanalytics:analytics_whitelist", "operation": "list", "name": "List threatanalytics:analytics_whitelist", "description": "List/search threatanalytics:analytics_whitelist objects with filters"}, {"id": "get_threatanalytics_analytics_whitelist", "object": "threatanalytics:analytics_whitelist", "operation": "get", "name": "Get threatanalytics:analytics_whitelist", "description": "Get specific threatanalytics:analytics_whitelist by reference"}, {"id": "create_threatanalytics_analytics_whitelist", "object": "threatanalytics:analytics_whitelist", "operation": "create", "name": "Create threatanalytics:analytics_whitelist", "description": "Create new threatanalytics:analytics_whitelist"}, {"id": "update_threatanalytics_analytics_whitelist", "object": "threatanalytics:analytics_whitelist", "operation": "update", "name": "Update threatanalytics:analytics_whitelist", "description": "Update existing threatanalytics:analytics_whitelist"}, {"id": "delete_threatanalytics_analytics_whitelist", "object": "threatanalytics:analytics_whitelist", "operation": "delete", "name": "Delete threatanalytics:analytics_whitelist", "description": "Delete threatanalytics:analytics_whitelist"}, {"id": "list_threatanalytics_moduleset", "object": "threatanalytics:moduleset", "operation": "list", "name": "List threatanalytics:moduleset", "description": "List/search threatanalytics:moduleset objects with filters"}, {"id": "get_threatanalytics_moduleset", "object": "threatanalytics:moduleset", "operation": "get", "name": "Get threatanalytics:moduleset", "description": "Get specific threatanalytics:moduleset by reference"}, {"id": "create_threatanalytics_moduleset", "object": "threatanalytics:moduleset", "operation": "create", "name": "Create threatanalytics:moduleset", "description": "Create new threatanalytics:moduleset"}, {"id": "update_threatanalytics_moduleset", "object": "threatanalytics:moduleset", "operation": "update", "name": "Update threatanalytics:moduleset", "description": "Update existing threatanalytics:moduleset"}, {"id": "delete_threatanalytics_moduleset", "object": "threatanalytics:moduleset", "operation": "delete", "name": "Delete threatanalytics:moduleset", "description": "Delete threatanalytics:moduleset"}, {"id": "list_threatanalytics_whitelist", "object": "threatanalytics:whitelist", "operation": "list", "name": "List threatanalytics:whitelist", "description": "List/search threatanalytics:whitelist objects with filters"}, {"id": "get_threatanalytics_whitelist", "object": "threatanalytics:whitelist", "operation": "get", "name": "Get threatanalytics:whitelist", "description": "Get specific threatanalytics:whitelist by reference"}, {"id": "create_threatanalytics_whitelist", "object": "threatanalytics:whitelist", "operation": "create", "name": "Create threatanalytics:whitelist", "description": "Create new threatanalytics:whitelist"}, {"id": "update_threatanalytics_whitelist", "object": "threatanalytics:whitelist", "operation": "update", "name": "Update threatanalytics:whitelist", "description": "Update existing threatanalytics:whitelist"}, {"id": "delete_threatanalytics_whitelist", "object": "threatanalytics:whitelist", "operation": "delete", "name": "Delete threatanalytics:whitelist", "description": "Delete threatanalytics:whitelist"}, {"id": "list_threatinsight_cloudclient", "object": "threatinsight:cloudclient", "operation": "list", "name": "List threatinsight:cloudclient", "description": "List/search threatinsight:cloudclient objects with filters"}, {"id": "get_threatinsight_cloudclient", "object": "threatinsight:cloudclient", "operation": "get", "name": "Get threatinsight:cloudclient", "description": "Get specific threatinsight:cloudclient by reference"}, {"id": "create_threatinsight_cloudclient", "object": "threatinsight:cloudclient", "operation": "create", "name": "Create threatinsight:cloudclient", "description": "Create new threatinsight:cloudclient"}, {"id": "update_threatinsight_cloudclient", "object": "threatinsight:cloudclient", "operation": "update", "name": "Update threatinsight:cloudclient", "description": "Update existing threatinsight:cloudclient"}, {"id": "delete_threatinsight_cloudclient", "object": "threatinsight:cloudclient", "operation": "delete", "name": "Delete threatinsight:cloudclient", "description": "Delete threatinsight:cloudclient"}, {"id": "list_threatprotection_grid_rule", "object": "threatprotection:grid:rule", "operation": "list", "name": "List threatprotection:grid:rule", "description": "List/search threatprotection:grid:rule objects with filters"}, {"id": "get_threatprotection_grid_rule", "object": "threatprotection:grid:rule", "operation": "get", "name": "Get threatprotection:grid:rule", "description": "Get specific threatprotection:grid:rule by reference"}, {"id": "create_threatprotection_grid_rule", "object": "threatprotection:grid:rule", "operation": "create", "name": "Create threatprotection:grid:rule", "description": "Create new threatprotection:grid:rule"}, {"id": "update_threatprotection_grid_rule", "object": "threatprotection:grid:rule", "operation": "update", "name": "Update threatprotection:grid:rule", "description": "Update existing threatprotection:grid:rule"}, {"id": "delete_threatprotection_grid_rule", "object": "threatprotection:grid:rule", "operation": "delete", "name": "Delete threatprotection:grid:rule", "description": "Delete threatprotection:grid:rule"}, {"id": "list_threatprotection_profile", "object": "threatprotection:profile", "operation": "list", "name": "List threatprotection:profile", "description": "List/search threatprotection:profile objects with filters"}, {"id": "get_threatprotection_profile", "object": "threatprotection:profile", "operation": "get", "name": "Get threatprotection:profile", "description": "Get specific threatprotection:profile by reference"}, {"id": "create_threatprotection_profile", "object": "threatprotection:profile", "operation": "create", "name": "Create threatprotection:profile", "description": "Create new threatprotection:profile"}, {"id": "update_threatprotection_profile", "object": "threatprotection:profile", "operation": "update", "name": "Update threatprotection:profile", "description": "Update existing threatprotection:profile"}, {"id": "delete_threatprotection_profile", "object": "threatprotection:profile", "operation": "delete", "name": "Delete threatprotection:profile", "description": "Delete threatprotection:profile"}, {"id": "list_threatprotection_profile_rule", "object": "threatprotection:profile:rule", "operation": "list", "name": "List threatprotection:profile:rule", "description": "List/search threatprotection:profile:rule objects with filters"}, {"id": "get_threatprotection_profile_rule", "object": "threatprotection:profile:rule", "operation": "get", "name": "Get threatprotection:profile:rule", "description": "Get specific threatprotection:profile:rule by reference"}, {"id": "create_threatprotection_profile_rule", "object": "threatprotection:profile:rule", "operation": "create", "name": "Create threatprotection:profile:rule", "description": "Create new threatprotection:profile:rule"}, {"id": "update_threatprotection_profile_rule", "object": "threatprotection:profile:rule", "operation": "update", "name": "Update threatprotection:profile:rule", "description": "Update existing threatprotection:profile:rule"}, {"id": "delete_threatprotection_profile_rule", "object": "threatprotection:profile:rule", "operation": "delete", "name": "Delete threatprotection:profile:rule", "description": "Delete threatprotection:profile:rule"}, {"id": "list_threatprotection_rule", "object": "threatprotection:rule", "operation": "list", "name": "List threatprotection:rule", "description": "List/search threatprotection:rule objects with filters"}, {"id": "get_threatprotection_rule", "object": "threatprotection:rule", "operation": "get", "name": "Get threatprotection:rule", "description": "Get specific threatprotection:rule by reference"}, {"id": "create_threatprotection_rule", "object": "threatprotection:rule", "operation": "create", "name": "Create threatprotection:rule", "description": "Create new threatprotection:rule"}, {"id": "update_threatprotection_rule", "object": "threatprotection:rule", "operation": "update", "name": "Update threatprotection:rule", "description": "Update existing threatprotection:rule"}, {"id": "delete_threatprotection_rule", "object": "threatprotection:rule", "operation": "delete", "name": "Delete threatprotection:rule", "description": "Delete threatprotection:rule"}, {"id": "list_threatprotection_rulecategory", "object": "threatprotection:rulecategory", "operation": "list", "name": "List threatprotection:rulecategory", "description": "List/search threatprotection:rulecategory objects with filters"}, {"id": "get_threatprotection_rulecategory", "object": "threatprotection:rulecategory", "operation": "get", "name": "Get threatprotection:rulecategory", "description": "Get specific threatprotection:rulecategory by reference"}, {"id": "create_threatprotection_rulecategory", "object": "threatprotection:rulecategory", "operation": "create", "name": "Create threatprotection:rulecategory", "description": "Create new threatprotection:rulecategory"}, {"id": "update_threatprotection_rulecategory", "object": "threatprotection:rulecategory", "operation": "update", "name": "Update threatprotection:rulecategory", "description": "Update existing threatprotection:rulecategory"}, {"id": "delete_threatprotection_rulecategory", "object": "threatprotection:rulecategory", "operation": "delete", "name": "Delete threatprotection:rulecategory", "description": "Delete threatprotection:rulecategory"}, {"id": "list_threatprotection_ruleset", "object": "threatprotection:ruleset", "operation": "list", "name": "List threatprotection:ruleset", "description": "List/search threatprotection:ruleset objects with filters"}, {"id": "get_threatprotection_ruleset", "object": "threatprotection:ruleset", "operation": "get", "name": "Get threatprotection:ruleset", "description": "Get specific threatprotection:ruleset by reference"}, {"id": "create_threatprotection_ruleset", "object": "threatprotection:ruleset", "operation": "create", "name": "Create threatprotection:ruleset", "description": "Create new threatprotection:ruleset"}, {"id": "update_threatprotection_ruleset", "object": "threatprotection:ruleset", "operation": "update", "name": "Update threatprotection:ruleset", "description": "Update existing threatprotection:ruleset"}, {"id": "delete_threatprotection_ruleset", "object": "threatprotection:ruleset", "operation": "delete", "name": "Delete threatprotection:ruleset", "description": "Delete threatprotection:ruleset"}, {"id": "list_threatprotection_ruletemplate", "object": "threatprotection:ruletemplate", "operation": "list", "name": "List threatprotection:ruletemplate", "description": "List/search threatprotection:ruletemplate objects with filters"}, {"id": "get_threatprotection_ruletemplate", "object": "threatprotection:ruletemplate", "operation": "get", "name": "Get threatprotection:ruletemplate", "description": "Get specific threatprotection:ruletemplate by reference"}, {"id": "create_threatprotection_ruletemplate", "object": "threatprotection:ruletemplate", "operation": "create", "name": "Create threatprotection:ruletemplate", "description": "Create new threatprotection:ruletemplate"}, {"id": "update_threatprotection_ruletemplate", "object": "threatprotection:ruletemplate", "operation": "update", "name": "Update threatprotection:ruletemplate", "description": "Update existing threatprotection:ruletemplate"}, {"id": "delete_threatprotection_ruletemplate", "object": "threatprotection:ruletemplate", "operation": "delete", "name": "Delete threatprotection:ruletemplate", "description": "Delete threatprotection:ruletemplate"}, {"id": "list_threatprotection_statistics", "object": "threatprotection:statistics", "operation": "list", "name": "List threatprotection:statistics", "description": "List/search threatprotection:statistics objects with filters"}, {"id": "get_threatprotection_statistics", "object": "threatprotection:statistics", "operation": "get", "name": "Get threatprotection:statistics", "description": "Get specific threatprotection:statistics by reference"}, {"id": "create_threatprotection_statistics", "object": "threatprotection:statistics", "operation": "create", "name": "Create threatprotection:statistics", "description": "Create new threatprotection:statistics"}, {"id": "update_threatprotection_statistics", "object": "threatprotection:statistics", "operation": "update", "name": "Update threatprotection:statistics", "description": "Update existing threatprotection:statistics"}, {"id": "delete_threatprotection_statistics", "object": "threatprotection:statistics", "operation": "delete", "name": "Delete threatprotection:statistics", "description": "Delete threatprotection:statistics"}, {"id": "list_upgradegroup", "object": "upgradegroup", "operation": "list", "name": "List upgradegroup", "description": "List/search upgradegroup objects with filters"}, {"id": "get_upgradegroup", "object": "upgradegroup", "operation": "get", "name": "Get upgradegroup", "description": "Get specific upgradegroup by reference"}, {"id": "create_upgradegroup", "object": "upgradegroup", "operation": "create", "name": "Create upgradegroup", "description": "Create new upgradegroup"}, {"id": "update_upgradegroup", "object": "upgradegroup", "operation": "update", "name": "Update upgradegroup", "description": "Update existing upgradegroup"}, {"id": "delete_upgradegroup", "object": "upgradegroup", "operation": "delete", "name": "Delete upgradegroup", "description": "Delete upgradegroup"}, {"id": "list_upgradeschedule", "object": "upgradeschedule", "operation": "list", "name": "List upgradeschedule", "description": "List/search upgradeschedule objects with filters"}, {"id": "get_upgradeschedule", "object": "upgradeschedule", "operation": "get", "name": "Get upgradeschedule", "description": "Get specific upgradeschedule by reference"}, {"id": "create_upgradeschedule", "object": "upgradeschedule", "operation": "create", "name": "Create upgradeschedule", "description": "Create new upgradeschedule"}, {"id": "update_upgradeschedule", "object": "upgradeschedule", "operation": "update", "name": "Update upgradeschedule", "description": "Update existing upgradeschedule"}, {"id": "delete_upgradeschedule", "object": "upgradeschedule", "operation": "delete", "name": "Delete upgradeschedule", "description": "Delete upgradeschedule"}, {"id": "list_upgradestatus", "object": "upgradestatus", "operation": "list", "name": "List upgradestatus", "description": "List/search upgradestatus objects with filters"}, {"id": "get_upgradestatus", "object": "upgradestatus", "operation": "get", "name": "Get upgradestatus", "description": "Get specific upgradestatus by reference"}, {"id": "create_upgradestatus", "object": "upgradestatus", "operation": "create", "name": "Create upgradestatus", "description": "Create new upgradestatus"}, {"id": "update_upgradestatus", "object": "upgradestatus", "operation": "update", "name": "Update upgradestatus", "description": "Update existing upgradestatus"}, {"id": "delete_upgradestatus", "object": "upgradestatus", "operation": "delete", "name": "Delete upgradestatus", "description": "Delete upgradestatus"}, {"id": "list_userprofile", "object": "userprofile", "operation": "list", "name": "List userprofile", "description": "List/search userprofile objects with filters"}, {"id": "get_userprofile", "object": "userprofile", "operation": "get", "name": "Get userprofile", "description": "Get specific userprofile by reference"}, {"id": "create_userprofile", "object": "userprofile", "operation": "create", "name": "Create userprofile", "description": "Create new userprofile"}, {"id": "update_userprofile", "object": "userprofile", "operation": "update", "name": "Update userprofile", "description": "Update existing userprofile"}, {"id": "delete_userprofile", "object": "userprofile", "operation": "delete", "name": "Delete userprofile", "description": "Delete userprofile"}, {"id": "list_vdiscoverytask", "object": "vdiscoverytask", "operation": "list", "name": "List vdiscoverytask", "description": "List/search vdiscoverytask objects with filters"}, {"id": "get_vdiscoverytask", "object": "vdiscoverytask", "operation": "get", "name": "Get vdiscoverytask", "description": "Get specific vdiscoverytask by reference"}, {"id": "create_vdiscoverytask", "object": "vdiscoverytask", "operation": "create", "name": "Create vdiscoverytask", "description": "Create new vdiscoverytask"}, {"id": "update_vdiscoverytask", "object": "vdiscoverytask", "operation": "update", "name": "Update vdiscoverytask", "description": "Update existing vdiscoverytask"}, {"id": "delete_vdiscoverytask", "object": "vdiscoverytask", "operation": "delete", "name": "Delete vdiscoverytask", "description": "Delete vdiscoverytask"}, {"id": "list_view", "object": "view", "operation": "list", "name": "List view", "description": "List/search view objects with filters"}, {"id": "get_view", "object": "view", "operation": "get", "name": "Get view", "description": "Get specific view by reference"}, {"id": "create_view", "object": "view", "operation": "create", "name": "Create view", "description": "Create new view"}, {"id": "update_view", "object": "view", "operation": "update", "name": "Update view", "description": "Update existing view"}, {"id": "delete_view", "object": "view", "operation": "delete", "name": "Delete view", "description": "Delete view"}, {"id": "list_vlan", "object": "vlan", "operation": "list", "name": "List vlan", "description": "List/search vlan objects with filters"}, {"id": "get_vlan", "object": "vlan", "operation": "get", "name": "Get vlan", "description": "Get specific vlan by reference"}, {"id": "create_vlan", "object": "vlan", "operation": "create", "name": "Create vlan", "description": "Create new vlan"}, {"id": "update_vlan", "object": "vlan", "operation": "update", "name": "Update vlan", "description": "Update existing vlan"}, {"id": "delete_vlan", "object": "vlan", "operation": "delete", "name": "Delete vlan", "description": "Delete vlan"}, {"id": "list_vlanrange", "object": "vlanrange", "operation": "list", "name": "List vlanrange", "description": "List/search vlanrange objects with filters"}, {"id": "get_vlanrange", "object": "vlanrange", "operation": "get", "name": "Get vlanrange", "description": "Get specific vlanrange by reference"}, {"id": "create_vlanrange", "object": "vlanrange", "operation": "create", "name": "Create vlanrange", "description": "Create new vlanrange"}, {"id": "update_vlanrange", "object": "vlanrange", "operation": "update", "name": "Update vlanrange", "description": "Update existing vlanrange"}, {"id": "delete_vlanrange", "object": "vlanrange", "operation": "delete", "name": "Delete vlanrange", "description": "Delete vlanrange"}, {"id": "list_vlanview", "object": "vlanview", "operation": "list", "name": "List vlanview", "description": "List/search vlanview objects with filters"}, {"id": "get_vlanview", "object": "vlanview", "operation": "get", "name": "Get vlanview", "description": "Get specific vlanview by reference"}, {"id": "create_vlanview", "object": "vlanview", "operation": "create", "name": "Create vlanview", "description": "Create new vlanview"}, {"id": "update_vlanview", "object": "vlanview", "operation": "update", "name": "Update vlanview", "description": "Update existing vlanview"}, {"id": "delete_vlanview", "object": "vlanview", "operation": "delete", "name": "Delete vlanview", "description": "Delete vlanview"}, {"id": "list_zone_auth", "object": "zone_auth", "operation": "list", "name": "List zone_auth", "description": "List/search zone_auth objects with filters"}, {"id": "get_zone_auth", "object": "zone_auth", "operation": "get", "name": "Get zone_auth", "description": "Get specific zone_auth by reference"}, {"id": "create_zone_auth", "object": "zone_auth", "operation": "create", "name": "Create zone_auth", "description": "Create new zone_auth"}, {"id": "update_zone_auth", "object": "zone_auth", "operation": "update", "name": "Update zone_auth", "description": "Update existing zone_auth"}, {"id": "delete_zone_auth", "object": "zone_auth", "operation": "delete", "name": "Delete zone_auth", "description": "Delete zone_auth"}, {"id": "list_zone_auth_discrepancy", "object": "zone_auth_discrepancy", "operation": "list", "name": "List zone_auth_discrepancy", "description": "List/search zone_auth_discrepancy objects with filters"}, {"id": "get_zone_auth_discrepancy", "object": "zone_auth_discrepancy", "operation": "get", "name": "Get zone_auth_discrepancy", "description": "Get specific zone_auth_discrepancy by reference"}, {"id": "create_zone_auth_discrepancy", "object": "zone_auth_discrepancy", "operation": "create", "name": "Create zone_auth_discrepancy", "description": "Create new zone_auth_discrepancy"}, {"id": "update_zone_auth_discrepancy", "object": "zone_auth_discrepancy", "operation": "update", "name": "Update zone_auth_discrepancy", "description": "Update existing zone_auth_discrepancy"}, {"id": "delete_zone_auth_discrepancy", "object": "zone_auth_discrepancy", "operation": "delete", "name": "Delete zone_auth_discrepancy", "description": "Delete zone_auth_discrepancy"}, {"id": "list_zone_delegated", "object": "zone_delegated", "operation": "list", "name": "List zone_delegated", "description": "List/search zone_delegated objects with filters"}, {"id": "get_zone_delegated", "object": "zone_delegated", "operation": "get", "name": "Get zone_delegated", "description": "Get specific zone_delegated by reference"}, {"id": "create_zone_delegated", "object": "zone_delegated", "operation": "create", "name": "Create zone_delegated", "description": "Create new zone_delegated"}, {"id": "update_zone_delegated", "object": "zone_delegated", "operation": "update", "name": "Update zone_delegated", "description": "Update existing zone_delegated"}, {"id": "delete_zone_delegated", "object": "zone_delegated", "operation": "delete", "name": "Delete zone_delegated", "description": "Delete zone_delegated"}, {"id": "list_zone_forward", "object": "zone_forward", "operation": "list", "name": "List zone_forward", "description": "List/search zone_forward objects with filters"}, {"id": "get_zone_forward", "object": "zone_forward", "operation": "get", "name": "Get zone_forward", "description": "Get specific zone_forward by reference"}, {"id": "create_zone_forward", "object": "zone_forward", "operation": "create", "name": "Create zone_forward", "description": "Create new zone_forward"}, {"id": "update_zone_forward", "object": "zone_forward", "operation": "update", "name": "Update zone_forward", "description": "Update existing zone_forward"}, {"id": "delete_zone_forward", "object": "zone_forward", "operation": "delete", "name": "Delete zone_forward", "description": "Delete zone_forward"}, {"id": "list_zone_rp", "object": "zone_rp", "operation": "list", "name": "List zone_rp", "description": "List/search zone_rp objects with filters"}, {"id": "get_zone_rp", "object": "zone_rp", "operation": "get", "name": "Get zone_rp", "description": "Get specific zone_rp by reference"}, {"id": "create_zone_rp", "object": "zone_rp", "operation": "create", "name": "Create zone_rp", "description": "Create new zone_rp"}, {"id": "update_zone_rp", "object": "zone_rp", "operation": "update", "name": "Update zone_rp", "description": "Update existing zone_rp"}, {"id": "delete_zone_rp", "object": "zone_rp", "operation": "delete", "name": "Delete zone_rp", "description": "Delete zone_rp"}, {"id": "list_zone_stub", "object": "zone_stub", "operation": "list", "name": "List zone_stub", "description": "List/search zone_stub objects with filters"}, {"id": "get_zone_stub", "object": "zone_stub", "operation": "get", "name": "Get zone_stub", "description": "Get specific zone_stub by reference"}, {"id": "create_zone_stub", "object": "zone_stub", "operation": "create", "name": "Create zone_stub", "description": "Create new zone_stub"}, {"id": "update_zone_stub", "object": "zone_stub", "operation": "update", "name": "Update zone_stub", "description": "Update existing zone_stub"}, {"id": "delete_zone_stub", "object": "zone_stub", "operation": "delete", "name": "Delete zone_stub", "description": "Delete zone_stub"}]