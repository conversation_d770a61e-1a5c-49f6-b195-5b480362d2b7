#!/bin/bash
echo "====================================="
echo "  INFOBLOX MODEL SETUP FOR OPEN WEBUI"
echo "====================================="
echo ""
echo "The InfoBlox MCP server is running correctly!"
echo "Connection test: ✅ PASSED"
echo ""
echo "To see the InfoBlox model in Open WebUI:"
echo ""
echo "1. Open your browser to: http://localhost:3000"
echo ""
echo "2. After logging in, click the Settings icon (⚙️) in the sidebar"
echo ""
echo "3. Click 'Connections' in the left menu"
echo ""
echo "4. Click the '+ Add' or '+ New Connection' button"
echo ""
echo "5. Fill in these EXACT values:"
echo "   ┌─────────────────────────────────────────┐"
echo "   │ API Type: OpenAI                        │"
echo "   │ Name: InfoBlox Assistant                │"
echo "   │ API Base URL: http://host.docker.internal:8000/v1  │"
echo "   │ API Key: dummy-key-12345                │"
echo "   └─────────────────────────────────────────┘"
echo ""
echo "6. Click 'Save'"
echo ""
echo "7. IMPORTANT: Refresh your browser (Cmd+R or F5)"
echo ""
echo "8. Go back to the chat and click the model dropdown"
echo ""
echo "9. You will now see 'infoblox-assistant' in the list!"
echo ""
echo "====================================="
echo "Current Status:"
echo "- InfoBlox MCP: ✅ Running on port 8000"
echo "- Open WebUI: ✅ Running on port 3000"
echo "- Model endpoint: ✅ Working"
echo "- Connection from WebUI: ✅ Verified"
echo "====================================="
