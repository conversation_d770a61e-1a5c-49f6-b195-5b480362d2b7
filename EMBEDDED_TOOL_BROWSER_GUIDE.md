# Using InfoBlox Tool Browser in Open WebUI

## 🎉 The Tool Browser is Now Embedded in Chat!

You no longer need to access a different port. Everything is available directly in your Open WebUI chat interface.

## How to Use

### 1. Connect Open WebUI to InfoBlox MCP

In Open WebUI Settings → Connections:
- Add a new "OpenAI API" connection
- API Base URL: `http://localhost:8000/v1` (or `http://host.docker.internal:8000/v1` if Open WebUI is in Docker)
- API Key: `dummy` (any value works)
- Save and refresh

### 2. Select the InfoBlox Assistant

In the model dropdown, select "InfoBlox Assistant"

### 3. Browse Tools

Type: `browse tools`

This shows:
- All tool categories with counts
- Instructions on how to search and execute tools
- Popular tools with examples

### 4. Search for Specific Tools

Type: `search tools: <term>`

Examples:
- `search tools: network` - Find network-related tools
- `search tools: create` - Find creation tools
- `search tools: dns` - Find DNS tools
- `search tools: dhcp` - Find DHCP tools

### 5. Execute Tools

Once you find a tool, copy the example and modify parameters:

```
execute tool: {"tool_id": "list_network", "parameters": {"_max_results": 10}}
```

## Examples

### List Networks
```
execute tool: {"tool_id": "list_network", "parameters": {}}
```

### Create a Network
```
execute tool: {"tool_id": "create_network", "parameters": {"network": "*********/24", "comment": "Test network"}}
```

### List DNS A Records
```
execute tool: {"tool_id": "list_record_a", "parameters": {"zone": "example.com"}}
```

### Search for an IP
```
execute tool: {"tool_id": "list_ipv4address", "parameters": {"ip_address": "**********"}}
```

## Tips

1. **Start with browse tools** to see categories
2. **Use search tools** to find specific operations
3. **Copy the examples** from search results
4. **Modify parameters** as needed
5. **Add json to any command** for JSON output

## Common Tool Categories

- **Network** (50 tools) - Network management
- **DNS Records** (250 tools) - A, AAAA, CNAME, PTR, etc.
- **DNS Zones** (35 tools) - Zone management
- **DHCP** (100 tools) - Ranges, leases, reservations
- **IPAM** (15 tools) - IP address management
- **Security** (40 tools) - Admin, certificates
- **Discovery** (80 tools) - Network discovery
- **Grid** (175 tools) - Grid and member management

## Workflow Example

1. **Find available networks:**
   ```
   User: browse tools
   Bot: [Shows categories and examples]
   
   User: search tools: list network
   Bot: [Shows network listing tools with examples]
   
   User: execute tool: {"tool_id": "list_network", "parameters": {}}
   Bot: [Shows all networks]
   ```

2. **Create a new network:**
   ```
   User: search tools: create network
   Bot: [Shows network creation tool with parameters]
   
   User: execute tool: {"tool_id": "create_network", "parameters": {"network": "**********/24", "comment": "Development network"}}
   Bot: [Creates network and shows result]
   ```

## Troubleshooting

- **Model not showing:** Check Open WebUI connection settings
- **Connection failed:** Ensure InfoBlox MCP is running (`docker ps`)
- **Tool not found:** Use broader search terms
- **Execution error:** Check required parameters in the tool description
