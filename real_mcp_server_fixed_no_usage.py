import requests
import json
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib3
import os
import re

# Disable SSL warnings for self-signed certificates
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class RealInfoBloxMCP(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.grid_master = os.getenv("GRID_MASTER_URL", "https://192.168.1.1/wapi/v2.13.1")
        self.username = os.getenv("INFOBLOX_USERNAME", "admin")
        self.password = os.getenv("INFOBLOX_PASSWORD", "infoblox")
        self.network_view = os.getenv("NETWORK_VIEW", "default")
        
        # Create session for InfoBlox
        self.session = requests.Session()
        self.session.auth = (self.username, self.password)
        self.session.verify = False  # For self-signed certificates
        self.session.headers.update({'Content-Type': 'application/json'})
        
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        if self.path == "/health":
            self.send_json({"status": "healthy", "grid_master": self.grid_master})
        elif self.path == "/v1/models":
            self.send_json({
                "data": [{
                    "id": "infoblox-assistant",
                    "object": "model",
                    "owned_by": "infoblox-real"
                }]
            })
        else:
            self.send_json({"status": "ok"})
    
    def do_POST(self):
        content_length = int(self.headers.get('Content-Length', 0))
        body = self.rfile.read(content_length).decode('utf-8')
        
        if self.path == "/v1/chat/completions":
            try:
                data = json.loads(body)
                messages = data.get("messages", [])
                if messages:
                    user_message = messages[-1].get("content", "").lower()
                    response_content = self.process_real_infoblox_query(user_message)
                else:
                    response_content = self.get_sample_commands()
                
                response = {
                    "id": "chatcmpl-" + str(int(datetime.now().timestamp())),
                    "object": "chat.completion",
                    "created": int(datetime.now().timestamp()),
                    "model": "infoblox-assistant",
                    "choices": [{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_content
                        },
                        "finish_reason": "stop"
                    }]
                }
                self.send_json(response)
            except Exception as e:
                self.send_json({"error": str(e)})
    
    def process_real_infoblox_query(self, query):
        """Process queries against real InfoBlox"""
        
        # Test connection
        if any(word in query for word in ["test", "connection", "verify", "check connection"]):
            return self.test_connection()
        
        # LIST ALL NETWORKS - Fixed to catch more variations
        elif any(phrase in query for phrase in [
            "list all network", "list network", "show all network", 
            "all network", "list my network", "show network",
            "get all network", "what network", "which network",
            "display network", "view network"
        ]):
            return self.list_all_networks()
        
        # Show first N networks
        elif re.search(r'first \d+ network|show me the first|first few network', query):
            match = re.search(r'first (\d+)', query)
            limit = int(match.group(1)) if match else 5
            return self.list_networks_limited(limit)
        
        # Get specific network usage
        elif "usage" in query and any(char in query for char in ['/', '.']) :
            return self.get_network_usage(query)
        
        # List DNS zones
        elif any(word in query for word in ["dns", "zone", "domains"]):
            return self.list_dns_zones()
        
        # Find available IP
        elif "available ip" in query or "next ip" in query:
            return self.find_available_ip(query)
        
        # Help or unknown command
        else:
            return self.get_sample_commands() + f"\n\nYour query: '{query[:50]}...'"
    
    def list_all_networks(self):
        """List all networks from InfoBlox"""
        try:
            print(f"Fetching all networks from {self.grid_master}")
            
            # Get networks WITHOUT usage field (which causes the error)
            params = {
                "_max_results": 25,
                "_return_fields": "network,comment,network_view,members",  # Removed 'usage'
                "network_view": self.network_view
            }
            
            response = self.session.get(f"{self.grid_master}/network", params=params)
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                networks = response.json()
                
                if not networks:
                    return f"No networks found in view '{self.network_view}'\n\nTry checking:\n• Network view name\n• Permissions"
                
                result = f"📊 **All Networks in InfoBlox** (View: {self.network_view}):\n\n"
                
                for i, net in enumerate(networks, 1):
                    network = net.get('network', 'Unknown')
                    comment = net.get('comment', 'No description')
                    
                    result += f"**{i}. {network}**\n"
                    result += f"   • Description: {comment}\n"
                    result += f"   • View: {self.network_view}\n"
                    result += "\n"
                
                # Get total count
                total_response = self.session.get(
                    f"{self.grid_master}/network",
                    params={"_return_fields": "network", "network_view": self.network_view, "_max_results": 1000}
                )
                if total_response.status_code == 200:
                    total_count = len(total_response.json())
                    if total_count > 25:
                        result += f"📈 Showing 25 of {total_count} total networks\n"
                        result += f"(Use 'show me the first 50 networks' to see more)"
                    else:
                        result += f"📈 Total networks: {total_count}"
                
                return result
            else:
                return f"❌ Error fetching networks: HTTP {response.status_code}\n\nDetails: {response.text[:200]}"
                
        except Exception as e:
            return f"❌ Error connecting to InfoBlox:\n{str(e)}\n\nPlease check your connection settings."
    
    def list_networks_limited(self, limit=5):
        """List limited number of networks"""
        try:
            params = {
                "_max_results": limit,
                "_return_fields": "network,comment,network_view,members",  # Removed 'usage'
                "network_view": self.network_view
            }
            
            response = self.session.get(f"{self.grid_master}/network", params=params)
            
            if response.status_code == 200:
                networks = response.json()
                
                if not networks:
                    return f"No networks found in view '{self.network_view}'"
                
                result = f"📊 **First {len(networks)} Networks** (View: {self.network_view}):\n\n"
                
                for i, net in enumerate(networks, 1):
                    network = net.get('network', 'Unknown')
                    comment = net.get('comment', 'No description')
                    
                    result += f"**{i}. {network}**\n"
                    result += f"   • Description: {comment}\n"
                    result += f"   • View: {self.network_view}\n"
                    result += "\n"
                
                return result
            else:
                return f"Error fetching networks: HTTP {response.status_code}"
                
        except Exception as e:
            return f"Error: {str(e)}"
    
    def test_connection(self):
        """Test InfoBlox connection"""
        try:
            response = self.session.get(
                f"{self.grid_master}/grid",
                params={"_return_fields": "name,ntp_setting,dns_resolver_setting"}
            )
            
            if response.status_code == 200:
                grid_info = response.json()
                if isinstance(grid_info, list) and grid_info:
                    grid_data = grid_info[0]
                    return f"""✅ **Successfully connected to InfoBlox!**

**Grid Information:**
• Grid Name: {grid_data.get('name', 'Unknown')}
• Grid Master: {self.grid_master}
• API Version: {self.grid_master.split('/')[-1]}
• Network View: {self.network_view}
• Authentication: Successful

Connection test passed!"""
                else:
                    return "✅ Connected to InfoBlox"
            else:
                return f"❌ Connection failed: HTTP {response.status_code}"
        except Exception as e:
            return f"❌ Connection error: {str(e)}"
    
    def get_network_usage(self, query):
        """Get network info for specific network (without usage stats)"""
        network_match = re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', query)
        if network_match:
            network_cidr = network_match.group(1)
            try:
                params = {
                    "network": network_cidr,
                    "network_view": self.network_view,
                    "_return_fields": "network,comment,members,options"  # Removed 'usage'
                }
                
                response = self.session.get(f"{self.grid_master}/network", params=params)
                
                if response.status_code == 200:
                    networks = response.json()
                    if networks:
                        net = networks[0]
                        network = net.get('network', 'Unknown')
                        comment = net.get('comment', 'No description')
                        
                        result = f"📊 **Network Details for {network}**\n\n"
                        result += f"• Description: {comment}\n"
                        result += f"• Network View: {self.network_view}\n"
                        result += f"• Reference: {net.get('_ref', 'N/A')}\n"
                        
                        # Note about usage
                        result += f"\n📝 Note: Usage statistics require additional API calls.\n"
                        result += f"To get usage, use: 'Get IP addresses in {network}'"
                        
                        return result
                    else:
                        return f"Network {network_cidr} not found in view '{self.network_view}'"
                else:
                    return f"Error fetching network: HTTP {response.status_code}"
                    
            except Exception as e:
                return f"Error: {str(e)}"
        else:
            return "Please specify a network (e.g., 'What's the usage of 10.0.0.0/16')"
    
    def list_dns_zones(self):
        """List DNS zones"""
        try:
            # Get forward zones
            fwd_response = self.session.get(
                f"{self.grid_master}/zone_auth",
                params={"_return_fields": "fqdn,comment,view", "_max_results": 20}
            )
            
            result = "🌐 **DNS Zones in InfoBlox**\n\n"
            
            if fwd_response.status_code == 200:
                all_zones = fwd_response.json()
                fwd_zones = [z for z in all_zones if not z['fqdn'].endswith('.in-addr.arpa')]
                rev_zones = [z for z in all_zones if z['fqdn'].endswith('.in-addr.arpa')]
                
                if fwd_zones:
                    result += "**Forward Zones:**\n"
                    for zone in fwd_zones[:10]:
                        fqdn = zone.get('fqdn', 'Unknown')
                        comment = zone.get('comment', '')
                        view = zone.get('view', 'default')
                        result += f"• {fqdn}"
                        if comment:
                            result += f" - {comment}"
                        result += f" (view: {view})\n"
                    
                    if len(fwd_zones) > 10:
                        result += f"... and {len(fwd_zones) - 10} more forward zones\n"
                
                if rev_zones:
                    result += "\n**Reverse Zones:**\n"
                    for zone in rev_zones[:5]:
                        fqdn = zone.get('fqdn', 'Unknown')
                        result += f"• {fqdn}\n"
                    
                    if len(rev_zones) > 5:
                        result += f"... and {len(rev_zones) - 5} more reverse zones\n"
                
                result += f"\n📊 Total: {len(fwd_zones)} forward, {len(rev_zones)} reverse zones"
            else:
                result += f"Error fetching zones: HTTP {fwd_response.status_code}"
            
            return result
            
        except Exception as e:
            return f"Error querying DNS zones: {str(e)}"
    
    def find_available_ip(self, query):
        """Find available IPs in a network"""
        network_match = re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', query)
        if network_match:
            network_cidr = network_match.group(1)
            try:
                # Get network reference
                net_response = self.session.get(
                    f"{self.grid_master}/network",
                    params={"network": network_cidr, "network_view": self.network_view}
                )
                
                if net_response.status_code == 200 and net_response.json():
                    net_ref = net_response.json()[0]['_ref']
                    
                    # Get next available IPs
                    ip_response = self.session.post(
                        f"{self.grid_master}/{net_ref}",
                        params={"_function": "next_available_ip", "num": 5}
                    )
                    
                    if ip_response.status_code == 200:
                        ips = ip_response.json()
                        result = f"✅ **Next 5 Available IPs in {network_cidr}:**\n\n"
                        for i, ip in enumerate(ips['ips'][:5], 1):
                            result += f"{i}. {ip}\n"
                        return result
                
                return f"Network {network_cidr} not found"
                
            except Exception as e:
                return f"Error finding available IPs: {str(e)}"
        else:
            return "Please specify a network (e.g., 'Find available IP in 10.0.0.0/24')"
    
    def get_sample_commands(self):
        """Return sample commands for the user"""
        return """🎯 **Connected to Real InfoBlox!**

Here are commands you can try:

1️⃣ **List all networks**
   → Shows all networks in your InfoBlox

2️⃣ **Test my connection**
   → Verifies connectivity to your Grid Master

3️⃣ **Show network details for 10.0.0.0/16**
   → Shows info for a specific network

4️⃣ **List my DNS zones**
   → Shows all forward and reverse DNS zones

Just type any of these commands to see your real InfoBlox data!

Current configuration:
• Grid Master: """ + self.grid_master + """
• Network View: """ + self.network_view + """
• Status: Connected ✅"""
    
    def send_json(self, data):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
    
    def log_message(self, format, *args):
        print(f"{datetime.now().strftime('%H:%M:%S')} - {format % args}")

if __name__ == "__main__":
    print(f"Starting Real InfoBlox MCP Server (No Usage Field) on port 8000...")
    print(f"Grid Master: {os.getenv('GRID_MASTER_URL', 'Not set')}")
    print(f"Network View: {os.getenv('NETWORK_VIEW', 'default')}")
    server = HTTPServer(('0.0.0.0', 8000), RealInfoBloxMCP)
    server.serve_forever()
