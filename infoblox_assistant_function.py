"""
InfoBlox Assistant Function for Open WebUI
This function provides interactive suggestions and command completion
"""

class Filter:
    class Valves:
        INFOBLOX_URL: str = "http://localhost:8000"
        ENABLE_SUGGESTIONS: bool = True
        
    def __init__(self):
        self.valves = self.Valves()
        self.last_query = ""
        self.conversation_state = "new"
        
    def inlet(self, body: dict, user: dict) -> dict:
        """Process incoming messages and add context"""
        messages = body.get("messages", [])
        
        if messages and messages[-1].get("role") == "user":
            user_message = messages[-1].get("content", "").lower()
            self.last_query = user_message
            
            # Add system message with suggestions if this is a new conversation
            if len(messages) == 1 or self.conversation_state == "new":
                system_prompt = self.get_system_prompt()
                messages.insert(0, {
                    "role": "system",
                    "content": system_prompt
                })
                self.conversation_state = "active"
            
            # Inject suggestions based on keywords
            if self.valves.ENABLE_SUGGESTIONS:
                suggestions = self.get_contextual_suggestions(user_message)
                if suggestions and not any(word in user_message for word in ['execute tool:', 'search tools:', 'browse tools']):
                    # Add suggestions as a system message
                    messages[-1]["content"] = f"{messages[-1]['content']}\n\n[SUGGESTIONS: {suggestions}]"
        
        body["messages"] = messages
        return body
    
    def outlet(self, body: dict, user: dict) -> dict:
        """Process outgoing messages and format responses"""
        messages = body.get("messages", [])
        
        if messages and messages[-1].get("role") == "assistant":
            content = messages[-1].get("content", "")
            
            # Add interactive suggestions at the end of responses
            if self.valves.ENABLE_SUGGESTIONS and not "execute tool:" in content:
                suggestions = self.get_response_suggestions(self.last_query, content)
                if suggestions:
                    content += f"\n\n{suggestions}"
                    messages[-1]["content"] = content
        
        body["messages"] = messages
        return body
    
    def get_system_prompt(self):
        """Get the comprehensive system prompt"""
        return """You are the InfoBlox Assistant, a specialized AI that helps users manage their InfoBlox infrastructure through natural language. You have access to over 1,345 InfoBlox WAPI tools.

## Quick Start Commands:
• `browse tools` - Explore available tools
• `search tools: <keyword>` - Find specific tools
• `test connection` - Verify connectivity
• `list all networks` - View networks
• `help` - See all commands

## How to Use Tools:
1. Search: `search tools: create network`
2. Copy the example from search results
3. Modify parameters as needed
4. Execute: `execute tool: {"tool_id": "...", "parameters": {...}}`

## Response Guidelines:
- Always provide specific examples
- Use emojis for clarity (🔍 🛠️ ✅ ❌ 💡)
- Suggest related actions after each task
- Format results clearly with headers and bullets

When users are unsure, guide them with relevant suggestions and examples."""
    
    def get_contextual_suggestions(self, query):
        """Get suggestions based on query keywords"""
        query_lower = query.lower()
        
        # Map keywords to suggestions
        suggestions_map = {
            "network": ["create network", "list network", "network usage"],
            "dns": ["dns record", "dns zone", "create a record"],
            "dhcp": ["dhcp range", "lease", "fixed address"],
            "ip": ["available ip", "ip address", "next available"],
            "create": ["create network", "create zone", "create record"],
            "list": ["list network", "list dns zones", "list dhcp"],
            "delete": ["delete network", "delete record", "delete zone"],
            "hello": ["browse tools", "test connection", "help"],
            "hi": ["browse tools", "test connection", "help"],
            "help": ["browse tools", "search tools", "common tasks"]
        }
        
        # Find matching suggestions
        for keyword, suggestions in suggestions_map.items():
            if keyword in query_lower:
                return ", ".join(suggestions)
        
        # Default suggestions for unknown queries
        return "browse tools, search tools, help"
    
    def get_response_suggestions(self, query, response):
        """Get suggestions to append to responses"""
        # Don't add suggestions to tool execution results
        if any(phrase in response for phrase in ["executed successfully", "Error:", "Found", "tools matching"]):
            return ""
        
        # Context-aware suggestions
        if "network" in query.lower():
            return """💡 **Related Actions:**
• `search tools: network usage` - Check utilization
• `find available ips in <network>` - Find free IPs
• `search tools: network container` - Organize networks"""
        
        elif "dns" in query.lower():
            return """💡 **Related Actions:**
• `search tools: ptr record` - Reverse DNS
• `search tools: mx record` - Mail records
• `list dns zones` - View all zones"""
        
        elif "dhcp" in query.lower():
            return """💡 **Related Actions:**
• `search tools: lease` - View active leases
• `search tools: fixed address` - Create reservations
• `search tools: dhcp options` - Configure options"""
        
        elif any(word in query.lower() for word in ["hello", "hi", "start", "help"]):
            return """💡 **Quick Actions:**
• `test connection` - Verify InfoBlox is accessible
• `browse tools` - See all available tools
• `list all networks` - View your networks
• Type any network task and I'll help you find the right tools!"""
        
        return ""