import requests
import json
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib3
import os
import re
from typing import Dict, List, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Disable SSL warnings for self-signed certificates
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class DynamicInfoBloxMCP(BaseHTTPRequestHandler):
    """Dynamic MCP server that auto-discovers InfoBlox WAPI endpoints"""
    
    # Class variables to store discovered tools
    discovered_tools = {}
    wapi_objects = {}
    
    def __init__(self, *args, **kwargs):
        self.grid_master = os.getenv("GRID_MASTER_URL", "https://192.168.1.1/wapi/v2.13.1")
        self.username = os.getenv("INFOBLOX_USERNAME", "admin")
        self.password = os.getenv("INFOBLOX_PASSWORD", "infoblox")
        self.network_view = os.getenv("NETWORK_VIEW", "default")
        
        # Create session for InfoBlox
        self.session = requests.Session()
        self.session.auth = (self.username, self.password)
        self.session.verify = False
        self.session.headers.update({'Content-Type': 'application/json'})
        
        # Discover WAPI schema on first initialization
        if not self.__class__.discovered_tools:
            self.discover_wapi_schema()
        
        super().__init__(*args, **kwargs)
    
    def discover_wapi_schema(self):
        """Discover all available WAPI objects and their operations"""
        logger.info("Discovering InfoBlox WAPI schema...")
        
        try:
            # Get WAPI schema
            schema_response = self.session.get(f"{self.grid_master}/grid/b25lLmNsdXN0ZXIkMA:Infoblox?_return_fields=supported_objects")
            
            if schema_response.status_code == 200:
                # Get list of supported objects
                supported_objects = [
                    "network", "networkview", "ipv4address", "ipv6address",
                    "host", "host_ipv4addr", "host_ipv6addr", 
                    "record:a", "record:aaaa", "record:cname", "record:ptr", "record:mx", "record:txt", "record:srv",
                    "zone_auth", "zone_delegated", "zone_forward", "zone_stub",
                    "range", "fixedaddress", "grid", "member",
                    "lease", "macfilteraddress", "namedacl",
                    "nsgroup", "restartservicestatus", "scheduledtask",
                    "admingroup", "adminrole", "adminuser"
                ]
                
                # For each object type, discover its capabilities
                for obj_type in supported_objects:
                    self.discover_object_details(obj_type)
                
                logger.info(f"Discovered {len(self.__class__.discovered_tools)} WAPI tools")
            else:
                logger.warning("Could not fetch WAPI schema, using default tools")
                self.create_default_tools()
                
        except Exception as e:
            logger.error(f"Error discovering schema: {e}")
            self.create_default_tools()
    
    def discover_object_details(self, obj_type: str):
        """Discover details about a specific WAPI object type"""
        try:
            # Get schema for this object
            schema_url = f"{self.grid_master}/{obj_type}?_schema"
            schema_response = self.session.get(schema_url)
            
            if schema_response.status_code == 200:
                schema = schema_response.json()
                
                # Store object info
                self.__class__.wapi_objects[obj_type] = schema
                
                # Create tools for standard operations
                base_name = obj_type.replace(":", "_").replace(".", "_")
                
                # List/Search tool
                tool_id = f"list_{base_name}"
                self.__class__.discovered_tools[tool_id] = {
                    "type": "list",
                    "object": obj_type,
                    "name": f"List {obj_type}",
                    "description": f"List all {obj_type} objects with optional filters",
                    "parameters": {
                        "filters": {"type": "object", "description": "Field filters"},
                        "return_fields": {"type": "string", "description": "Comma-separated list of fields to return"},
                        "max_results": {"type": "integer", "description": "Maximum results to return", "default": 100}
                    }
                }
                
                # Get specific object tool
                tool_id = f"get_{base_name}"
                self.__class__.discovered_tools[tool_id] = {
                    "type": "get",
                    "object": obj_type,
                    "name": f"Get {obj_type}",
                    "description": f"Get a specific {obj_type} object by reference",
                    "parameters": {
                        "ref": {"type": "string", "description": "Object reference", "required": True},
                        "return_fields": {"type": "string", "description": "Comma-separated list of fields to return"}
                    }
                }
                
                # Create tool
                if schema.get("supports_create", True):
                    tool_id = f"create_{base_name}"
                    self.__class__.discovered_tools[tool_id] = {
                        "type": "create",
                        "object": obj_type,
                        "name": f"Create {obj_type}",
                        "description": f"Create a new {obj_type} object",
                        "parameters": {
                            "data": {"type": "object", "description": f"Object data for {obj_type}", "required": True}
                        }
                    }
                
                # Update tool
                if schema.get("supports_update", True):
                    tool_id = f"update_{base_name}"
                    self.__class__.discovered_tools[tool_id] = {
                        "type": "update",
                        "object": obj_type,
                        "name": f"Update {obj_type}",
                        "description": f"Update an existing {obj_type} object",
                        "parameters": {
                            "ref": {"type": "string", "description": "Object reference", "required": True},
                            "data": {"type": "object", "description": "Fields to update", "required": True}
                        }
                    }
                
                # Delete tool
                if schema.get("supports_delete", True):
                    tool_id = f"delete_{base_name}"
                    self.__class__.discovered_tools[tool_id] = {
                        "type": "delete",
                        "object": obj_type,
                        "name": f"Delete {obj_type}",
                        "description": f"Delete a {obj_type} object",
                        "parameters": {
                            "ref": {"type": "string", "description": "Object reference", "required": True}
                        }
                    }
                
                # Function tools (like next_available_ip)
                if schema.get("functions"):
                    for func_name, func_info in schema["functions"].items():
                        tool_id = f"{base_name}_{func_name}"
                        self.__class__.discovered_tools[tool_id] = {
                            "type": "function",
                            "object": obj_type,
                            "function": func_name,
                            "name": f"{obj_type} - {func_name}",
                            "description": func_info.get("description", f"Execute {func_name} on {obj_type}"),
                            "parameters": func_info.get("parameters", {})
                        }
                        
        except Exception as e:
            logger.debug(f"Could not discover {obj_type}: {e}")
    
    def create_default_tools(self):
        """Create default hardcoded tools as fallback"""
        self.__class__.discovered_tools = {
            "list_networks": {
                "type": "list",
                "object": "network",
                "name": "List Networks",
                "description": "List all networks in InfoBlox",
                "parameters": {
                    "network_view": {"type": "string", "description": "Network view name"},
                    "network": {"type": "string", "description": "Filter by network CIDR"}
                }
            },
            "get_network": {
                "type": "get",
                "object": "network",
                "name": "Get Network",
                "description": "Get details of a specific network",
                "parameters": {
                    "network": {"type": "string", "description": "Network CIDR", "required": True}
                }
            },
            "create_network": {
                "type": "create",
                "object": "network",
                "name": "Create Network",
                "description": "Create a new network",
                "parameters": {
                    "network": {"type": "string", "description": "Network CIDR", "required": True},
                    "comment": {"type": "string", "description": "Network description"}
                }
            },
            "list_dns_zones": {
                "type": "list",
                "object": "zone_auth",
                "name": "List DNS Zones",
                "description": "List all DNS zones",
                "parameters": {
                    "view": {"type": "string", "description": "DNS view name"}
                }
            },
            "list_ip_addresses": {
                "type": "list",
                "object": "ipv4address",
                "name": "List IP Addresses",
                "description": "List IP addresses in a network",
                "parameters": {
                    "network": {"type": "string", "description": "Network CIDR"},
                    "status": {"type": "string", "description": "Filter by status (USED, UNUSED)"}
                }
            }
        }
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == "/health":
            self.send_json({
                "status": "healthy", 
                "grid_master": self.grid_master,
                "tools_count": len(self.__class__.discovered_tools)
            })
        
        elif self.path == "/tools":
            # Return all discovered tools in MCP format
            tools_list = []
            for tool_id, tool_info in self.__class__.discovered_tools.items():
                tools_list.append({
                    "name": tool_id,
                    "description": tool_info["description"],
                    "inputSchema": {
                        "type": "object",
                        "properties": tool_info.get("parameters", {}),
                        "required": [k for k, v in tool_info.get("parameters", {}).items() if v.get("required")]
                    }
                })
            
            self.send_json({
                "tools": tools_list
            })
        
        elif self.path == "/v1/models":
            self.send_json({
                "data": [{
                    "id": "infoblox-dynamic",
                    "object": "model",
                    "owned_by": "infoblox-mcp"
                }]
            })
        
        else:
            self.send_json({"status": "ok"})
    
    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        body = self.rfile.read(content_length).decode('utf-8')
        
        if self.path == "/v1/chat/completions":
            try:
                data = json.loads(body)
                messages = data.get("messages", [])
                
                if messages:
                    user_message = messages[-1].get("content", "")
                    
                    # Check if this is a tool invocation
                    if user_message.startswith("TOOL:"):
                        # Parse tool invocation
                        tool_data = json.loads(user_message[5:])
                        tool_name = tool_data.get("tool")
                        params = tool_data.get("params", {})
                        
                        result = self.execute_tool(tool_name, params)
                        response_content = json.dumps(result, indent=2)
                    else:
                        # Natural language processing
                        response_content = self.process_natural_language(user_message)
                else:
                    response_content = self.get_help_message()
                
                response = {
                    "id": "chatcmpl-" + str(int(datetime.now().timestamp())),
                    "object": "chat.completion",
                    "created": int(datetime.now().timestamp()),
                    "model": "infoblox-dynamic",
                    "choices": [{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_content
                        },
                        "finish_reason": "stop"
                    }]
                }
                self.send_json(response)
                
            except Exception as e:
                self.send_json({"error": str(e)})
    
    def execute_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a discovered tool"""
        if tool_name not in self.__class__.discovered_tools:
            return {"error": f"Unknown tool: {tool_name}"}
        
        tool_info = self.__class__.discovered_tools[tool_name]
        obj_type = tool_info["object"]
        tool_type = tool_info["type"]
        
        try:
            if tool_type == "list":
                # List objects
                api_params = {
                    "_max_results": params.get("max_results", 100)
                }
                if params.get("return_fields"):
                    api_params["_return_fields"] = params["return_fields"]
                
                # Add any filters
                filters = params.get("filters", {})
                api_params.update(filters)
                
                response = self.session.get(f"{self.grid_master}/{obj_type}", params=api_params)
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "data": response.json(),
                        "count": len(response.json())
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif tool_type == "get":
                # Get specific object
                ref = params.get("ref")
                if not ref:
                    return {"error": "Missing required parameter: ref"}
                
                api_params = {}
                if params.get("return_fields"):
                    api_params["_return_fields"] = params["return_fields"]
                
                response = self.session.get(f"{self.grid_master}/{ref}", params=api_params)
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "data": response.json()
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif tool_type == "create":
                # Create object
                data = params.get("data", {})
                response = self.session.post(f"{self.grid_master}/{obj_type}", json=data)
                
                if response.status_code in [200, 201]:
                    return {
                        "success": True,
                        "ref": response.json(),
                        "message": f"Created {obj_type} successfully"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif tool_type == "update":
                # Update object
                ref = params.get("ref")
                data = params.get("data", {})
                
                if not ref:
                    return {"error": "Missing required parameter: ref"}
                
                response = self.session.put(f"{self.grid_master}/{ref}", json=data)
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "ref": response.json(),
                        "message": f"Updated {obj_type} successfully"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif tool_type == "delete":
                # Delete object
                ref = params.get("ref")
                if not ref:
                    return {"error": "Missing required parameter: ref"}
                
                response = self.session.delete(f"{self.grid_master}/{ref}")
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": f"Deleted {obj_type} successfully"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif tool_type == "function":
                # Execute function
                ref = params.get("ref")
                func_name = tool_info["function"]
                func_params = {k: v for k, v in params.items() if k != "ref"}
                
                response = self.session.post(
                    f"{self.grid_master}/{ref}",
                    params={"_function": func_name},
                    json=func_params
                )
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "data": response.json()
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            else:
                return {"error": f"Unknown tool type: {tool_type}"}
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def process_natural_language(self, query: str) -> str:
        """Process natural language queries"""
        query_lower = query.lower()
        
        # Map natural language to tool invocations
        if "list all networks" in query_lower or "show all networks" in query_lower:
            result = self.execute_tool("list_networks", {
                "filters": {"network_view": self.network_view},
                "return_fields": "network,comment,network_view"
            })
            
            if result.get("success"):
                networks = result["data"]
                response = f"📊 **Networks in InfoBlox** (View: {self.network_view}):\n\n"
                for i, net in enumerate(networks[:25], 1):
                    response += f"{i}. **{net.get('network')}**\n"
                    if net.get('comment'):
                        response += f"   • {net['comment']}\n"
                    response += "\n"
                if len(networks) > 25:
                    response += f"... and {len(networks) - 25} more networks"
                return response
            else:
                return f"❌ Error: {result.get('error')}"
        
        elif "audit network" in query_lower:
            # Extract network
            network_match = re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', query)
            if network_match:
                network = network_match.group(1)
                
                # Get IP addresses in network
                result = self.execute_tool("list_ipv4address", {
                    "filters": {"network": network},
                    "return_fields": "ip_address,status,names,mac_address",
                    "max_results": 1000
                })
                
                if result.get("success"):
                    ips = result["data"]
                    response = f"🔍 **Network Audit for {network}**\n\n"
                    
                    used = [ip for ip in ips if ip.get("status") == "USED"]
                    unused = [ip for ip in ips if ip.get("status") != "USED"]
                    
                    response += f"📊 **Summary:**\n"
                    response += f"• Total IPs tracked: {len(ips)}\n"
                    response += f"• Used: {len(used)}\n"
                    response += f"• Available: {len(unused)}\n\n"
                    
                    if used:
                        response += "✅ **Used IPs:**\n"
                        for ip in used[:20]:
                            response += f"• {ip['ip_address']}"
                            if ip.get('names'):
                                response += f" - {', '.join(ip['names'])}"
                            if ip.get('mac_address'):
                                response += f" (MAC: {ip['mac_address']})"
                            response += "\n"
                        if len(used) > 20:
                            response += f"... and {len(used) - 20} more\n"
                    
                    return response
                else:
                    return f"❌ Error: {result.get('error')}"
            else:
                return "Please specify a network to audit (e.g., 'audit network 10.0.0.0/24')"
        
        elif "help" in query_lower or "?" in query_lower:
            return self.get_help_message()
        
        else:
            # Show available tools
            return self.get_help_message()
    
    def get_help_message(self) -> str:
        """Get help message with available tools"""
        help_msg = f"🚀 **Dynamic InfoBlox MCP Server**\n\n"
        help_msg += f"Connected to: {self.grid_master}\n"
        help_msg += f"Discovered Tools: {len(self.__class__.discovered_tools)}\n\n"
        
        help_msg += "**Available Tools:**\n\n"
        
        # Group tools by object type
        tools_by_object = {}
        for tool_id, tool_info in self.__class__.discovered_tools.items():
            obj = tool_info["object"]
            if obj not in tools_by_object:
                tools_by_object[obj] = []
            tools_by_object[obj].append((tool_id, tool_info))
        
        # Show first few object types
        for obj_type in list(tools_by_object.keys())[:5]:
            help_msg += f"**{obj_type}:**\n"
            for tool_id, tool_info in tools_by_object[obj_type][:3]:
                help_msg += f"• `{tool_id}` - {tool_info['description']}\n"
            help_msg += "\n"
        
        if len(tools_by_object) > 5:
            help_msg += f"... and {len(tools_by_object) - 5} more object types\n\n"
        
        help_msg += "**Example Natural Language Commands:**\n"
        help_msg += "• list all networks\n"
        help_msg += "• audit network 10.0.0.0/24\n"
        help_msg += "• show DNS zones\n"
        help_msg += "• find available IPs in 10.0.0.0/24\n\n"
        
        help_msg += "**Direct Tool Usage:**\n"
        help_msg += 'Use format: `TOOL:{"tool":"tool_name","params":{...}}`'
        
        return help_msg
    
    def send_json(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
    
    def log_message(self, format, *args):
        """Log messages"""
        logger.info(f"{format % args}")

if __name__ == "__main__":
    print("🚀 Starting Dynamic InfoBlox MCP Server...")
    print(f"Grid Master: {os.getenv('GRID_MASTER_URL', 'Not set')}")
    print(f"Network View: {os.getenv('NETWORK_VIEW', 'default')}")
    print("Discovering WAPI schema...")
    
    server = HTTPServer(('0.0.0.0', 8000), DynamicInfoBloxMCP)
    
    print(f"\n✅ Server ready with {len(DynamicInfoBloxMCP.discovered_tools)} discovered tools!")
    print("Access tools at: http://localhost:8000/tools")
    
    server.serve_forever()
