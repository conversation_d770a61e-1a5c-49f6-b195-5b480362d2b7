import requests
import json
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib3
import os
import re
from typing import Dict, List, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class CompleteInfoBloxMCP(BaseHTTPRequestHandler):
    """Complete InfoBlox MCP Server with ALL WAPI endpoints as tools"""
    
    # Class variable to store tools
    wapi_tools = {}
    wapi_objects = {}
    
    def __init__(self, *args, **kwargs):
        self.grid_master = os.getenv("GRID_MASTER_URL", "https://192.168.1.1/wapi/v2.13.1")
        self.username = os.getenv("INFOBLOX_USERNAME", "admin")
        self.password = os.getenv("INFOBLOX_PASSWORD", "infoblox")
        self.network_view = os.getenv("NETWORK_VIEW", "default")
        
        # Create session
        self.session = requests.Session()
        self.session.auth = (self.username, self.password)
        self.session.verify = False
        self.session.headers.update({'Content-Type': 'application/json'})
        
        # Load discovered tools on first init
        if not self.__class__.wapi_tools:
            self.load_discovered_tools()
        
        super().__init__(*args, **kwargs)
    
    def load_discovered_tools(self):
        """Load tools from discovery"""
        try:
            # Load discovered objects
            if os.path.exists('discovered_wapi_objects.json'):
                with open('discovered_wapi_objects.json', 'r') as f:
                    self.__class__.wapi_objects = json.load(f)
                logger.info(f"Loaded {len(self.__class__.wapi_objects)} WAPI objects")
            
            # Load tool definitions
            if os.path.exists('infoblox_mcp_tools.json'):
                with open('infoblox_mcp_tools.json', 'r') as f:
                    tools = json.load(f)
                    for tool in tools:
                        self.__class__.wapi_tools[tool['id']] = tool
                logger.info(f"Loaded {len(self.__class__.wapi_tools)} MCP tools")
            else:
                # Fallback to dynamic discovery
                self.discover_wapi_endpoints()
        except Exception as e:
            logger.error(f"Error loading tools: {e}")
            self.discover_wapi_endpoints()
    
    def discover_wapi_endpoints(self):
        """Dynamically discover WAPI endpoints"""
        logger.info("Dynamically discovering WAPI endpoints...")
        
        # Standard objects to try
        standard_objects = [
            "network", "networkview", "ipv4address", "ipv6address",
            "zone_auth", "zone_forward", "zone_delegated",
            "record:a", "record:aaaa", "record:cname", "record:ptr", "record:mx", "record:txt",
            "range", "fixedaddress", "lease", "grid", "member",
            "host", "host_ipv4addr", "host_ipv6addr"
        ]
        
        for obj_type in standard_objects:
            try:
                # Test if object exists
                response = self.session.get(
                    f"{self.grid_master}/{obj_type}",
                    params={"_max_results": 1}
                )
                
                if response.status_code == 200:
                    self.create_tools_for_object(obj_type)
            except:
                pass
    
    def create_tools_for_object(self, obj_type: str):
        """Create tools for a WAPI object"""
        safe_name = obj_type.replace(":", "_").replace(".", "_")
        
        # List tool
        tool_id = f"list_{safe_name}"
        self.__class__.wapi_tools[tool_id] = {
            "id": tool_id,
            "object": obj_type,
            "operation": "list",
            "name": f"List {obj_type}",
            "description": f"List/search {obj_type} objects"
        }
        
        # Get tool
        tool_id = f"get_{safe_name}"
        self.__class__.wapi_tools[tool_id] = {
            "id": tool_id,
            "object": obj_type,
            "operation": "get",
            "name": f"Get {obj_type}",
            "description": f"Get specific {obj_type}"
        }
        
        # Create tool
        tool_id = f"create_{safe_name}"
        self.__class__.wapi_tools[tool_id] = {
            "id": tool_id,
            "object": obj_type,
            "operation": "create",
            "name": f"Create {obj_type}",
            "description": f"Create new {obj_type}"
        }
        
        # Update tool
        tool_id = f"update_{safe_name}"
        self.__class__.wapi_tools[tool_id] = {
            "id": tool_id,
            "object": obj_type,
            "operation": "update",
            "name": f"Update {obj_type}",
            "description": f"Update {obj_type}"
        }
        
        # Delete tool
        tool_id = f"delete_{safe_name}"
        self.__class__.wapi_tools[tool_id] = {
            "id": tool_id,
            "object": obj_type,
            "operation": "delete",
            "name": f"Delete {obj_type}",
            "description": f"Delete {obj_type}"
        }
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == "/health":
            self.send_json({
                "status": "healthy",
                "grid_master": self.grid_master,
                "tools_loaded": len(self.__class__.wapi_tools),
                "objects_discovered": len(self.__class__.wapi_objects)
            })
        
        elif self.path == "/tools":
            # Return all tools in MCP format
            tools_list = []
            for tool_id, tool_info in self.__class__.wapi_tools.items():
                tools_list.append({
                    "name": tool_id,
                    "description": tool_info.get("description", ""),
                    "object": tool_info.get("object", ""),
                    "operation": tool_info.get("operation", ""),
                    "inputSchema": self.get_tool_schema(tool_info)
                })
            
            self.send_json({
                "tools": tools_list,
                "total": len(tools_list),
                "categories": self.categorize_tools()
            })
        
        elif self.path == "/tools/summary":
            # Summary of available tools
            self.send_json(self.get_tools_summary())
        
        elif self.path == "/v1/models":
            self.send_json({
                "data": [{
                    "id": "infoblox-complete",
                    "object": "model",
                    "owned_by": "infoblox-wapi-complete"
                }]
            })
        
        else:
            self.send_json({"status": "ok"})
    
    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        body = self.rfile.read(content_length).decode('utf-8')
        
        if self.path == "/v1/chat/completions":
            try:
                data = json.loads(body)
                messages = data.get("messages", [])
                
                if messages:
                    user_message = messages[-1].get("content", "")
                    
                    # Check for direct tool invocation
                    if user_message.startswith("TOOL:"):
                        tool_data = json.loads(user_message[5:])
                        result = self.execute_tool(
                            tool_data.get("tool"),
                            tool_data.get("params", {})
                        )
                        response_content = json.dumps(result, indent=2)
                    else:
                        # Natural language processing
                        response_content = self.process_natural_language(user_message)
                else:
                    response_content = self.get_help_message()
                
                response = {
                    "id": "chatcmpl-" + str(int(datetime.now().timestamp())),
                    "object": "chat.completion",
                    "created": int(datetime.now().timestamp()),
                    "model": "infoblox-complete",
                    "choices": [{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_content
                        },
                        "finish_reason": "stop"
                    }]
                }
                self.send_json(response)
                
            except Exception as e:
                logger.error(f"Error processing request: {e}")
                self.send_json({"error": str(e)})
    
    def execute_tool(self, tool_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a WAPI tool"""
        if tool_id not in self.__class__.wapi_tools:
            return {"error": f"Unknown tool: {tool_id}"}
        
        tool_info = self.__class__.wapi_tools[tool_id]
        obj_type = tool_info["object"]
        operation = tool_info["operation"]
        
        try:
            if operation == "list":
                # List/search objects
                api_params = {
                    "_max_results": params.get("max_results", 100)
                }
                
                # Add return fields if specified
                if params.get("return_fields"):
                    api_params["_return_fields"] = params["return_fields"]
                elif params.get("_return_fields"):
                    api_params["_return_fields"] = params["_return_fields"]
                
                # Add any other parameters as filters
                for key, value in params.items():
                    if key not in ["max_results", "return_fields", "_return_fields"]:
                        api_params[key] = value
                
                response = self.session.get(
                    f"{self.grid_master}/{obj_type}",
                    params=api_params
                )
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "data": response.json(),
                        "count": len(response.json())
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif operation == "get":
                # Get specific object
                ref = params.get("ref") or params.get("_ref")
                if not ref:
                    return {"error": "Missing required parameter: ref"}
                
                api_params = {}
                if params.get("return_fields"):
                    api_params["_return_fields"] = params["return_fields"]
                
                response = self.session.get(
                    f"{self.grid_master}/{ref}",
                    params=api_params
                )
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "data": response.json()
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif operation == "create":
                # Create object
                response = self.session.post(
                    f"{self.grid_master}/{obj_type}",
                    json=params
                )
                
                if response.status_code in [200, 201]:
                    return {
                        "success": True,
                        "ref": response.json(),
                        "message": f"Created {obj_type} successfully"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif operation == "update":
                # Update object
                ref = params.pop("ref", None) or params.pop("_ref", None)
                if not ref:
                    return {"error": "Missing required parameter: ref"}
                
                response = self.session.put(
                    f"{self.grid_master}/{ref}",
                    json=params
                )
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "ref": response.json(),
                        "message": f"Updated {obj_type} successfully"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif operation == "delete":
                # Delete object
                ref = params.get("ref") or params.get("_ref")
                if not ref:
                    return {"error": "Missing required parameter: ref"}
                
                response = self.session.delete(f"{self.grid_master}/{ref}")
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": f"Deleted {obj_type} successfully"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif operation == "function":
                # Execute function
                ref = params.pop("ref", None) or params.pop("_ref", None)
                func_name = tool_info.get("function")
                
                if not ref:
                    return {"error": "Missing required parameter: ref"}
                
                response = self.session.post(
                    f"{self.grid_master}/{ref}",
                    params={"_function": func_name},
                    json=params
                )
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "data": response.json()
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            else:
                return {"error": f"Unknown operation: {operation}"}
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def process_natural_language(self, query: str) -> str:
        """Process natural language queries"""
        query_lower = query.lower()
        
        # List all networks
        if any(phrase in query_lower for phrase in ["list all network", "show all network", "list network"]):
            result = self.execute_tool("list_network", {
                "network_view": self.network_view,
                "return_fields": "network,comment,network_view",
                "max_results": 100
            })
            
            if result.get("success"):
                networks = result["data"]
                response = f"📊 **Networks in InfoBlox** (View: {self.network_view}):\n\n"
                for i, net in enumerate(networks[:25], 1):
                    response += f"{i}. **{net.get('network')}**\n"
                    if net.get('comment'):
                        response += f"   • {net['comment']}\n"
                    response += "\n"
                if len(networks) > 25:
                    response += f"... showing 25 of {len(networks)} total networks"
                return response
            else:
                return f"❌ Error: {result.get('error')}"
        
        # Audit network
        elif "audit" in query_lower and re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', query):
            network = re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', query).group(1)
            
            # Get IPs in network
            result = self.execute_tool("list_ipv4address", {
                "network": network,
                "return_fields": "ip_address,status,names,mac_address",
                "max_results": 1000
            })
            
            if result.get("success"):
                ips = result["data"]
                response = f"🔍 **Network Audit for {network}**\n\n"
                
                used = [ip for ip in ips if ip.get("status") == "USED"]
                unused = [ip for ip in ips if ip.get("status") != "USED"]
                
                response += f"📊 **Summary:**\n"
                response += f"• Total IPs tracked: {len(ips)}\n"
                response += f"• Used: {len(used)}\n"
                response += f"• Available: {len(unused)}\n\n"
                
                if used:
                    response += "✅ **Used IPs:**\n"
                    for ip in used[:20]:
                        response += f"• {ip['ip_address']}"
                        if ip.get('names'):
                            response += f" - {', '.join(ip['names'])}"
                        if ip.get('mac_address'):
                            response += f" (MAC: {ip['mac_address']})"
                        response += "\n"
                    if len(used) > 20:
                        response += f"... and {len(used) - 20} more\n"
                
                return response
            else:
                return f"❌ Error: {result.get('error')}"
        
        # Show available tools
        elif "help" in query_lower or "tools" in query_lower:
            return self.get_help_message()
        
        else:
            # Try to match query to tools
            return self.get_help_message()
    
    def get_tool_schema(self, tool_info: Dict[str, Any]) -> Dict[str, Any]:
        """Get input schema for a tool"""
        operation = tool_info.get("operation", "")
        
        if operation == "list":
            return {
                "type": "object",
                "properties": {
                    "max_results": {
                        "type": "integer",
                        "description": "Maximum results to return",
                        "default": 100
                    },
                    "return_fields": {
                        "type": "string",
                        "description": "Comma-separated list of fields to return"
                    }
                },
                "additionalProperties": True
            }
        
        elif operation == "get":
            return {
                "type": "object",
                "properties": {
                    "ref": {
                        "type": "string",
                        "description": "Object reference"
                    },
                    "return_fields": {
                        "type": "string",
                        "description": "Comma-separated list of fields to return"
                    }
                },
                "required": ["ref"]
            }
        
        elif operation == "create":
            return {
                "type": "object",
                "additionalProperties": True,
                "description": f"Object data for creating {tool_info.get('object', 'object')}"
            }
        
        elif operation == "update":
            return {
                "type": "object",
                "properties": {
                    "ref": {
                        "type": "string",
                        "description": "Object reference"
                    }
                },
                "required": ["ref"],
                "additionalProperties": True
            }
        
        elif operation == "delete":
            return {
                "type": "object",
                "properties": {
                    "ref": {
                        "type": "string",
                        "description": "Object reference"
                    }
                },
                "required": ["ref"]
            }
        
        elif operation == "function":
            return {
                "type": "object",
                "properties": {
                    "ref": {
                        "type": "string",
                        "description": "Object reference"
                    }
                },
                "required": ["ref"],
                "additionalProperties": True
            }
        
        return {"type": "object", "additionalProperties": True}
    
    def categorize_tools(self) -> Dict[str, int]:
        """Categorize tools by type"""
        categories = {}
        for tool_id, tool_info in self.__class__.wapi_tools.items():
            obj_type = tool_info.get("object", "unknown")
            category = self.get_category(obj_type)
            categories[category] = categories.get(category, 0) + 1
        return categories
    
    def get_category(self, obj_type: str) -> str:
        """Get category for an object type"""
        if "network" in obj_type and "discovery" not in obj_type:
            return "Network"
        elif "record:" in obj_type:
            return "DNS Records"
        elif "zone" in obj_type:
            return "DNS Zones"
        elif any(x in obj_type for x in ["dhcp", "range", "lease", "fixed"]):
            return "DHCP"
        elif any(x in obj_type for x in ["ipv4", "ipv6", "vlan"]):
            return "IPAM"
        elif any(x in obj_type for x in ["admin", "saml", "certificate"]):
            return "Security"
        elif "discovery" in obj_type:
            return "Discovery"
        elif any(x in obj_type for x in ["grid", "member"]):
            return "Grid"
        else:
            return "Other"
    
    def get_tools_summary(self) -> Dict[str, Any]:
        """Get summary of available tools"""
        summary = {
            "total_tools": len(self.__class__.wapi_tools),
            "objects": len(set(t.get("object") for t in self.__class__.wapi_tools.values())),
            "categories": self.categorize_tools(),
            "operations": {}
        }
        
        # Count by operation
        for tool_info in self.__class__.wapi_tools.values():
            op = tool_info.get("operation", "unknown")
            summary["operations"][op] = summary["operations"].get(op, 0) + 1
        
        # Sample tools by category
        summary["sample_tools"] = {}
        for tool_id, tool_info in list(self.__class__.wapi_tools.items())[:20]:
            category = self.get_category(tool_info.get("object", ""))
            if category not in summary["sample_tools"]:
                summary["sample_tools"][category] = []
            if len(summary["sample_tools"][category]) < 3:
                summary["sample_tools"][category].append({
                    "id": tool_id,
                    "name": tool_info.get("name", tool_id),
                    "object": tool_info.get("object", "")
                })
        
        return summary
    
    def get_help_message(self) -> str:
        """Get help message"""
        summary = self.get_tools_summary()
        
        help_msg = f"""🚀 **InfoBlox Complete MCP Server**

**Status:**
• Connected to: {self.grid_master}
• Total Tools Available: {summary['total_tools']}
• WAPI Objects: {summary['objects']}

**Tools by Category:**
"""
        
        for category, count in sorted(summary['categories'].items(), key=lambda x: x[1], reverse=True):
            help_msg += f"• {category}: {count} tools\n"
        
        help_msg += f"\n**Operations Available:**\n"
        for op, count in summary['operations'].items():
            help_msg += f"• {op}: {count} tools\n"
        
        help_msg += """
**Natural Language Commands:**
• `list all networks` - Show all networks
• `audit network 10.0.0.0/24` - Audit a network
• `search IP **********` - Get IP information
• `list DNS zones` - Show DNS zones
• `find available IPs in 10.0.0.0/24` - Find free IPs

**Direct Tool Usage:**
```
TOOL:{"tool":"tool_id","params":{...}}
```

**View All Tools:**
http://localhost:8000/tools (detailed list)
http://localhost:8000/tools/summary (summary)

💡 This server exposes ALL InfoBlox WAPI endpoints as MCP tools!"""
        
        return help_msg
    
    def send_json(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
    
    def log_message(self, format, *args):
        """Log messages"""
        logger.info(f"{format % args}")

if __name__ == "__main__":
    print("🚀 Starting Complete InfoBlox MCP Server...")
    print(f"Grid Master: {os.getenv('GRID_MASTER_URL', 'Not set')}")
    print(f"Network View: {os.getenv('NETWORK_VIEW', 'default')}")
    
    server = HTTPServer(('0.0.0.0', 8000), CompleteInfoBloxMCP)
    
    print(f"\n✅ Server ready with {len(CompleteInfoBloxMCP.wapi_tools)} tools!")
    print("\nEndpoints:")
    print("• View all tools: http://localhost:8000/tools")
    print("• Tools summary: http://localhost:8000/tools/summary")
    print("• Health check: http://localhost:8000/health")
    
    server.serve_forever()
