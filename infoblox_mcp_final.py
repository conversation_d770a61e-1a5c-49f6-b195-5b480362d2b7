import requests
import json
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib3
import os
import re
from typing import Dict, List, Any

# Disable SSL warnings for self-signed certificates
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class InfoBloxMCPServer(BaseHTTPRequestHandler):
    """InfoBlox MCP Server with comprehensive WAPI support"""
    
    def __init__(self, *args, **kwargs):
        self.grid_master = os.getenv("GRID_MASTER_URL", "https://192.168.1.1/wapi/v2.13.1")
        self.username = os.getenv("INFOBLOX_USERNAME", "admin")
        self.password = os.getenv("INFOBLOX_PASSWORD", "infoblox")
        self.network_view = os.getenv("NETWORK_VIEW", "default")
        
        # Create session for InfoBlox
        self.session = requests.Session()
        self.session.auth = (self.username, self.password)
        self.session.verify = False
        self.session.headers.update({'Content-Type': 'application/json'})
        
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == "/health":
            self.send_json({
                "status": "healthy", 
                "grid_master": self.grid_master,
                "network_view": self.network_view
            })
        
        elif self.path == "/tools":
            # Return comprehensive list of InfoBlox tools
            self.send_json({
                "tools": self.get_available_tools()
            })
        
        elif self.path == "/v1/models":
            self.send_json({
                "data": [{
                    "id": "infoblox-assistant",
                    "object": "model",
                    "owned_by": "infoblox-wapi"
                }]
            })
        
        else:
            self.send_json({"status": "ok"})
    
    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        body = self.rfile.read(content_length).decode('utf-8')
        
        if self.path == "/v1/chat/completions":
            try:
                data = json.loads(body)
                messages = data.get("messages", [])
                
                if messages:
                    user_message = messages[-1].get("content", "")
                    response_content = self.process_query(user_message)
                else:
                    response_content = self.get_help_message()
                
                response = {
                    "id": "chatcmpl-" + str(int(datetime.now().timestamp())),
                    "object": "chat.completion",
                    "created": int(datetime.now().timestamp()),
                    "model": "infoblox-assistant",
                    "choices": [{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_content
                        },
                        "finish_reason": "stop"
                    }]
                }
                self.send_json(response)
                
            except Exception as e:
                self.send_json({"error": str(e)})
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Return list of available InfoBlox tools"""
        return [
            # Network Management Tools
            {
                "name": "list_networks",
                "description": "List all networks with optional filters",
                "parameters": {
                    "network_view": {"type": "string", "description": "Network view (default: 'default')"},
                    "network": {"type": "string", "description": "Filter by network CIDR"},
                    "comment": {"type": "string", "description": "Filter by comment text"}
                }
            },
            {
                "name": "create_network",
                "description": "Create a new network",
                "parameters": {
                    "network": {"type": "string", "description": "Network CIDR (required)", "required": True},
                    "comment": {"type": "string", "description": "Network description"},
                    "network_view": {"type": "string", "description": "Network view (default: 'default')"}
                }
            },
            {
                "name": "audit_network",
                "description": "Audit a network - show all IPs and their status",
                "parameters": {
                    "network": {"type": "string", "description": "Network CIDR to audit", "required": True}
                }
            },
            # IP Address Management Tools
            {
                "name": "find_available_ips",
                "description": "Find available IP addresses in a network",
                "parameters": {
                    "network": {"type": "string", "description": "Network CIDR", "required": True},
                    "count": {"type": "integer", "description": "Number of IPs to find (default: 5)"}
                }
            },
            {
                "name": "search_ip",
                "description": "Search for information about a specific IP address",
                "parameters": {
                    "ip_address": {"type": "string", "description": "IP address to search", "required": True}
                }
            },
            # DNS Management Tools
            {
                "name": "list_dns_zones",
                "description": "List all DNS zones",
                "parameters": {
                    "view": {"type": "string", "description": "DNS view (default: 'default')"},
                    "zone_type": {"type": "string", "description": "Type: forward, reverse, or all"}
                }
            },
            {
                "name": "create_dns_record",
                "description": "Create a DNS record",
                "parameters": {
                    "type": {"type": "string", "description": "Record type: A, AAAA, CNAME, PTR, TXT, MX", "required": True},
                    "name": {"type": "string", "description": "Record name", "required": True},
                    "value": {"type": "string", "description": "Record value", "required": True},
                    "view": {"type": "string", "description": "DNS view (default: 'default')"}
                }
            },
            # DHCP Management Tools
            {
                "name": "list_dhcp_ranges",
                "description": "List DHCP ranges",
                "parameters": {
                    "network": {"type": "string", "description": "Filter by network CIDR"},
                    "network_view": {"type": "string", "description": "Network view (default: 'default')"}
                }
            },
            # Host Management Tools
            {
                "name": "create_host_record",
                "description": "Create a host record",
                "parameters": {
                    "hostname": {"type": "string", "description": "Hostname", "required": True},
                    "ip_address": {"type": "string", "description": "IP address", "required": True},
                    "mac_address": {"type": "string", "description": "MAC address (optional)"}
                }
            },
            # Grid Management Tools
            {
                "name": "get_grid_info",
                "description": "Get InfoBlox grid information",
                "parameters": {}
            }
        ]
    
    def process_query(self, query: str) -> str:
        """Process user query"""
        query_lower = query.lower()
        
        # Test connection
        if any(word in query_lower for word in ["test", "connection", "verify"]):
            return self.test_connection()
        
        # List networks
        elif any(phrase in query_lower for phrase in ["list all network", "show all network", "list network", "show network"]):
            return self.list_networks()
        
        # Audit network
        elif "audit" in query_lower and re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', query):
            network = re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', query).group(1)
            return self.audit_network(network)
        
        # Search IP
        elif ("search" in query_lower or "find" in query_lower or "lookup" in query_lower) and re.search(r'(\d+\.\d+\.\d+\.\d+)(?!/)', query):
            ip = re.search(r'(\d+\.\d+\.\d+\.\d+)(?!/)', query).group(1)
            return self.search_ip(ip)
        
        # Find available IPs
        elif "available" in query_lower and "ip" in query_lower and re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', query):
            network = re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', query).group(1)
            return self.find_available_ips(network)
        
        # List DNS zones
        elif any(word in query_lower for word in ["dns", "zone", "domain"]):
            return self.list_dns_zones()
        
        # List DHCP ranges
        elif "dhcp" in query_lower:
            return self.list_dhcp_ranges()
        
        # Grid info
        elif "grid" in query_lower and "info" in query_lower:
            return self.get_grid_info()
        
        # Show help
        else:
            return self.get_help_message()
    
    def test_connection(self) -> str:
        """Test InfoBlox connection"""
        try:
            response = self.session.get(
                f"{self.grid_master}/grid",
                params={"_return_fields": "name,ntp_setting"}
            )
            
            if response.status_code == 200:
                grid_info = response.json()
                if isinstance(grid_info, list) and grid_info:
                    grid_data = grid_info[0]
                    return f"""✅ **Successfully connected to InfoBlox!**

**Grid Information:**
• Grid Name: {grid_data.get('name', 'Unknown')}
• Grid Master: {self.grid_master}
• API Version: {self.grid_master.split('/')[-1]}
• Network View: {self.network_view}
• Authentication: Successful

Connection test passed!"""
            else:
                return f"❌ Connection failed: HTTP {response.status_code}\n{response.text[:200]}"
        except Exception as e:
            return f"❌ Connection error: {str(e)}"
    
    def list_networks(self) -> str:
        """List all networks"""
        try:
            params = {
                "_max_results": 100,
                "_return_fields": "network,comment,network_view",
                "network_view": self.network_view
            }
            
            response = self.session.get(f"{self.grid_master}/network", params=params)
            
            if response.status_code == 200:
                networks = response.json()
                
                if not networks:
                    return f"No networks found in view '{self.network_view}'"
                
                result = f"📊 **Networks in InfoBlox** (View: {self.network_view}):\n\n"
                
                for i, net in enumerate(networks[:25], 1):
                    network = net.get('network', 'Unknown')
                    comment = net.get('comment', 'No description')
                    
                    result += f"**{i}. {network}**\n"
                    result += f"   • Description: {comment}\n\n"
                
                if len(networks) > 25:
                    result += f"... showing 25 of {len(networks)} total networks\n"
                    result += f"\nUse 'audit network X.X.X.X/XX' to see details of a specific network"
                
                return result
            else:
                return f"❌ Error fetching networks: HTTP {response.status_code}"
                
        except Exception as e:
            return f"❌ Error: {str(e)}"
    
    def audit_network(self, network_cidr: str) -> str:
        """Audit a specific network"""
        try:
            # Get network info
            net_response = self.session.get(
                f"{self.grid_master}/network",
                params={
                    "network": network_cidr,
                    "network_view": self.network_view,
                    "_return_fields": "network,comment"
                }
            )
            
            if net_response.status_code != 200 or not net_response.json():
                return f"❌ Network {network_cidr} not found"
            
            network_info = net_response.json()[0]
            
            # Get IP addresses
            ip_response = self.session.get(
                f"{self.grid_master}/ipv4address",
                params={
                    "network": network_cidr,
                    "network_view": self.network_view,
                    "_return_fields": "ip_address,status,names,mac_address",
                    "_max_results": 1000
                }
            )
            
            result = f"🔍 **Network Audit for {network_cidr}**\n\n"
            result += f"📋 **Network Information:**\n"
            result += f"• Network: {network_info.get('network')}\n"
            result += f"• Description: {network_info.get('comment', 'No description')}\n\n"
            
            if ip_response.status_code == 200:
                ips = ip_response.json()
                
                used = [ip for ip in ips if ip.get("status") == "USED"]
                unused = [ip for ip in ips if ip.get("status") != "USED"]
                
                result += f"📊 **IP Usage Summary:**\n"
                result += f"• Total IPs tracked: {len(ips)}\n"
                result += f"• Used: {len(used)}\n"
                result += f"• Available: {len(unused)}\n\n"
                
                if used:
                    result += "✅ **Used IP Addresses:**\n"
                    for ip in used[:20]:
                        result += f"• {ip['ip_address']}"
                        if ip.get('names'):
                            result += f" - {', '.join(ip['names'])}"
                        if ip.get('mac_address'):
                            result += f" (MAC: {ip['mac_address']})"
                        result += "\n"
                    if len(used) > 20:
                        result += f"... and {len(used) - 20} more used IPs\n"
            
            return result
            
        except Exception as e:
            return f"❌ Error auditing network: {str(e)}"
    
    def search_ip(self, ip_address: str) -> str:
        """Search for IP information"""
        try:
            ip_response = self.session.get(
                f"{self.grid_master}/ipv4address",
                params={
                    "ip_address": ip_address,
                    "_return_fields": "ip_address,status,names,mac_address,network"
                }
            )
            
            if ip_response.status_code == 200 and ip_response.json():
                ip_info = ip_response.json()[0]
                
                result = f"🔎 **IP Address Information for {ip_address}**\n\n"
                result += f"• Status: {ip_info.get('status', 'Unknown')}\n"
                result += f"• Network: {ip_info.get('network', 'Unknown')}\n"
                
                if ip_info.get('names'):
                    result += f"• DNS Names: {', '.join(ip_info['names'])}\n"
                
                if ip_info.get('mac_address'):
                    result += f"• MAC Address: {ip_info['mac_address']}\n"
                
                return result
            else:
                return f"❌ IP address {ip_address} not found"
                
        except Exception as e:
            return f"❌ Error searching IP: {str(e)}"
    
    def find_available_ips(self, network_cidr: str, count: int = 5) -> str:
        """Find available IPs in a network"""
        try:
            # Get network reference
            net_response = self.session.get(
                f"{self.grid_master}/network",
                params={"network": network_cidr, "network_view": self.network_view}
            )
            
            if net_response.status_code == 200 and net_response.json():
                net_ref = net_response.json()[0]['_ref']
                
                # Get next available IPs
                ip_response = self.session.post(
                    f"{self.grid_master}/{net_ref}",
                    params={"_function": "next_available_ip", "num": count}
                )
                
                if ip_response.status_code == 200:
                    ips = ip_response.json()
                    result = f"✅ **Next {count} Available IPs in {network_cidr}:**\n\n"
                    for i, ip in enumerate(ips['ips'][:count], 1):
                        result += f"{i}. {ip}\n"
                    return result
            
            return f"❌ Network {network_cidr} not found"
            
        except Exception as e:
            return f"❌ Error finding available IPs: {str(e)}"
    
    def list_dns_zones(self) -> str:
        """List DNS zones"""
        try:
            response = self.session.get(
                f"{self.grid_master}/zone_auth",
                params={"_return_fields": "fqdn,comment,view", "_max_results": 50}
            )
            
            if response.status_code == 200:
                zones = response.json()
                
                if not zones:
                    return "No DNS zones found"
                
                fwd_zones = [z for z in zones if not z['fqdn'].endswith('.in-addr.arpa')]
                rev_zones = [z for z in zones if z['fqdn'].endswith('.in-addr.arpa')]
                
                result = "🌐 **DNS Zones in InfoBlox**\n\n"
                
                if fwd_zones:
                    result += "**Forward Zones:**\n"
                    for zone in fwd_zones[:10]:
                        result += f"• {zone['fqdn']}"
                        if zone.get('comment'):
                            result += f" - {zone['comment']}"
                        result += "\n"
                    if len(fwd_zones) > 10:
                        result += f"... and {len(fwd_zones) - 10} more\n"
                
                if rev_zones:
                    result += "\n**Reverse Zones:**\n"
                    for zone in rev_zones[:5]:
                        result += f"• {zone['fqdn']}\n"
                    if len(rev_zones) > 5:
                        result += f"... and {len(rev_zones) - 5} more\n"
                
                return result
            else:
                return f"❌ Error fetching zones: HTTP {response.status_code}"
                
        except Exception as e:
            return f"❌ Error: {str(e)}"
    
    def list_dhcp_ranges(self) -> str:
        """List DHCP ranges"""
        try:
            response = self.session.get(
                f"{self.grid_master}/range",
                params={
                    "_return_fields": "start_addr,end_addr,network,comment",
                    "_max_results": 20,
                    "network_view": self.network_view
                }
            )
            
            if response.status_code == 200:
                ranges = response.json()
                
                if not ranges:
                    return "No DHCP ranges found"
                
                result = "🔄 **DHCP Ranges in InfoBlox**\n\n"
                
                for i, dhcp_range in enumerate(ranges, 1):
                    result += f"**{i}. {dhcp_range['start_addr']} - {dhcp_range['end_addr']}**\n"
                    result += f"   • Network: {dhcp_range.get('network', 'Unknown')}\n"
                    if dhcp_range.get('comment'):
                        result += f"   • Comment: {dhcp_range['comment']}\n"
                    result += "\n"
                
                return result
            else:
                return f"❌ Error fetching DHCP ranges: HTTP {response.status_code}"
                
        except Exception as e:
            return f"❌ Error: {str(e)}"
    
    def get_grid_info(self) -> str:
        """Get grid information"""
        try:
            response = self.session.get(
                f"{self.grid_master}/grid",
                params={"_return_fields": "name,dns_resolver_setting,ntp_setting"}
            )
            
            if response.status_code == 200:
                grid_info = response.json()
                if grid_info:
                    grid = grid_info[0]
                    result = "🌐 **InfoBlox Grid Information**\n\n"
                    result += f"• Grid Name: {grid.get('name', 'Unknown')}\n"
                    result += f"• Grid Master: {self.grid_master}\n"
                    
                    if grid.get('dns_resolver_setting'):
                        resolvers = grid['dns_resolver_setting'].get('resolvers', [])
                        if resolvers:
                            result += f"• DNS Resolvers: {', '.join(resolvers)}\n"
                    
                    return result
            
            return "❌ Could not fetch grid information"
            
        except Exception as e:
            return f"❌ Error: {str(e)}"
    
    def get_help_message(self) -> str:
        """Return help message"""
        return """🚀 **InfoBlox MCP Assistant**

I can help you manage your InfoBlox infrastructure. Try these commands:

**Network Management:**
• `list all networks` - Show all networks
• `audit network 10.0.0.0/24` - Detailed network audit
• `find available IPs in 10.0.0.0/24` - Find free IP addresses

**IP Address Management:**
• `search IP **********` - Get information about an IP
• `show IPs in network 10.0.0.0/24` - List all IPs in a network

**DNS Management:**
• `list DNS zones` - Show all DNS zones
• `show forward zones` - List forward lookup zones
• `show reverse zones` - List reverse lookup zones

**DHCP Management:**
• `list DHCP ranges` - Show all DHCP ranges

**System Information:**
• `test connection` - Verify InfoBlox connectivity
• `show grid info` - Display grid information

Current Configuration:
• Grid Master: """ + self.grid_master + """
• Network View: """ + self.network_view + """

💡 Tip: You can also check all available tools at http://localhost:8000/tools"""
    
    def send_json(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
    
    def log_message(self, format, *args):
        """Log messages"""
        print(f"{datetime.now().strftime('%H:%M:%S')} - {format % args}")

if __name__ == "__main__":
    print("🚀 Starting InfoBlox MCP Server...")
    print(f"Grid Master: {os.getenv('GRID_MASTER_URL', 'Not set')}")
    print(f"Network View: {os.getenv('NETWORK_VIEW', 'default')}")
    
    server = HTTPServer(('0.0.0.0', 8000), InfoBloxMCPServer)
    
    print("\n✅ Server ready!")
    print("View tools at: http://localhost:8000/tools")
    print("Health check: http://localhost:8000/health")
    
    server.serve_forever()
