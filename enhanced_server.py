#!/usr/bin/env python3
"""
Enhanced InfoBlox MCP Server with JSON formatting and Tool Discovery UI
"""
import requests
import json
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib3
import os
import re
from typing import Dict, List, Any, Optional
import logging
from urllib.parse import urlparse, parse_qs

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class EnhancedInfoBloxMCP(BaseHTTPRequestHandler):
    # Class variables for tools
    wapi_tools = {}
    wapi_objects = {}
    tool_categories = {}
    
    def __init__(self, *args, **kwargs):
        self.grid_master = os.getenv("GRID_MASTER_URL", "https://192.168.1.1/wapi/v2.13.1")
        self.username = os.getenv("INFOBLOX_USERNAME", "admin")
        self.password = os.getenv("INFOBLOX_PASSWORD", "infoblox")
        self.network_view = os.getenv("NETWORK_VIEW", "default")
        
        # Create session
        self.session = requests.Session()
        self.session.auth = (self.username, self.password)
        self.session.verify = False
        self.session.headers.update({'Content-Type': 'application/json'})
        
        # Load tools on first init
        if not self.__class__.wapi_tools:
            self.load_tools()
            self.categorize_tools()
        
        super().__init__(*args, **kwargs)
    
    def load_tools(self):
        """Load tool definitions"""
        try:
            # Load tool definitions
            if os.path.exists('infoblox_mcp_tools.json'):
                with open('infoblox_mcp_tools.json', 'r') as f:
                    tools = json.load(f)
                    for tool in tools:
                        self.__class__.wapi_tools[tool['id']] = tool
                logger.info(f"Loaded {len(self.__class__.wapi_tools)} MCP tools")
            
            # Load discovered objects
            if os.path.exists('discovered_wapi_objects.json'):
                with open('discovered_wapi_objects.json', 'r') as f:
                    self.__class__.wapi_objects = json.load(f)
        except Exception as e:
            logger.error(f"Error loading tools: {e}")
    
    def categorize_tools(self):
        """Organize tools into categories"""
        for tool_id, tool in self.__class__.wapi_tools.items():
            category = self.get_category(tool.get('object', ''))
            if category not in self.__class__.tool_categories:
                self.__class__.tool_categories[category] = []
            self.__class__.tool_categories[category].append(tool)
    
    def get_category(self, obj_type: str) -> str:
        """Get category for an object type"""
        if "network" in obj_type and "discovery" not in obj_type:
            return "Network"
        elif "record:" in obj_type:
            return "DNS Records"
        elif "zone" in obj_type:
            return "DNS Zones"
        elif any(x in obj_type for x in ["dhcp", "range", "lease", "fixed"]):
            return "DHCP"
        elif any(x in obj_type for x in ["ipv4", "ipv6"]):
            return "IPAM"
        elif any(x in obj_type for x in ["admin", "saml", "certificate"]):
            return "Security"
        elif "discovery" in obj_type:
            return "Discovery"
        elif any(x in obj_type for x in ["grid", "member"]):
            return "Grid"
        else:
            return "Other"
    
    def do_GET(self):
        """Handle GET requests"""
        path = urlparse(self.path).path
        
        if path == "/health":
            self.send_json({
                "status": "healthy",
                "grid_master": self.grid_master,
                "tools_loaded": len(self.__class__.wapi_tools)
            })
        
        elif path == "/v1/models":
            # This is important for Open WebUI model selection
            self.send_json({
                "data": [{
                    "id": "infoblox-assistant",
                    "name": "InfoBlox Assistant",
                    "description": "InfoBlox WAPI Assistant with 1,300+ tools",
                    "object": "model",
                    "owned_by": "infoblox-wapi",
                    "permission": [],
                    "created": int(datetime.now().timestamp()),
                    "default_prompts": [
                        "browse tools",
                        "search tools: network",
                        "search tools: dns",
                        "test connection",
                        "list all networks"
                    ]
                }]
            })
        
        elif path == "/tools":
            # Return categorized tools
            self.send_json({
                "total": len(self.__class__.wapi_tools),
                "categories": {
                    cat: [
                        {
                            "id": tool["id"],
                            "name": tool.get("name", tool["id"]),
                            "description": tool.get("description", ""),
                            "operation": tool.get("operation", ""),
                            "object": tool.get("object", ""),
                            "parameters": tool.get("parameters", {})
                        } for tool in tools
                    ] for cat, tools in self.__class__.tool_categories.items()
                }
            })
        
        elif path == "/tools/tree":
            # Return tools in tree structure for UI
            self.send_json(self.get_tools_tree())
        
        elif path.startswith("/tools/"):
            # Get specific tool details
            tool_id = path.replace("/tools/", "")
            if tool_id in self.__class__.wapi_tools:
                self.send_json(self.__class__.wapi_tools[tool_id])
            else:
                self.send_404()
        
        elif path == "/ui/tool-browser":
            # Serve the tool browser HTML
            self.send_tool_browser_ui()
        
        elif path == "/v1/prompts" or path == "/prompts":
            # Suggested prompts for Open WebUI
            self.send_json({
                "prompts": [
                    {
                        "id": "browse-tools",
                        "title": "🛠️ Browse InfoBlox Tools",
                        "prompt": "browse tools",
                        "description": "View all 1,345 InfoBlox tools organized by category",
                        "category": "InfoBlox Tools"
                    },
                    {
                        "id": "search-network",
                        "title": "🔍 Search Network Tools",
                        "prompt": "search tools: network",
                        "description": "Find network management tools",
                        "category": "InfoBlox Tools"
                    },
                    {
                        "id": "search-dns",
                        "title": "🌐 Search DNS Tools",
                        "prompt": "search tools: dns",
                        "description": "Find DNS record and zone tools",
                        "category": "InfoBlox Tools"
                    },
                    {
                        "id": "list-networks",
                        "title": "📊 List All Networks",
                        "prompt": "list all networks",
                        "description": "Show all networks in InfoBlox",
                        "category": "InfoBlox Operations"
                    },
                    {
                        "id": "list-networks-json",
                        "title": "📊 List Networks (JSON)",
                        "prompt": "list all networks json",
                        "description": "Show networks in JSON format",
                        "category": "InfoBlox Operations"
                    },
                    {
                        "id": "test-connection",
                        "title": "✅ Test Connection",
                        "prompt": "test connection",
                        "description": "Verify InfoBlox Grid connectivity",
                        "category": "InfoBlox Operations"
                    },
                    {
                        "id": "search-dhcp",
                        "title": "📡 Search DHCP Tools",
                        "prompt": "search tools: dhcp",
                        "description": "Find DHCP management tools",
                        "category": "InfoBlox Tools"
                    },
                    {
                        "id": "create-network",
                        "title": "➕ Create Network Guide",
                        "prompt": "search tools: create network",
                        "description": "Find network creation tools",
                        "category": "InfoBlox Tools"
                    },
                    {
                        "id": "help",
                        "title": "❓ Help",
                        "prompt": "help",
                        "description": "Show available commands and tips",
                        "category": "InfoBlox Help"
                    }
                ]
            })
        
        else:
            self.send_json({"status": "ok", "endpoints": [
                "/health", "/v1/models", "/v1/prompts", "/tools", "/tools/tree", 
                "/tools/<tool_id>", "/ui/tool-browser"
            ]})
    
    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        body = self.rfile.read(content_length).decode('utf-8')
        path = urlparse(self.path).path
        
        if path == "/v1/chat/completions":
            # OpenAI-compatible chat endpoint
            try:
                data = json.loads(body)
                messages = data.get("messages", [])
                
                if messages:
                    user_message = messages[-1].get("content", "")
                    response_content = self.process_query(user_message)
                else:
                    response_content = self.get_initial_suggestions()
                
                response = {
                    "id": "chatcmpl-" + str(int(datetime.now().timestamp())),
                    "object": "chat.completion",
                    "created": int(datetime.now().timestamp()),
                    "model": "infoblox-assistant",
                    "choices": [{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_content
                        },
                        "finish_reason": "stop"
                    }]
                }
                self.send_json(response)
                
            except Exception as e:
                logger.error(f"Error: {e}")
                self.send_json({"error": str(e)})
        
        elif path == "/tools/execute":
            # Execute a tool
            try:
                data = json.loads(body)
                tool_id = data.get("tool_id")
                params = data.get("parameters", {})
                
                result = self.execute_tool(tool_id, params)
                self.send_json(result)
                
            except Exception as e:
                self.send_json({"error": str(e)})
        
        else:
            self.send_404()
    
    def get_tools_tree(self):
        """Get tools organized in tree structure"""
        tree = {
            "name": "InfoBlox Tools",
            "children": []
        }
        
        for category, tools in self.__class__.tool_categories.items():
            cat_node = {
                "name": category,
                "type": "category",
                "count": len(tools),
                "children": []
            }
            
            # Group by operation
            operations = {}
            for tool in tools:
                op = tool.get("operation", "other")
                if op not in operations:
                    operations[op] = []
                operations[op].append(tool)
            
            for op, op_tools in operations.items():
                op_node = {
                    "name": op.capitalize(),
                    "type": "operation",
                    "count": len(op_tools),
                    "children": [
                        {
                            "id": t["id"],
                            "name": t.get("name", t["id"]),
                            "type": "tool",
                            "description": t.get("description", ""),
                            "object": t.get("object", ""),
                            "parameters": self.get_tool_parameters(t)
                        } for t in op_tools
                    ]
                }
                cat_node["children"].append(op_node)
            
            tree["children"].append(cat_node)
        
        return tree
    
    def get_tool_parameters(self, tool):
        """Extract parameters for a tool"""
        params = []
        
        # Common parameters based on operation
        operation = tool.get("operation", "")
        object_type = tool.get("object", "")
        
        if operation == "list":
            params.extend([
                {
                    "name": "_max_results",
                    "type": "number",
                    "default": 100,
                    "description": "Maximum number of results",
                    "required": False
                },
                {
                    "name": "_return_fields",
                    "type": "text",
                    "description": "Comma-separated list of fields to return",
                    "required": False
                }
            ])
            
            # Add object-specific filters
            if "network" in object_type:
                params.append({
                    "name": "network",
                    "type": "text",
                    "description": "Network CIDR (e.g., 10.0.0.0/24)",
                    "required": False
                })
                params.append({
                    "name": "network_view",
                    "type": "text",
                    "default": self.network_view,
                    "description": "Network view",
                    "required": False
                })
            elif "record:" in object_type:
                params.append({
                    "name": "zone",
                    "type": "text",
                    "description": "DNS zone",
                    "required": False
                })
                params.append({
                    "name": "name",
                    "type": "text",
                    "description": "Record name",
                    "required": False
                })
        
        elif operation == "create":
            if "network" in object_type:
                params.extend([
                    {
                        "name": "network",
                        "type": "text",
                        "description": "Network CIDR (e.g., 10.0.0.0/24)",
                        "required": True
                    },
                    {
                        "name": "comment",
                        "type": "text",
                        "description": "Network description",
                        "required": False
                    }
                ])
            elif "record:a" in object_type:
                params.extend([
                    {
                        "name": "name",
                        "type": "text",
                        "description": "FQDN (e.g., host.example.com)",
                        "required": True
                    },
                    {
                        "name": "ipv4addr",
                        "type": "text",
                        "description": "IPv4 address",
                        "required": True
                    }
                ])
        
        elif operation == "get" or operation == "update" or operation == "delete":
            params.append({
                "name": "_ref",
                "type": "text",
                "description": "Object reference (from list operation)",
                "required": True
            })
        
        return params
    
    def send_tool_browser_ui(self):
        """Send the tool browser UI HTML"""
        html = """
<!DOCTYPE html>
<html>
<head>
    <title>InfoBlox Tool Browser</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 20px;
            height: calc(100vh - 40px);
        }
        .tree-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            overflow-y: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .tool-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            overflow-y: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .tree-node {
            margin: 2px 0;
            user-select: none;
        }
        .tree-node-content {
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .tree-node-content:hover {
            background: #f0f0f0;
        }
        .tree-node-content.selected {
            background: #e3f2fd;
            color: #1976d2;
        }
        .tree-children {
            margin-left: 20px;
            display: none;
        }
        .tree-node.expanded > .tree-children {
            display: block;
        }
        .tree-icon {
            width: 16px;
            text-align: center;
        }
        .badge {
            background: #e0e0e0;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: auto;
        }
        .tool-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .tool-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 10px 0;
        }
        .tool-meta {
            display: flex;
            gap: 20px;
            color: #666;
            font-size: 14px;
        }
        .parameter {
            margin-bottom: 20px;
        }
        .parameter label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
        }
        .parameter input, .parameter select, .parameter textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .parameter textarea {
            min-height: 80px;
            resize: vertical;
        }
        .parameter .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        .required::after {
            content: " *";
            color: #f44336;
        }
        .execute-btn {
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
        }
        .execute-btn:hover {
            background: #1565c0;
        }
        .execute-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            background: #f5f5f5;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .result.error {
            background: #ffebee;
            color: #c62828;
        }
        .result.success {
            background: #e8f5e9;
            color: #2e7d32;
        }
        .search-box {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .no-tool {
            text-align: center;
            color: #666;
            padding: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="tree-panel">
            <h2 style="margin-top: 0;">Tool Browser</h2>
            <input type="text" class="search-box" placeholder="Search tools..." id="searchBox">
            <div id="treeContainer">Loading tools...</div>
        </div>
        <div class="tool-panel">
            <div id="toolContainer">
                <div class="no-tool">
                    <h3>Select a tool from the tree</h3>
                    <p>Browse and execute any of the 1,300+ InfoBlox WAPI tools</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let toolsData = null;
        let selectedTool = null;

        async function loadTools() {
            try {
                const response = await fetch('/tools/tree');
                toolsData = await response.json();
                renderTree(toolsData);
                
                // Setup search
                document.getElementById('searchBox').addEventListener('input', (e) => {
                    filterTree(e.target.value);
                });
            } catch (error) {
                console.error('Error loading tools:', error);
                document.getElementById('treeContainer').innerHTML = 'Error loading tools';
            }
        }

        function renderTree(node, container = document.getElementById('treeContainer'), level = 0) {
            if (level === 0) {
                container.innerHTML = '';
            }

            if (node.children) {
                node.children.forEach(child => {
                    const nodeDiv = document.createElement('div');
                    nodeDiv.className = 'tree-node';
                    
                    const contentDiv = document.createElement('div');
                    contentDiv.className = 'tree-node-content';
                    
                    const icon = document.createElement('span');
                    icon.className = 'tree-icon';
                    
                    if (child.children) {
                        icon.textContent = '▶';
                        contentDiv.onclick = () => toggleNode(nodeDiv, icon);
                    } else {
                        icon.textContent = '•';
                        contentDiv.onclick = () => selectTool(child, contentDiv);
                    }
                    
                    const label = document.createElement('span');
                    label.textContent = child.name;
                    
                    contentDiv.appendChild(icon);
                    contentDiv.appendChild(label);
                    
                    if (child.count !== undefined) {
                        const badge = document.createElement('span');
                        badge.className = 'badge';
                        badge.textContent = child.count;
                        contentDiv.appendChild(badge);
                    }
                    
                    nodeDiv.appendChild(contentDiv);
                    
                    if (child.children) {
                        const childrenDiv = document.createElement('div');
                        childrenDiv.className = 'tree-children';
                        renderTree(child, childrenDiv, level + 1);
                        nodeDiv.appendChild(childrenDiv);
                    }
                    
                    container.appendChild(nodeDiv);
                });
            }
        }

        function toggleNode(node, icon) {
            node.classList.toggle('expanded');
            icon.textContent = node.classList.contains('expanded') ? '▼' : '▶';
        }

        function selectTool(tool, element) {
            // Update selection
            document.querySelectorAll('.tree-node-content').forEach(el => {
                el.classList.remove('selected');
            });
            element.classList.add('selected');
            
            selectedTool = tool;
            displayTool(tool);
        }

        function displayTool(tool) {
            const container = document.getElementById('toolContainer');
            
            let html = `
                <div class="tool-header">
                    <h1 class="tool-title">${tool.name}</h1>
                    <div class="tool-meta">
                        <span><strong>ID:</strong> ${tool.id}</span>
                        <span><strong>Object:</strong> ${tool.object}</span>
                    </div>
                    <p>${tool.description || 'No description available'}</p>
                </div>
                
                <form id="toolForm">
                    <h3>Parameters</h3>
            `;
            
            if (tool.parameters && tool.parameters.length > 0) {
                tool.parameters.forEach(param => {
                    html += `
                        <div class="parameter">
                            <label class="${param.required ? 'required' : ''}">${param.name}</label>
                            ${getParameterInput(param)}
                            ${param.description ? `<div class="help-text">${param.description}</div>` : ''}
                        </div>
                    `;
                });
            } else {
                html += '<p>No parameters required</p>';
            }
            
            html += `
                <button type="submit" class="execute-btn">Execute Tool</button>
                <div id="resultContainer"></div>
                </form>
            `;
            
            container.innerHTML = html;
            
            // Setup form submission
            document.getElementById('toolForm').onsubmit = (e) => {
                e.preventDefault();
                executeTool();
            };
        }

        function getParameterInput(param) {
            const name = param.name;
            const value = param.default || '';
            
            switch (param.type) {
                case 'number':
                    return `<input type="number" name="${name}" value="${value}" ${param.required ? 'required' : ''}>`;
                case 'boolean':
                    return `
                        <select name="${name}" ${param.required ? 'required' : ''}>
                            <option value="true">True</option>
                            <option value="false">False</option>
                        </select>
                    `;
                case 'select':
                    return `
                        <select name="${name}" ${param.required ? 'required' : ''}>
                            ${param.options.map(opt => `<option value="${opt}">${opt}</option>`).join('')}
                        </select>
                    `;
                case 'textarea':
                    return `<textarea name="${name}" ${param.required ? 'required' : ''}>${value}</textarea>`;
                default:
                    return `<input type="text" name="${name}" value="${value}" ${param.required ? 'required' : ''}>`;
            }
        }

        async function executeTool() {
            const form = document.getElementById('toolForm');
            const formData = new FormData(form);
            const parameters = {};
            
            for (let [key, value] of formData.entries()) {
                if (value) {
                    parameters[key] = value;
                }
            }
            
            const resultContainer = document.getElementById('resultContainer');
            resultContainer.innerHTML = '<div class="result">Executing...</div>';
            
            try {
                const response = await fetch('/tools/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tool_id: selectedTool.id,
                        parameters: parameters
                    })
                });
                
                const result = await response.json();
                
                if (result.error) {
                    resultContainer.innerHTML = `<div class="result error">Error: ${result.error}</div>`;
                } else {
                    resultContainer.innerHTML = `<div class="result success">${JSON.stringify(result, null, 2)}</div>`;
                }
            } catch (error) {
                resultContainer.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }

        function filterTree(searchTerm) {
            const term = searchTerm.toLowerCase();
            const nodes = document.querySelectorAll('.tree-node');
            
            nodes.forEach(node => {
                const text = node.textContent.toLowerCase();
                if (text.includes(term)) {
                    node.style.display = 'block';
                    // Expand parent nodes
                    let parent = node.parentElement;
                    while (parent && parent.classList.contains('tree-children')) {
                        parent.parentElement.classList.add('expanded');
                        parent = parent.parentElement.parentElement;
                    }
                } else {
                    node.style.display = 'none';
                }
            });
            
            // Update icons for expanded nodes
            document.querySelectorAll('.tree-node.expanded').forEach(node => {
                const icon = node.querySelector('.tree-icon');
                if (icon && icon.textContent !== '•') {
                    icon.textContent = '▼';
                }
            });
        }

        // Load tools on page load
        loadTools();
    </script>
</body>
</html>
"""
        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.send_header('Content-Length', str(len(html)))
        self.end_headers()
        self.wfile.write(html.encode())
    
    def execute_tool(self, tool_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool"""
        if tool_id not in self.__class__.wapi_tools:
            return {"error": f"Unknown tool: {tool_id}"}
        
        tool = self.__class__.wapi_tools[tool_id]
        obj_type = tool["object"]
        operation = tool["operation"]
        
        try:
            if operation == "list":
                # List/search objects
                api_params = {
                    "_max_results": params.get("_max_results", 100)
                }
                
                # Add return fields if specified
                if params.get("_return_fields"):
                    api_params["_return_fields"] = params["_return_fields"]
                
                # Add other parameters
                for key, value in params.items():
                    if key not in ["_max_results", "_return_fields"] and value:
                        api_params[key] = value
                
                response = self.session.get(
                    f"{self.grid_master}/{obj_type}",
                    params=api_params
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "success": True,
                        "count": len(data),
                        "data": data
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif operation == "create":
                # Create object
                response = self.session.post(
                    f"{self.grid_master}/{obj_type}",
                    json=params
                )
                
                if response.status_code in [200, 201]:
                    return {
                        "success": True,
                        "ref": response.json(),
                        "message": f"Created {obj_type}"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif operation == "get":
                # Get specific object
                ref = params.get("_ref", "")
                if not ref:
                    return {"error": "Missing _ref parameter"}
                
                response = self.session.get(f"{self.grid_master}/{ref}")
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "data": response.json()
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif operation == "update":
                # Update object
                ref = params.pop("_ref", "")
                if not ref:
                    return {"error": "Missing _ref parameter"}
                
                response = self.session.put(
                    f"{self.grid_master}/{ref}",
                    json=params
                )
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "ref": response.json(),
                        "message": f"Updated {obj_type}"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            elif operation == "delete":
                # Delete object
                ref = params.get("_ref", "")
                if not ref:
                    return {"error": "Missing _ref parameter"}
                
                response = self.session.delete(f"{self.grid_master}/{ref}")
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": f"Deleted {obj_type}"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
            
            else:
                return {"error": f"Unknown operation: {operation}"}
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_initial_suggestions(self):
        """Return initial greeting with suggestions"""
        return f"""👋 **Welcome to InfoBlox Assistant!**

I can help you manage your InfoBlox infrastructure with **{len(self.__class__.wapi_tools)} tools** at your disposal.

**🚀 Quick Actions - Just click or type:**

**Network Management:**
• `list all networks` - View all your networks
• `create network 10.x.x.x/24` - Create a new network
• `find IP 10.x.x.x` - Search for an IP address
• `check network usage 10.x.x.x/24` - See network utilization

**DNS Management:**
• `list dns zones` - View all DNS zones
• `show A records for example.com` - List A records
• `create DNS record` - Add a new DNS record
• `find record hostname` - Search DNS records

**DHCP Management:**
• `show dhcp ranges` - List DHCP ranges
• `list active leases` - View current DHCP leases
• `create dhcp reservation` - Add a fixed address

**Explore Tools:**
• `browse tools` - See all {len(self.__class__.wapi_tools)} available tools
• `search tools: <keyword>` - Find specific tools
• `help` - Show detailed help

**💡 Tips:**
- I understand natural language! Just tell me what you need
- Add `json` to any command for JSON output
- Type partial commands and I'll help complete them

**Current Status:**
• Grid Master: {self.grid_master}
• Network View: {self.network_view}
• Connection: ✅ Active

What would you like to do first?"""
    
    def process_query(self, query):
        """Process user query with intelligent suggestions"""
        # Check if JSON format is requested
        output_json = "json" in query.lower() or "format=json" in query.lower()
        
        # Clean query for processing
        clean_query = query.lower().replace("json", "").replace("format=", "").strip()
        
        # Provide suggestions for incomplete queries
        if len(clean_query) < 3:
            return self.get_initial_suggestions()
        
        # Smart suggestions based on keywords
        if clean_query in ["help", "?", "what can you do", "how do i", "show me"]:
            return self.get_contextual_help()
        
        # Network-related queries
        if any(word in clean_query for word in ["network", "subnet", "cidr"]) and not any(word in clean_query for word in ["list", "create", "search", "tool"]):
            return self.suggest_network_operations()
        
        # DNS-related queries
        if any(word in clean_query for word in ["dns", "domain", "record", "zone"]) and not any(word in clean_query for word in ["list", "create", "search", "tool"]):
            return self.suggest_dns_operations()
        
        # DHCP-related queries
        if any(word in clean_query for word in ["dhcp", "lease", "reservation"]) and not any(word in clean_query for word in ["list", "create", "search", "tool"]):
            return self.suggest_dhcp_operations()
        
        # Test connection
        if any(word in clean_query for word in ["test", "connection", "verify", "check connection"]):
            return self.test_connection_json() if output_json else self.test_connection()
        
        # LIST ALL NETWORKS
        elif any(phrase in clean_query for phrase in [
            "list all network", "list network", "show all network", 
            "all network", "list my network", "show network"
        ]):
            return self.list_all_networks_json() if output_json else self.list_all_networks()
        
        # Tool browser - return embedded HTML
        elif "tool browser" in clean_query or "browse tools" in clean_query:
            return self.get_embedded_tool_browser()
        
        # Execute tool command
        elif clean_query.startswith("execute tool:"):
            try:
                # Parse tool execution request
                tool_data = json.loads(query[13:])
                result = self.execute_tool(tool_data.get("tool_id"), tool_data.get("parameters", {}))
                return self.format_tool_result(result)
            except Exception as e:
                return f"❌ Error executing tool: {str(e)}"
        
        # Search tools
        elif clean_query.startswith("search tools:") or "find tool" in clean_query:
            if clean_query.startswith("search tools:"):
                search_term = query[13:].strip()
            else:
                search_term = clean_query.replace("find tool", "").strip()
            return self.search_and_display_tools(search_term)
        
        # Natural language queries
        elif any(word in clean_query for word in ["create", "add", "new"]):
            return self.suggest_creation_tools(clean_query)
        elif any(word in clean_query for word in ["delete", "remove"]):
            return self.suggest_deletion_tools(clean_query)
        elif any(word in clean_query for word in ["list", "show", "get", "find"]):
            return self.suggest_listing_tools(clean_query)
        
        # Default help
        else:
            return self.get_contextual_help_with_suggestions(query)
    
    def test_connection(self):
        """Test connection - existing method"""
        try:
            response = self.session.get(
                f"{self.grid_master}/grid",
                params={"_return_fields": "name"}
            )
            
            if response.status_code == 200:
                grid_info = response.json()
                if isinstance(grid_info, list) and grid_info:
                    grid_data = grid_info[0]
                    return f"""✅ **Successfully connected to InfoBlox!**

**Grid Information:**
• Grid Name: {grid_data.get('name', 'Unknown')}
• Grid Master: {self.grid_master}
• API Version: {self.grid_master.split('/')[-1]}
• Network View: {self.network_view}
• Authentication: Successful
• Tools Available: {len(self.__class__.wapi_tools)}

Connection test passed!"""
            else:
                return f"❌ Connection failed: HTTP {response.status_code}"
        except Exception as e:
            return f"❌ Connection error: {str(e)}"
    
    def test_connection_json(self):
        """Test connection with JSON output"""
        try:
            response = self.session.get(
                f"{self.grid_master}/grid",
                params={"_return_fields": "name"}
            )
            
            if response.status_code == 200:
                grid_info = response.json()
                if isinstance(grid_info, list) and grid_info:
                    grid_data = grid_info[0]
                    return json.dumps({
                        "status": "success",
                        "connection": "established",
                        "grid_info": {
                            "name": grid_data.get('name', 'Unknown'),
                            "master": self.grid_master,
                            "api_version": self.grid_master.split('/')[-1],
                            "network_view": self.network_view,
                            "tools_available": len(self.__class__.wapi_tools)
                        }
                    }, indent=2)
            else:
                return json.dumps({
                    "status": "error",
                    "code": response.status_code
                }, indent=2)
                
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)
    
    def list_all_networks(self):
        """List networks - existing method"""
        try:
            params = {
                "_max_results": 25,
                "_return_fields": "network,comment,network_view,members",
                "network_view": self.network_view
            }
            
            response = self.session.get(f"{self.grid_master}/network", params=params)
            
            if response.status_code == 200:
                networks = response.json()
                
                if not networks:
                    return f"No networks found in view '{self.network_view}'"
                
                result = f"📊 **Networks in InfoBlox** (View: {self.network_view}):\n\n"
                
                for i, net in enumerate(networks, 1):
                    network = net.get('network', 'Unknown')
                    comment = net.get('comment', 'No description')
                    
                    result += f"**{i}. {network}**\n"
                    result += f"   • Description: {comment}\n\n"
                
                if len(networks) == 25:
                    result += f"... showing first 25 networks"
                
                return result
            else:
                return f"❌ Error: HTTP {response.status_code}"
                
        except Exception as e:
            return f"❌ Error: {str(e)}"
    
    def list_all_networks_json(self):
        """List networks with JSON output"""
        try:
            params = {
                "_max_results": 100,
                "_return_fields": "network,comment,network_view,members,extattrs",
                "network_view": self.network_view
            }
            
            response = self.session.get(f"{self.grid_master}/network", params=params)
            
            if response.status_code == 200:
                networks = response.json()
                
                # Format each network
                formatted_networks = []
                for i, net in enumerate(networks, 1):
                    network_obj = {
                        "number": i,
                        "network": net.get('network', 'Unknown'),
                        "comment": net.get('comment', 'No description'),
                        "network_view": net.get('network_view', self.network_view)
                    }
                    
                    # Parse extended attributes
                    extattrs = net.get('extattrs', {})
                    parsed_extattrs = {}
                    
                    # Parse AWS VPC info from comment
                    if 'comment' in net:
                        vpc_match = re.search(r'vpc-([a-f0-9]+)\s*\(([^)]+)\)\s*on\s*([\d-]+)', net['comment'])
                        if vpc_match:
                            parsed_extattrs['vpc_id'] = f"vpc-{vpc_match.group(1)}"
                            parsed_extattrs['vpc_name'] = vpc_match.group(2)
                            parsed_extattrs['import_date'] = vpc_match.group(3)
                            
                            # Extract region
                            region_match = re.search(r'(us-east-1|us-west-2|eu-west-1|ap-southeast-1)', vpc_match.group(2))
                            if region_match:
                                parsed_extattrs['region'] = region_match.group(1)
                    
                    # Add direct extended attributes
                    for key, value in extattrs.items():
                        if isinstance(value, dict) and 'value' in value:
                            parsed_extattrs[key.lower()] = value['value']
                        else:
                            parsed_extattrs[key.lower()] = value
                    
                    if parsed_extattrs:
                        network_obj['extended_attributes'] = parsed_extattrs
                    
                    formatted_networks.append(network_obj)
                
                return json.dumps({
                    "status": "success",
                    "network_view": self.network_view,
                    "total_networks": len(formatted_networks),
                    "networks": formatted_networks
                }, indent=2)
                
            else:
                return json.dumps({
                    "status": "error",
                    "code": response.status_code,
                    "message": response.text[:200]
                }, indent=2)
                
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)
    
    def get_help_message(self):
        """Return help message"""
        return f"""🎯 **InfoBlox Assistant with Embedded Tool Browser**

**Available Commands:**
• `browse tools` - Open embedded tool browser
• `search tools: <term>` - Search for specific tools
• `execute tool: {{"tool_id": "...", "parameters": {{...}}}}` - Execute a tool
• `list all networks [json]` - List networks
• `test connection [json]` - Test connectivity

**🛠️ Tool Browser:**
Type "browse tools" to see an interactive tool browser right here in the chat!

**Features:**
• Browse {len(self.__class__.wapi_tools)} tools by category
• Search and filter tools
• See tool parameters
• Execute tools directly

**Quick Examples:**
• `search tools: create network` - Find network creation tools
• `search tools: dns record` - Find DNS record tools
• `search tools: dhcp` - Find DHCP tools

Current configuration:
• Grid Master: {self.grid_master}
• Network View: {self.network_view}
• Status: Connected ✅"""
    
    def get_initial_suggestions(self):
        """Return initial suggestions for Open WebUI"""
        return """👋 **Welcome to InfoBlox Assistant!**

I can help you manage your InfoBlox infrastructure with over 1,345 tools.

**🚀 Quick Start Commands:**

• **`browse tools`** - View all tools organized by category
• **`search tools: network`** - Find network management tools
• **`search tools: dns`** - Find DNS tools
• **`test connection`** - Verify InfoBlox connectivity
• **`list all networks`** - Show your network inventory

**💡 Try these popular operations:**

• **Networks:** `search tools: create network`
• **DNS Records:** `search tools: a record`
• **DHCP:** `search tools: dhcp range`
• **IP Search:** `search tools: ip address`

**📖 Tips:**
- Add `json` to any command for JSON output
- Copy tool examples and modify parameters
- Type `help` for detailed instructions

What would you like to do today?"""
    
    def get_embedded_tool_browser(self):
        """Return embedded tool browser for chat interface"""
        categories = {}
        for tool_id, tool in self.__class__.wapi_tools.items():
            category = self.get_category(tool.get('object', ''))
            if category not in categories:
                categories[category] = []
            categories[category].append(tool)
        
        # Build a text-based tree that's easy to navigate
        response = "🛠️ **InfoBlox Tool Browser**\n\n"
        response += f"Browse {len(self.__class__.wapi_tools)} tools organized by category.\n"
        response += "To search for specific tools, use: `search tools: <term>`\n\n"
        
        # Show categories with counts
        response += "**Categories:**\n"
        for category, tools in sorted(categories.items()):
            response += f"• **{category}** ({len(tools)} tools)\n"
        
        response += "\n**How to use:**\n"
        response += "1. Search for tools: `search tools: network`\n"
        response += "2. Execute a tool: `execute tool: {\"tool_id\": \"list_network\", \"parameters\": {}}`\n"
        response += "\n**Popular Tools:**\n"
        
        # Show some popular tools
        popular_tools = [
            ("list_network", "List all networks"),
            ("create_network", "Create a new network"),
            ("list_record_a", "List A records"),
            ("list_zone_auth", "List DNS zones"),
            ("list_range", "List DHCP ranges"),
            ("list_lease", "List DHCP leases")
        ]
        
        for tool_id, desc in popular_tools:
            if tool_id in self.__class__.wapi_tools:
                tool = self.__class__.wapi_tools[tool_id]
                response += f"\n**{tool_id}**\n"
                response += f"• Description: {desc}\n"
                response += f"• Object: {tool.get('object', 'N/A')}\n"
                response += f"• Example: `execute tool: {{\"tool_id\": \"{tool_id}\", \"parameters\": {{}}}}`\n"
        
        return response
    
    def search_and_display_tools(self, search_term):
        """Search and display tools matching the search term"""
        search_lower = search_term.lower()
        matching_tools = []
        
        # Search through all tools
        for tool_id, tool in self.__class__.wapi_tools.items():
            if any([
                search_lower in tool_id.lower(),
                search_lower in tool.get('description', '').lower(),
                search_lower in tool.get('object', '').lower(),
                search_lower in tool.get('name', '').lower()
            ]):
                matching_tools.append((tool_id, tool))
        
        if not matching_tools:
            return f"❌ No tools found matching '{search_term}'\n\nTry broader terms like: network, dns, dhcp, create, list"
        
        # Sort by relevance (ID match first, then operation type)
        matching_tools.sort(key=lambda x: (
            search_lower not in x[0].lower(),  # ID matches first
            x[1].get('operation') != 'list'     # List operations first
        ))
        
        response = f"🔍 **Found {len(matching_tools)} tools matching '{search_term}'**\n\n"
        
        # Group by operation for better display
        by_operation = {}
        for tool_id, tool in matching_tools[:20]:  # Show max 20
            op = tool.get('operation', 'other')
            if op not in by_operation:
                by_operation[op] = []
            by_operation[op].append((tool_id, tool))
        
        for operation, tools in by_operation.items():
            response += f"**{operation.upper()} Operations:**\n"
            
            for tool_id, tool in tools:
                response += f"\n**{tool_id}**\n"
                response += f"• {tool.get('description', 'No description')}\n"
                response += f"• Object: {tool.get('object', 'N/A')}\n"
                
                # Show parameters
                params = self.get_tool_parameters(tool)
                if params:
                    response += "• Parameters:\n"
                    for param in params[:3]:  # Show first 3 params
                        req = " (required)" if param.get('required') else ""
                        response += f"  - {param['name']}: {param.get('description', 'N/A')}{req}\n"
                    if len(params) > 3:
                        response += f"  ... and {len(params) - 3} more\n"
                
                # Show example
                example_params = {}
                for param in params:
                    if param.get('required'):
                        if param['name'] == 'network':
                            example_params['network'] = '10.0.0.0/24'
                        elif param['name'] == '_ref':
                            example_params['_ref'] = '<object_reference>'
                        elif param['name'] == 'name':
                            example_params['name'] = 'example.com'
                        else:
                            example_params[param['name']] = f'<{param["name"]}>'
                
                response += f"• Example: `execute tool: {{\"tool_id\": \"{tool_id}\", \"parameters\": {json.dumps(example_params)}}}`\n"
        
        if len(matching_tools) > 20:
            response += f"\n... showing 20 of {len(matching_tools)} matches. Try a more specific search term."
        
        return response
    
    def format_tool_result(self, result):
        """Format tool execution result for display"""
        if result.get("success"):
            response = "✅ **Tool executed successfully!**\n\n"
            
            data = result.get("data", [])
            if isinstance(data, list):
                response += f"**Found {len(data)} results**\n\n"
                
                # Show first few results
                for i, item in enumerate(data[:5], 1):
                    if isinstance(item, dict):
                        # Format based on common fields
                        if 'network' in item:
                            response += f"{i}. **Network: {item['network']}**\n"
                            if 'comment' in item:
                                response += f"   • Comment: {item['comment']}\n"
                        elif 'name' in item:
                            response += f"{i}. **{item['name']}**\n"
                        elif 'fqdn' in item:
                            response += f"{i}. **{item['fqdn']}**\n"
                        else:
                            # Generic display
                            response += f"{i}. {json.dumps(item, indent=2)}\n"
                        response += "\n"
                
                if len(data) > 5:
                    response += f"... and {len(data) - 5} more results\n"
                    response += "\n💡 Add `json` to your query to see full JSON output"
            else:
                # Non-list result
                response += "**Result:**\n"
                response += f"```json\n{json.dumps(data, indent=2)}\n```"
            
            return response
        else:
            return f"❌ **Tool execution failed**\n\n**Error:** {result.get('error', 'Unknown error')}"
    
    def send_json(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
    
    def send_404(self):
        """Send 404 response"""
        self.send_response(404)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({"error": "Not found"}).encode())
    
    def log_message(self, format, *args):
        """Log messages"""
        logger.info(f"{format % args}")

if __name__ == "__main__":
    print(f"🚀 Starting Enhanced InfoBlox MCP Server...")
    print(f"Grid Master: {os.getenv('GRID_MASTER_URL', 'Not set')}")
    print(f"Network View: {os.getenv('NETWORK_VIEW', 'default')}")
    
    server = HTTPServer(('0.0.0.0', 8000), EnhancedInfoBloxMCP)
    
    print(f"\n✅ Server ready!")
    print("\n📍 Endpoints:")
    print("• Tool Browser UI: http://localhost:8000/ui/tool-browser")
    print("• Tools API: http://localhost:8000/tools")
    print("• Chat: http://localhost:8000/v1/chat/completions")
    print("• Health: http://localhost:8000/health")
    
    server.serve_forever()
