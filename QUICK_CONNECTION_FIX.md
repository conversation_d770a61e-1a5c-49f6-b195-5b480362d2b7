# 🔧 Quick Fix: InfoBlox MCP Connection Issues

## The Problem
You're getting "cannot access http://host.docker.internal:8000/v1" when trying to add the InfoBlox model to Open WebUI.

## The Solution
The issue is that `host.docker.internal` doesn't work in all Docker environments. Here are the correct URLs to use:

### ✅ Method 1: Container Network (Recommended)
Since both services are in the same Docker Compose network, use the container name:

**URL to use in Open WebUI**: `http://infoblox-mcp:8000/v1`

### ✅ Method 2: Localhost (Alternative)
If Method 1 doesn't work, use localhost:

**URL to use in Open WebUI**: `http://localhost:8000/v1`

### ✅ Method 3: Docker Bridge IP (Linux)
On Linux systems, you might need the Docker bridge IP:

**URL to use in Open WebUI**: `http://**********:8000/v1`

## 📋 Step-by-Step Fix

### 1. First, make sure services are running:
```bash
docker-compose ps
```

If not running:
```bash
docker-compose up -d
```

### 2. Test which URL works:
```bash
# Run the connection fix script
./fix-connection.sh
```

This script will test all connection methods and tell you which URL to use.

### 3. Configure Open WebUI:

1. **Open your browser** to http://localhost:3000
2. **Go to Settings** → **Connections**
3. **Click "Add Connection"** or **"+ New Connection"**
4. **Select "OpenAI API"**
5. **Fill in these values**:
   - **Name**: `InfoBlox Assistant`
   - **API Base URL**: `http://infoblox-mcp:8000/v1` (try this first)
   - **API Key**: `dummy-key`
6. **Click "Save"**
7. **Refresh your browser** (F5 or Cmd+R)
8. **Select "infoblox-assistant"** from the model dropdown

### 4. If Method 1 doesn't work, try Method 2:
- Change **API Base URL** to: `http://localhost:8000/v1`
- Save and refresh

### 5. If both fail, try Method 3 (Linux only):
- Change **API Base URL** to: `http://**********:8000/v1`
- Save and refresh

## 🧪 Quick Test

After configuring, test the connection by typing in the chat:
```
test connection
```

You should see a response from the InfoBlox Assistant.

## 🔍 Troubleshooting

### If you still can't connect:

1. **Check InfoBlox MCP is running**:
   ```bash
   curl http://localhost:8000/health
   ```
   Should return: `{"status": "healthy", ...}`

2. **Check Open WebUI can reach InfoBlox MCP**:
   ```bash
   docker-compose exec open-webui curl http://infoblox-mcp:8000/health
   ```

3. **Check the logs**:
   ```bash
   docker-compose logs infoblox-mcp
   docker-compose logs open-webui
   ```

4. **Restart everything**:
   ```bash
   docker-compose down
   docker-compose up -d
   ```

### Common Issues:

- **Port 8000 already in use**: Stop other services using port 8000
- **Firewall blocking**: Allow ports 3000 and 8000
- **Docker network issues**: Restart Docker service
- **Browser cache**: Clear browser cache and cookies

## 🎯 Expected Result

Once configured correctly, you should:
- ✅ See "InfoBlox Assistant" in the model dropdown
- ✅ Be able to chat with natural language commands
- ✅ Get responses about InfoBlox infrastructure
- ✅ Access 1,300+ InfoBlox tools through chat

## 📞 Still Need Help?

If you're still having issues:

1. **Run the diagnostic script**:
   ```bash
   ./fix-connection.sh
   ```

2. **Check the full logs**:
   ```bash
   docker-compose logs -f
   ```

3. **Verify your setup**:
   ```bash
   ./test-integration.sh
   ```

The most common solution is using `http://infoblox-mcp:8000/v1` instead of `http://host.docker.internal:8000/v1`.
