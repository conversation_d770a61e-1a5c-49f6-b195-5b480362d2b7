#!/bin/bash

echo "🔍 Verifying InfoBlox MCP Integration..."
echo "========================================="
echo ""

# Check if services are running
echo "1️⃣ Checking Docker containers..."
if docker ps | grep -q infoblox-mcp; then
    echo "✅ InfoBlox MCP is running"
else
    echo "❌ InfoBlox MCP is not running"
    echo "   Run: docker start infoblox-mcp"
fi

if docker ps | grep -q open-webui; then
    echo "✅ Open WebUI is running"
else
    echo "❌ Open WebUI is not running"
    echo "   Run: docker start open-webui"
fi
echo ""

# Test InfoBlox endpoints
echo "2️⃣ Testing InfoBlox MCP endpoints..."
echo ""
echo "Testing /v1/models endpoint:"
if curl -s http://localhost:8000/v1/models | grep -q "infoblox-assistant"; then
    echo "✅ Models endpoint is working"
    curl -s http://localhost:8000/v1/models | python3 -m json.tool | grep -A2 -B2 "infoblox-assistant"
else
    echo "❌ Models endpoint failed"
fi
echo ""

echo "Testing /v1/openapi.json endpoint:"
if curl -s http://localhost:8000/v1/openapi.json | grep -q "InfoBlox Assistant API"; then
    echo "✅ OpenAPI endpoint is working"
else
    echo "❌ OpenAPI endpoint failed"
fi
echo ""

echo "Testing chat completion:"
RESPONSE=$(curl -s -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model": "infoblox-assistant", "messages": [{"role": "user", "content": "test"}]}')

if echo "$RESPONSE" | grep -q "Connection Successful"; then
    echo "✅ Chat completion is working"
    echo "   Response: Connected to InfoBlox grid successfully!"
else
    echo "❌ Chat completion failed"
fi
echo ""

# Test from Open WebUI perspective
echo "3️⃣ Testing connectivity from Open WebUI container..."
if docker exec open-webui curl -s http://host.docker.internal:8000/v1/models 2>/dev/null | grep -q "infoblox-assistant"; then
    echo "✅ Open WebUI can reach InfoBlox MCP"
else
    echo "⚠️  Open WebUI cannot reach InfoBlox MCP via host.docker.internal"
    echo "   Trying alternative addresses..."
    
    # Try Docker bridge IP
    if docker exec open-webui curl -s http://**********:8000/v1/models 2>/dev/null | grep -q "infoblox-assistant"; then
        echo "✅ Works with Docker bridge IP (**********)"
        echo "   Use this in Open WebUI connection: http://**********:8000/v1"
    fi
fi
echo ""

echo "4️⃣ Connection URLs for Open WebUI:"
echo "=================================="
echo "URL:          http://host.docker.internal:8000/v1"
echo "OpenAPI:      http://host.docker.internal:8000/v1/openapi.json"
echo ""
echo "Alternative (Linux):"
echo "URL:          http://**********:8000/v1"
echo "OpenAPI:      http://**********:8000/v1/openapi.json"
echo ""

echo "5️⃣ Next Steps:"
echo "============="
echo "1. Open http://localhost:3000 in your browser"
echo "2. Go to Settings > Connections"
echo "3. Add the connection with the URLs above"
echo "4. Refresh the page"
echo "5. Select 'InfoBlox Assistant' from the model dropdown"
echo ""
echo "✨ Then you can use natural language to manage InfoBlox!"
