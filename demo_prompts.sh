#!/bin/bash
# Demo: InfoBlox Suggested Prompts

echo "🎯 InfoBlox Suggested Prompts Demo"
echo "=================================="
echo ""

# Show available prompts
echo "1. Available prompts from API:"
echo "------------------------------"
curl -s http://localhost:8000/v1/prompts | jq -r '.prompts[] | "• \(.title): \(.prompt)"'
echo ""

# Show model info with prompts
echo "2. Model information:"
echo "--------------------"
curl -s http://localhost:8000/v1/models | jq -r '.data[0] | "Model: \(.name)\nDescription: \(.description)\nDefault prompts: \(.default_prompts | join(", "))"'
echo ""

# Show initial suggestions
echo "3. Welcome message (when no messages):"
echo "-------------------------------------"
curl -s -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"messages": []}' | jq -r '.choices[0].message.content' | head -10
echo ""

echo "✅ All prompts are ready for Open WebUI integration!"
echo ""
echo "To use in Open WebUI:"
echo "1. Go to Workspace → Prompts"
echo "2. Create new prompts using the titles and commands above"
echo "3. They'll appear as clickable suggestions in chat!"
