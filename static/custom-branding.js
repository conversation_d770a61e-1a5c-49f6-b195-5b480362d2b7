// InfoBlox Custom Branding and UI Enhancements for Open WebUI

(function() {
    'use strict';

    // Wait for DOM to be ready
    function ready(fn) {
        if (document.readyState !== 'loading') {
            fn();
        } else {
            document.addEventListener('DOMContentLoaded', fn);
        }
    }

    // InfoBlox branding configuration
    const infobloxConfig = {
        brandName: 'InfoBlox Network Management',
        tagline: 'Professional Network Infrastructure Management',
        primaryColor: '#0066cc',
        secondaryColor: '#004499',
        accentColor: '#00aaff',
        logoUrl: '/static/custom/infoblox-logo.png',
        faviconUrl: '/static/custom/favicon-infoblox.ico'
    };

    // Apply custom branding
    function applyBranding() {
        // Update page title
        document.title = infobloxConfig.brandName;

        // Update favicon
        let favicon = document.querySelector('link[rel="icon"]');
        if (!favicon) {
            favicon = document.createElement('link');
            favicon.rel = 'icon';
            document.head.appendChild(favicon);
        }
        favicon.href = infobloxConfig.faviconUrl;

        // Add custom CSS
        const customCSS = document.createElement('link');
        customCSS.rel = 'stylesheet';
        customCSS.href = '/static/custom/infoblox-theme.css';
        document.head.appendChild(customCSS);

        // Update brand elements
        updateBrandElements();
    }

    // Update brand elements in the UI
    function updateBrandElements() {
        // Update navbar brand
        const navbarBrand = document.querySelector('.navbar-brand, .header-title');
        if (navbarBrand) {
            navbarBrand.textContent = infobloxConfig.brandName;
        }

        // Add tagline if space allows
        const headerContainer = document.querySelector('.header, .navbar');
        if (headerContainer && !headerContainer.querySelector('.tagline')) {
            const tagline = document.createElement('div');
            tagline.className = 'tagline';
            tagline.textContent = infobloxConfig.tagline;
            tagline.style.cssText = `
                font-size: 0.9rem;
                opacity: 0.8;
                margin-top: 0.25rem;
                color: white;
            `;
            headerContainer.appendChild(tagline);
        }
    }

    // Add InfoBlox-specific prompt suggestions
    function addPromptSuggestions() {
        const suggestions = [
            {
                icon: '🔍',
                title: 'Network Discovery',
                prompt: 'Show me all networks in my infrastructure with usage statistics'
            },
            {
                icon: '🌐',
                title: 'DNS Management',
                prompt: 'List all DNS zones and show their current status'
            },
            {
                icon: '📊',
                title: 'IP Usage Report',
                prompt: 'Generate a comprehensive IP usage report for all networks'
            },
            {
                icon: '🛠️',
                title: 'Browse Tools',
                prompt: 'browse tools'
            },
            {
                icon: '🔧',
                title: 'System Health',
                prompt: 'test connection and show system status'
            },
            {
                icon: '🏗️',
                title: 'Create Network',
                prompt: 'Help me create a new network with proper configuration'
            },
            {
                icon: '🔎',
                title: 'Search IP',
                prompt: 'Search for information about a specific IP address'
            },
            {
                icon: '📋',
                title: 'List Records',
                prompt: 'Show me all DNS records for a specific zone'
            }
        ];

        // Find or create suggestions container
        let suggestionsContainer = document.querySelector('.prompt-suggestions');
        if (!suggestionsContainer) {
            suggestionsContainer = document.createElement('div');
            suggestionsContainer.className = 'prompt-suggestions';
            
            // Try to insert after chat input or at the beginning of main content
            const chatInput = document.querySelector('.chat-input, .input-area');
            const mainContent = document.querySelector('.main-content, .chat-container');
            
            if (chatInput && chatInput.parentNode) {
                chatInput.parentNode.insertBefore(suggestionsContainer, chatInput);
            } else if (mainContent) {
                mainContent.insertBefore(suggestionsContainer, mainContent.firstChild);
            }
        }

        // Clear existing suggestions
        suggestionsContainer.innerHTML = '';

        // Add title
        const title = document.createElement('h6');
        title.textContent = 'Quick Actions';
        title.style.cssText = `
            margin: 1rem 0 0.5rem 0;
            color: var(--infoblox-primary, #0066cc);
            font-weight: 600;
        `;
        suggestionsContainer.appendChild(title);

        // Add suggestion buttons
        suggestions.forEach(suggestion => {
            const button = document.createElement('button');
            button.className = 'prompt-suggestion';
            button.innerHTML = `${suggestion.icon} ${suggestion.title}`;
            button.onclick = () => sendPrompt(suggestion.prompt);
            suggestionsContainer.appendChild(button);
        });
    }

    // Send prompt to chat
    function sendPrompt(prompt) {
        const chatInput = document.querySelector('input[type="text"], textarea');
        const sendButton = document.querySelector('button[type="submit"], .send-button');
        
        if (chatInput) {
            chatInput.value = prompt;
            chatInput.dispatchEvent(new Event('input', { bubbles: true }));
            
            if (sendButton) {
                sendButton.click();
            }
        }
    }

    // Add status indicator for InfoBlox connection
    function addConnectionStatus() {
        const statusContainer = document.createElement('div');
        statusContainer.className = 'connection-status';
        statusContainer.style.cssText = `
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 1000;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        `;

        const statusIndicator = document.createElement('span');
        statusIndicator.className = 'status-indicator status-online';
        
        const statusText = document.createElement('span');
        statusText.textContent = 'InfoBlox Connected';

        statusContainer.appendChild(statusIndicator);
        statusContainer.appendChild(statusText);
        document.body.appendChild(statusContainer);

        // Check connection status periodically
        checkConnectionStatus(statusIndicator, statusText);
        setInterval(() => checkConnectionStatus(statusIndicator, statusText), 30000);
    }

    // Check InfoBlox MCP connection status
    async function checkConnectionStatus(indicator, text) {
        try {
            const response = await fetch('/api/v1/models');
            if (response.ok) {
                const data = await response.json();
                const infobloxModel = data.data?.find(model => model.id === 'infoblox-assistant');
                
                if (infobloxModel) {
                    indicator.className = 'status-indicator status-online';
                    text.textContent = 'InfoBlox Connected';
                } else {
                    indicator.className = 'status-indicator status-warning';
                    text.textContent = 'InfoBlox Model Not Found';
                }
            } else {
                indicator.className = 'status-indicator status-offline';
                text.textContent = 'Connection Error';
            }
        } catch (error) {
            indicator.className = 'status-indicator status-offline';
            text.textContent = 'InfoBlox Offline';
        }
    }

    // Add welcome message for new users
    function addWelcomeMessage() {
        const welcomeMessage = `
            <div class="welcome-message" style="
                background: linear-gradient(135deg, #0066cc, #00aaff);
                color: white;
                padding: 2rem;
                border-radius: 12px;
                margin: 1rem;
                text-align: center;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            ">
                <h2>🌐 Welcome to InfoBlox Network Management</h2>
                <p>Your intelligent assistant for InfoBlox infrastructure management</p>
                <p><strong>1,300+ tools</strong> available through natural language commands</p>
                <div style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.9;">
                    Try: "Show me all networks" or "Test connection" to get started
                </div>
            </div>
        `;

        // Only show if no previous messages
        const chatContainer = document.querySelector('.chat-container, .messages');
        if (chatContainer && chatContainer.children.length === 0) {
            chatContainer.innerHTML = welcomeMessage + chatContainer.innerHTML;
        }
    }

    // Initialize all customizations
    function initializeInfoBloxUI() {
        applyBranding();
        addPromptSuggestions();
        addConnectionStatus();
        addWelcomeMessage();
    }

    // Run when DOM is ready
    ready(initializeInfoBloxUI);

    // Re-run customizations when navigating (for SPA)
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            setTimeout(initializeInfoBloxUI, 500);
        }
    }).observe(document, { subtree: true, childList: true });

})();
