/* InfoBlox Professional Theme for Open WebUI */

:root {
  /* InfoBlox Brand Colors */
  --infoblox-primary: #0066cc;
  --infoblox-secondary: #004499;
  --infoblox-accent: #00aaff;
  --infoblox-success: #28a745;
  --infoblox-warning: #ffc107;
  --infoblox-danger: #dc3545;
  --infoblox-light: #f8f9fa;
  --infoblox-dark: #343a40;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-sidebar: linear-gradient(135deg, var(--infoblox-primary), var(--infoblox-secondary));
  
  /* Text Colors */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-light: #ffffff;
  
  /* Border and Shadow */
  --border-color: #dee2e6;
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global Styles */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* Header/Navigation */
.navbar, .header {
  background: var(--bg-sidebar) !important;
  border-bottom: 3px solid var(--infoblox-accent);
  box-shadow: var(--shadow-md);
}

.navbar-brand, .header-title {
  color: var(--text-light) !important;
  font-weight: 700;
  font-size: 1.5rem;
}

.navbar-brand::before {
  content: "🌐 ";
  margin-right: 0.5rem;
}

/* Sidebar */
.sidebar {
  background: var(--bg-sidebar) !important;
  border-right: 3px solid var(--infoblox-accent);
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  border-radius: 8px;
  margin: 0.25rem 0.5rem;
  transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: var(--text-light) !important;
  transform: translateX(5px);
}

.sidebar .nav-link.active {
  background-color: var(--infoblox-accent) !important;
  color: var(--text-light) !important;
  box-shadow: var(--shadow-sm);
}

/* Main Content Area */
.main-content {
  background-color: var(--bg-primary);
  border-radius: 12px;
  margin: 1rem;
  box-shadow: var(--shadow-md);
}

/* Chat Interface */
.chat-container {
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
}

.chat-message {
  border-radius: 12px;
  margin: 1rem 0;
  padding: 1rem;
  box-shadow: var(--shadow-sm);
}

.chat-message.user {
  background: linear-gradient(135deg, var(--infoblox-primary), var(--infoblox-accent));
  color: var(--text-light);
  margin-left: 2rem;
}

.chat-message.assistant {
  background: var(--bg-secondary);
  border-left: 4px solid var(--infoblox-primary);
  margin-right: 2rem;
}

.chat-message.system {
  background: linear-gradient(135deg, var(--infoblox-success), #20c997);
  color: var(--text-light);
  text-align: center;
  border-radius: 20px;
}

/* Buttons */
.btn-primary {
  background: linear-gradient(135deg, var(--infoblox-primary), var(--infoblox-accent));
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--infoblox-secondary), var(--infoblox-primary));
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  border-color: var(--infoblox-primary);
  background-color: var(--infoblox-light);
}

/* Input Fields */
.form-control, .input-field {
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem;
  transition: all 0.3s ease;
}

.form-control:focus, .input-field:focus {
  border-color: var(--infoblox-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 102, 204, 0.25);
}

/* Model Selector */
.model-selector {
  background: var(--bg-primary);
  border: 2px solid var(--infoblox-primary);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: var(--shadow-sm);
}

.model-option {
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.model-option:hover {
  background-color: var(--infoblox-light);
}

.model-option.selected {
  background: linear-gradient(135deg, var(--infoblox-primary), var(--infoblox-accent));
  color: var(--text-light);
}

/* Prompt Suggestions */
.prompt-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin: 1rem 0;
}

.prompt-suggestion {
  background: var(--bg-primary);
  border: 2px solid var(--infoblox-primary);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.prompt-suggestion:hover {
  background: var(--infoblox-primary);
  color: var(--text-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Status Indicators */
.status-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-online {
  background-color: var(--infoblox-success);
  box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
}

.status-offline {
  background-color: var(--infoblox-danger);
}

.status-warning {
  background-color: var(--infoblox-warning);
}

/* Loading Animation */
.loading-spinner {
  border: 3px solid var(--bg-tertiary);
  border-top: 3px solid var(--infoblox-primary);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-message.user {
    margin-left: 0.5rem;
  }
  
  .chat-message.assistant {
    margin-right: 0.5rem;
  }
  
  .prompt-suggestions {
    flex-direction: column;
  }
  
  .prompt-suggestion {
    text-align: center;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --border-color: #555555;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--infoblox-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--infoblox-secondary);
}
