version: '3.8'

services:
  # InfoBlox MCP Server
  infoblox-mcp:
    build:
      context: .
      dockerfile: Dockerfile.infoblox
    container_name: infoblox-mcp-server
    ports:
      - "8000:8000"
    environment:
      - GRID_MASTER_URL=${GRID_MASTER_URL:-https://*************/wapi/v2.13.1}
      - INFOBLOX_USERNAME=${INFOBLOX_USERNAME:-admin}
      - INFOBLOX_PASSWORD=${INFOBLOX_PASSWORD:-infoblox}
      - NETWORK_VIEW=${NETWORK_VIEW:-default}
      - PYTHONUNBUFFERED=1
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
    networks:
      - infoblox-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Open WebUI
  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    container_name: open-webui-infoblox
    ports:
      - "3000:8080"
    environment:
      # Basic Configuration
      - WEBUI_NAME=InfoBlox Assistant
      - WEBUI_URL=http://localhost:3000
      - WEBUI_SECRET_KEY=${WEBUI_SECRET_KEY:-your-secret-key-here}
      
      # OpenAI API Configuration for InfoBlox MCP
      - OPENAI_API_BASE_URL=http://infoblox-mcp:8000/v1
      - OPENAI_API_KEY=dummy-key
      - DEFAULT_MODELS=infoblox-assistant
      
      # UI Customization
      - DEFAULT_USER_ROLE=user
      - ENABLE_SIGNUP=true
      - ENABLE_LOGIN_FORM=true
      - ENABLE_COMMUNITY_SHARING=false
      
      # Custom Branding
      - WEBUI_FAVICON_URL=/static/favicon-infoblox.ico
      - CUSTOM_NAME=InfoBlox Network Management
      - SHOW_ADMIN_DETAILS=false
      
      # Feature Toggles
      - ENABLE_RAG_HYBRID_SEARCH=false
      - ENABLE_RAG_WEB_LOADER_SSL_VERIFICATION=false
      - ENABLE_IMAGE_GENERATION=false
      - ENABLE_COMMUNITY_SHARING=false
      
      # Performance
      - CHUNK_SIZE=1024
      - CHUNK_OVERLAP=100
      
    volumes:
      - open-webui-data:/app/backend/data
      - ./open-webui-config:/app/backend/config:ro
      - ./static:/app/backend/static/custom:ro
    networks:
      - infoblox-network
    depends_on:
      infoblox-mcp:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Optional: Nginx reverse proxy for custom domain/SSL
  nginx:
    image: nginx:alpine
    container_name: infoblox-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./static:/usr/share/nginx/html/static:ro
    networks:
      - infoblox-network
    depends_on:
      - open-webui
      - infoblox-mcp
    restart: unless-stopped
    profiles:
      - production

networks:
  infoblox-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  open-webui-data:
    driver: local
