{"total_objects": 269, "working": ["ad_auth_service", "admingroup", "adminrole", "adminuser", "allendpoints", "allnsgroup", "approvalworkflow", "authpolicy", "awsrte53taskgroup", "awsuser", "bfdtemplate", "bulkhost", "bulkhostnametemplate", "cacertificate", "captiveportal", "certificate:authservice", "csvimporttask", "datacollectioncluster", "dbsnapshot", "ddns:principalcluster", "ddns:principalcluster:group", "dhc<PERSON><PERSON><PERSON>", "dhcpoptiondefinition", "dhcpoptionspace", "discovery:credentialgroup", "discovery:<PERSON><PERSON><PERSON><PERSON>", "discovery:diagnostictask", "discovery:gridproperties", "discovery:memberproperties", "discoverytask", "distributionschedule", "dns64group", "dtc:certificate", "dtc:lbdn", "dtc:monitor", "dtc:monitor:http", "dtc:monitor:icmp", "dtc:monitor:pdp", "dtc:monitor:sip", "dtc:monitor:snmp", "dtc:monitor:tcp", "dtc:object", "dtc:pool", "dtc:server", "dtc:topology", "dtc:topology:rule", "dxl:endpoint", "extensibleattributedef", "filterfingerprint", "filtermac", "filternac", "filteroption", "filterrelayagent", "fingerprint", "fixedaddress", "fixedaddresstemplate", "ftpuser", "gmcgroup", "gmcschedule", "grid", "grid:cloudapi", "grid:cloudapi:cloudstatistics", "grid:cloudapi:tenant", "grid:cloudapi:vm", "grid:cloudapi:vmaddress", "grid:dashboard", "grid:dhcpproperties", "grid:dns", "grid:filedistribution", "grid:license_pool", "grid:license_pool_container", "grid:maxminddbinfo", "grid:member:cloudapi", "grid:servicerestart:group", "grid:servicerestart:request", "grid:servicerestart:request:changedobject", "grid:servicerestart:status", "grid:threatanalytics", "grid:threatprotection", "grid:x509certificate", "hostnamerewritepolicy", "hsm:allgroups", "hsm:entrustnshieldgroup", "hsm:thaleslunagroup", "ipam:statistics", "ipv6dhcpoptiondefinition", "ipv6dhcpoptionspace", "ipv6filteroption", "ipv6fixedaddress", "ipv6fixedaddresstemplate", "ipv6network", "ipv6networkcontainer", "ipv6networktemplate", "ipv6range", "ipv6rangetemplate", "ipv6sharednetwork", "<PERSON>er<PERSON><PERSON><PERSON>", "ldap_auth_service", "lease", "license:gridwide", "localuser:authservice", "macfilteraddress", "mastergrid", "member", "member:dhcpproperties", "member:dns", "member:filedistribution", "member:license", "member:parentalcontrol", "member:threatanalytics", "member:threatprotection", "memberclouddnssync", "memberdfp", "msserver", "msserver:adsites:domain", "msserver:adsites:site", "msserver:dhcp", "msserver:dns", "mssuperscope", "<PERSON><PERSON><PERSON>", "natgroup", "network", "networkcontainer", "networktemplate", "networkuser", "networkview", "notification:rest:endpoint", "notification:rest:template", "notification:rule", "nsgroup", "nsgroup:delegation", "nsgroup:forwardingmember", "nsgroup:forwardstubserver", "nsgroup:stubmember", "outbound:cloudclient", "parentalcontrol:avp", "parentalcontrol:blockingpolicy", "parentalcontrol:subscriber", "parentalcontrol:subscribersite", "permission", "pxgrid:endpoint", "radius:authservice", "range", "rangetemplate", "record:a", "record:aaaa", "record:alias", "record:caa", "record:cname", "record:dhcid", "record:dname", "record:dns<PERSON>", "record:ds", "record:dtclbdn", "record:host", "record:host_ipv4addr", "record:host_ipv6addr", "record:mx", "record:naptr", "record:ns", "record:nsec", "record:nsec3", "record:nsec3param", "record:ptr", "record:rpz:a", "record:rpz:a:ipaddress", "record:rpz:aaaa", "record:rpz:aaaa:ipad<PERSON>", "record:rpz:cname", "record:rpz:cname:clientipaddress", "record:rpz:cname:clientipaddressdn", "record:rpz:cname:ipaddress", "record:rpz:cname:ipaddressdn", "record:rpz:mx", "record:rpz:naptr", "record:rpz:ptr", "record:rpz:srv", "record:rpz:txt", "record:rrsig", "record:srv", "record:tlsa", "record:txt", "record:unknown", "recordnamepolicy", "restartservicestatus", "rir", "rir:organization", "roaminghost", "ruleset", "saml:authservice", "scavengingtask", "scheduledtask", "sharednetwork", "sharedrecord:a", "sharedrecord:aaaa", "sharedrecord:cname", "sharedrecord:mx", "sharedrecord:srv", "sharedrecord:txt", "sharedrecordgroup", "smartfolder:children", "smartfolder:global", "smartfolder:personal", "snmpuser", "superhost", "syslog:endpoint", "tacacsplus:authservice", "taxii", "threatanalytics:analytics_whitelist", "threatanalytics:moduleset", "threatanalytics:whitelist", "threatinsight:cloudclient", "threatprotection:grid:rule", "threatprotection:profile", "threatprotection:profile:rule", "threatprotection:rule", "threatprotection:rulecategory", "threatprotection:ruleset", "threatprotection:ruletemplate", "threatprotection:statistics", "upgradegroup", "upgradeschedule", "userprofile", "vdiscoverytask", "view", "vlan", "vlanrange", "vlanview", "zone_auth", "zone_auth_discrepancy", "zone_delegated", "zone_forward", "zone_rp", "zone_stub"], "requires_discovery": ["discovery:device", "discovery:devicecomponent", "discovery:deviceinterface", "discovery:devicesupportbundle", "discovery:sdnnetwork", "discovery:vrf"], "failing": ["allrecords", "allrpzrecords", "capacityreport", "db_objects", "deleted_objects", "dhcp:statistics", "discovery", "discovery:status", "dtc", "dtc:allrecords", "dtc:record:a", "dtc:record:aaaa", "dtc:record:cname", "dtc:record:naptr", "dtc:record:srv", "dtc:topology:label", "fileop", "grid:servicerestart:group:order", "ipv4address", "ipv6address", "network_discovery", "orderedranges", "orderedresponsepolicyzones", "parentalcontrol:subscriberrecord", "request", "search", "superhostchild", "tftpfiledir", "upgradestatus"], "details": {"ad_auth_service": {"status": "working", "count": 0}, "admingroup": {"status": "working", "count": 1}, "adminrole": {"status": "working", "count": 1}, "adminuser": {"status": "working", "count": 1}, "allendpoints": {"status": "working", "count": 0}, "allnsgroup": {"status": "working", "count": 0}, "allrecords": {"status": "failed", "error": "HTTP 400"}, "allrpzrecords": {"status": "failed", "error": "HTTP 400"}, "approvalworkflow": {"status": "working", "count": 0}, "authpolicy": {"status": "working", "count": 1}, "awsrte53taskgroup": {"status": "working", "count": 0}, "awsuser": {"status": "working", "count": 0}, "bfdtemplate": {"status": "working", "count": 0}, "bulkhost": {"status": "working", "count": 0}, "bulkhostnametemplate": {"status": "working", "count": 1}, "cacertificate": {"status": "working", "count": 0}, "capacityreport": {"status": "failed", "error": "HTTP 400"}, "captiveportal": {"status": "working", "count": 1}, "certificate:authservice": {"status": "working", "count": 0}, "csvimporttask": {"status": "working", "count": 1}, "datacollectioncluster": {"status": "working", "count": 1}, "db_objects": {"status": "failed", "error": "HTTP 400"}, "dbsnapshot": {"status": "working", "count": 0}, "ddns:principalcluster": {"status": "working", "count": 0}, "ddns:principalcluster:group": {"status": "working", "count": 0}, "deleted_objects": {"status": "failed", "error": "HTTP 400"}, "dhcp:statistics": {"status": "failed", "error": "HTTP 400"}, "dhcpfailover": {"status": "working", "count": 0}, "dhcpoptiondefinition": {"status": "working", "count": 1}, "dhcpoptionspace": {"status": "working", "count": 1}, "discovery": {"status": "failed", "error": "HTTP 400"}, "discovery:credentialgroup": {"status": "working", "count": 1}, "discovery:device": {"status": "requires_discovery", "error": "Discovery service not running"}, "discovery:devicecomponent": {"status": "requires_discovery", "error": "Discovery service not running"}, "discovery:deviceinterface": {"status": "requires_discovery", "error": "Discovery service not running"}, "discovery:deviceneighbor": {"status": "working", "count": 0}, "discovery:devicesupportbundle": {"status": "requires_discovery", "error": "Discovery service not running"}, "discovery:diagnostictask": {"status": "working", "count": 0}, "discovery:gridproperties": {"status": "working", "count": 1}, "discovery:memberproperties": {"status": "working", "count": 0}, "discovery:sdnnetwork": {"status": "requires_discovery", "error": "Discovery service not running"}, "discovery:status": {"status": "failed", "error": "HTTP 400"}, "discovery:vrf": {"status": "requires_discovery", "error": "Discovery service not running"}, "discoverytask": {"status": "working", "count": 1}, "distributionschedule": {"status": "working", "count": 1}, "dns64group": {"status": "working", "count": 1}, "dtc": {"status": "failed", "error": "HTTP 400"}, "dtc:allrecords": {"status": "failed", "error": "HTTP 400"}, "dtc:certificate": {"status": "working", "count": 0}, "dtc:lbdn": {"status": "working", "count": 0}, "dtc:monitor": {"status": "working", "count": 1}, "dtc:monitor:http": {"status": "working", "count": 1}, "dtc:monitor:icmp": {"status": "working", "count": 1}, "dtc:monitor:pdp": {"status": "working", "count": 1}, "dtc:monitor:sip": {"status": "working", "count": 1}, "dtc:monitor:snmp": {"status": "working", "count": 1}, "dtc:monitor:tcp": {"status": "working", "count": 0}, "dtc:object": {"status": "working", "count": 0}, "dtc:pool": {"status": "working", "count": 0}, "dtc:record:a": {"status": "failed", "error": "HTTP 400"}, "dtc:record:aaaa": {"status": "failed", "error": "HTTP 400"}, "dtc:record:cname": {"status": "failed", "error": "HTTP 400"}, "dtc:record:naptr": {"status": "failed", "error": "HTTP 400"}, "dtc:record:srv": {"status": "failed", "error": "HTTP 400"}, "dtc:server": {"status": "working", "count": 0}, "dtc:topology": {"status": "working", "count": 0}, "dtc:topology:label": {"status": "failed", "error": "HTTP 400"}, "dtc:topology:rule": {"status": "working", "count": 0}, "dxl:endpoint": {"status": "working", "count": 0}, "extensibleattributedef": {"status": "working", "count": 1}, "fileop": {"status": "failed", "error": "HTTP 400"}, "filterfingerprint": {"status": "working", "count": 0}, "filtermac": {"status": "working", "count": 0}, "filternac": {"status": "working", "count": 0}, "filteroption": {"status": "working", "count": 0}, "filterrelayagent": {"status": "working", "count": 0}, "fingerprint": {"status": "working", "count": 1}, "fixedaddress": {"status": "working", "count": 0}, "fixedaddresstemplate": {"status": "working", "count": 0}, "ftpuser": {"status": "working", "count": 0}, "gmcgroup": {"status": "working", "count": 1}, "gmcschedule": {"status": "working", "count": 1}, "grid": {"status": "working", "count": 1}, "grid:cloudapi": {"status": "working", "count": 1}, "grid:cloudapi:cloudstatistics": {"status": "working", "count": 1}, "grid:cloudapi:tenant": {"status": "working", "count": 0}, "grid:cloudapi:vm": {"status": "working", "count": 0}, "grid:cloudapi:vmaddress": {"status": "working", "count": 0}, "grid:dashboard": {"status": "working", "count": 1}, "grid:dhcpproperties": {"status": "working", "count": 1}, "grid:dns": {"status": "working", "count": 1}, "grid:filedistribution": {"status": "working", "count": 1}, "grid:license_pool": {"status": "working", "count": 0}, "grid:license_pool_container": {"status": "working", "count": 1}, "grid:maxminddbinfo": {"status": "working", "count": 0}, "grid:member:cloudapi": {"status": "working", "count": 0}, "grid:servicerestart:group": {"status": "working", "count": 1}, "grid:servicerestart:group:order": {"status": "failed", "error": "HTTP 400"}, "grid:servicerestart:request": {"status": "working", "count": 0}, "grid:servicerestart:request:changedobject": {"status": "working", "count": 0}, "grid:servicerestart:status": {"status": "working", "count": 1}, "grid:threatanalytics": {"status": "working", "count": 1}, "grid:threatprotection": {"status": "working", "count": 1}, "grid:x509certificate": {"status": "working", "count": 0}, "hostnamerewritepolicy": {"status": "working", "count": 1}, "hsm:allgroups": {"status": "working", "count": 1}, "hsm:entrustnshieldgroup": {"status": "working", "count": 0}, "hsm:thaleslunagroup": {"status": "working", "count": 0}, "ipam:statistics": {"status": "working", "count": 1}, "ipv4address": {"status": "failed", "error": "HTTP 400"}, "ipv6address": {"status": "failed", "error": "HTTP 400"}, "ipv6dhcpoptiondefinition": {"status": "working", "count": 1}, "ipv6dhcpoptionspace": {"status": "working", "count": 1}, "ipv6filteroption": {"status": "working", "count": 0}, "ipv6fixedaddress": {"status": "working", "count": 0}, "ipv6fixedaddresstemplate": {"status": "working", "count": 0}, "ipv6network": {"status": "working", "count": 0}, "ipv6networkcontainer": {"status": "working", "count": 0}, "ipv6networktemplate": {"status": "working", "count": 0}, "ipv6range": {"status": "working", "count": 0}, "ipv6rangetemplate": {"status": "working", "count": 0}, "ipv6sharednetwork": {"status": "working", "count": 0}, "kerberoskey": {"status": "working", "count": 0}, "ldap_auth_service": {"status": "working", "count": 0}, "lease": {"status": "working", "count": 0}, "license:gridwide": {"status": "working", "count": 0}, "localuser:authservice": {"status": "working", "count": 1}, "macfilteraddress": {"status": "working", "count": 0}, "mastergrid": {"status": "working", "count": 1}, "member": {"status": "working", "count": 1}, "member:dhcpproperties": {"status": "working", "count": 1}, "member:dns": {"status": "working", "count": 1}, "member:filedistribution": {"status": "working", "count": 1}, "member:license": {"status": "working", "count": 1}, "member:parentalcontrol": {"status": "working", "count": 1}, "member:threatanalytics": {"status": "working", "count": 0}, "member:threatprotection": {"status": "working", "count": 0}, "memberclouddnssync": {"status": "working", "count": 1}, "memberdfp": {"status": "working", "count": 1}, "msserver": {"status": "working", "count": 0}, "msserver:adsites:domain": {"status": "working", "count": 0}, "msserver:adsites:site": {"status": "working", "count": 0}, "msserver:dhcp": {"status": "working", "count": 0}, "msserver:dns": {"status": "working", "count": 0}, "mssuperscope": {"status": "working", "count": 0}, "namedacl": {"status": "working", "count": 0}, "natgroup": {"status": "working", "count": 0}, "network": {"status": "working", "count": 1}, "network_discovery": {"status": "failed", "error": "HTTP 400"}, "networkcontainer": {"status": "working", "count": 1}, "networktemplate": {"status": "working", "count": 0}, "networkuser": {"status": "working", "count": 0}, "networkview": {"status": "working", "count": 1}, "notification:rest:endpoint": {"status": "working", "count": 0}, "notification:rest:template": {"status": "working", "count": 0}, "notification:rule": {"status": "working", "count": 0}, "nsgroup": {"status": "working", "count": 0}, "nsgroup:delegation": {"status": "working", "count": 0}, "nsgroup:forwardingmember": {"status": "working", "count": 0}, "nsgroup:forwardstubserver": {"status": "working", "count": 0}, "nsgroup:stubmember": {"status": "working", "count": 0}, "orderedranges": {"status": "failed", "error": "HTTP 400"}, "orderedresponsepolicyzones": {"status": "failed", "error": "HTTP 400"}, "outbound:cloudclient": {"status": "working", "count": 1}, "parentalcontrol:avp": {"status": "working", "count": 1}, "parentalcontrol:blockingpolicy": {"status": "working", "count": 1}, "parentalcontrol:subscriber": {"status": "working", "count": 1}, "parentalcontrol:subscriberrecord": {"status": "failed", "error": "HTTP 400"}, "parentalcontrol:subscribersite": {"status": "working", "count": 0}, "permission": {"status": "working", "count": 1}, "pxgrid:endpoint": {"status": "working", "count": 0}, "radius:authservice": {"status": "working", "count": 0}, "range": {"status": "working", "count": 0}, "rangetemplate": {"status": "working", "count": 0}, "record:a": {"status": "working", "count": 0}, "record:aaaa": {"status": "working", "count": 0}, "record:alias": {"status": "working", "count": 0}, "record:caa": {"status": "working", "count": 0}, "record:cname": {"status": "working", "count": 0}, "record:dhcid": {"status": "working", "count": 0}, "record:dname": {"status": "working", "count": 0}, "record:dnskey": {"status": "working", "count": 0}, "record:ds": {"status": "working", "count": 0}, "record:dtclbdn": {"status": "working", "count": 0}, "record:host": {"status": "working", "count": 0}, "record:host_ipv4addr": {"status": "working", "count": 0}, "record:host_ipv6addr": {"status": "working", "count": 0}, "record:mx": {"status": "working", "count": 0}, "record:naptr": {"status": "working", "count": 0}, "record:ns": {"status": "working", "count": 1}, "record:nsec": {"status": "working", "count": 0}, "record:nsec3": {"status": "working", "count": 0}, "record:nsec3param": {"status": "working", "count": 0}, "record:ptr": {"status": "working", "count": 1}, "record:rpz:a": {"status": "working", "count": 0}, "record:rpz:a:ipaddress": {"status": "working", "count": 0}, "record:rpz:aaaa": {"status": "working", "count": 0}, "record:rpz:aaaa:ipaddress": {"status": "working", "count": 0}, "record:rpz:cname": {"status": "working", "count": 0}, "record:rpz:cname:clientipaddress": {"status": "working", "count": 0}, "record:rpz:cname:clientipaddressdn": {"status": "working", "count": 0}, "record:rpz:cname:ipaddress": {"status": "working", "count": 0}, "record:rpz:cname:ipaddressdn": {"status": "working", "count": 0}, "record:rpz:mx": {"status": "working", "count": 0}, "record:rpz:naptr": {"status": "working", "count": 0}, "record:rpz:ptr": {"status": "working", "count": 0}, "record:rpz:srv": {"status": "working", "count": 0}, "record:rpz:txt": {"status": "working", "count": 0}, "record:rrsig": {"status": "working", "count": 0}, "record:srv": {"status": "working", "count": 0}, "record:tlsa": {"status": "working", "count": 0}, "record:txt": {"status": "working", "count": 0}, "record:unknown": {"status": "working", "count": 0}, "recordnamepolicy": {"status": "working", "count": 1}, "request": {"status": "failed", "error": "HTTP 400"}, "restartservicestatus": {"status": "working", "count": 1}, "rir": {"status": "working", "count": 1}, "rir:organization": {"status": "working", "count": 0}, "roaminghost": {"status": "working", "count": 0}, "ruleset": {"status": "working", "count": 0}, "saml:authservice": {"status": "working", "count": 0}, "scavengingtask": {"status": "working", "count": 0}, "scheduledtask": {"status": "working", "count": 0}, "search": {"status": "failed", "error": "HTTP 400"}, "sharednetwork": {"status": "working", "count": 0}, "sharedrecord:a": {"status": "working", "count": 0}, "sharedrecord:aaaa": {"status": "working", "count": 0}, "sharedrecord:cname": {"status": "working", "count": 0}, "sharedrecord:mx": {"status": "working", "count": 0}, "sharedrecord:srv": {"status": "working", "count": 0}, "sharedrecord:txt": {"status": "working", "count": 0}, "sharedrecordgroup": {"status": "working", "count": 0}, "smartfolder:children": {"status": "working", "count": 0}, "smartfolder:global": {"status": "working", "count": 0}, "smartfolder:personal": {"status": "working", "count": 1}, "snmpuser": {"status": "working", "count": 0}, "superhost": {"status": "working", "count": 0}, "superhostchild": {"status": "failed", "error": "HTTP 400"}, "syslog:endpoint": {"status": "working", "count": 0}, "tacacsplus:authservice": {"status": "working", "count": 0}, "taxii": {"status": "working", "count": 0}, "tftpfiledir": {"status": "failed", "error": "HTTP 400"}, "threatanalytics:analytics_whitelist": {"status": "working", "count": 0}, "threatanalytics:moduleset": {"status": "working", "count": 0}, "threatanalytics:whitelist": {"status": "working", "count": 0}, "threatinsight:cloudclient": {"status": "working", "count": 1}, "threatprotection:grid:rule": {"status": "working", "count": 0}, "threatprotection:profile": {"status": "working", "count": 0}, "threatprotection:profile:rule": {"status": "working", "count": 0}, "threatprotection:rule": {"status": "working", "count": 0}, "threatprotection:rulecategory": {"status": "working", "count": 0}, "threatprotection:ruleset": {"status": "working", "count": 0}, "threatprotection:ruletemplate": {"status": "working", "count": 0}, "threatprotection:statistics": {"status": "working", "count": 0}, "upgradegroup": {"status": "working", "count": 1}, "upgradeschedule": {"status": "working", "count": 1}, "upgradestatus": {"status": "failed", "error": "HTTP 400"}, "userprofile": {"status": "working", "count": 1}, "vdiscoverytask": {"status": "working", "count": 0}, "view": {"status": "working", "count": 1}, "vlan": {"status": "working", "count": 0}, "vlanrange": {"status": "working", "count": 0}, "vlanview": {"status": "working", "count": 1}, "zone_auth": {"status": "working", "count": 0}, "zone_auth_discrepancy": {"status": "working", "count": 0}, "zone_delegated": {"status": "working", "count": 0}, "zone_forward": {"status": "working", "count": 0}, "zone_rp": {"status": "working", "count": 0}, "zone_stub": {"status": "working", "count": 0}}}