import requests
import json
import urllib3

urllib3.disable_warnings()

# Test a few key objects
test_objects = [
    "network",
    "ipv4address", 
    "zone_auth",
    "record:a",
    "record:host",
    "range",
    "lease",
    "discovery:sdnnetwork"  # This should fail
]

session = requests.Session()
session.auth = ("admin", "infoblox")
session.verify = False

working = []
failing = []

for obj in test_objects:
    try:
        resp = session.get(f"https://*************/wapi/v2.13.1/{obj}?_max_results=1")
        if resp.status_code == 200:
            working.append(obj)
            print(f"✅ {obj}")
        else:
            failing.append(obj)
            print(f"❌ {obj} - {resp.status_code}")
    except Exception as e:
        failing.append(obj)
        print(f"❌ {obj} - Error")

print(f"\nWorking: {len(working)}")
print(f"Failing: {len(failing)}")
