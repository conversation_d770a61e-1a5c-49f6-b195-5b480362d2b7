# InfoBlox MCP Server Dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt* ./
RUN pip install --no-cache-dir \
    requests \
    urllib3 \
    python-dotenv \
    pydantic \
    fastapi \
    uvicorn \
    aiofiles \
    jinja2

# Create necessary directories
RUN mkdir -p /app/config /app/logs /app/static

# Copy application files
COPY natural_language_server.py ./mcp_server.py
COPY discovered_wapi_objects.json* ./
COPY infoblox_mcp_tools.json* ./
COPY wapi_schema.json* ./
COPY openapi.json* ./
COPY config.txt ./config/

# Copy static assets if they exist
COPY static/* ./static/ 2>/dev/null || true

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app
ENV LOG_LEVEL=INFO

# Create non-root user for security
RUN groupadd -r infoblox && useradd -r -g infoblox infoblox
RUN chown -R infoblox:infoblox /app
USER infoblox

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Start command
CMD ["python", "-u", "mcp_server.py"]
