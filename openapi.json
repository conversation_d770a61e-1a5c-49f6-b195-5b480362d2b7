{"openapi": "3.0.0", "info": {"title": "InfoBlox Assistant API", "description": "Natural language InfoBlox management with 1,345+ tools", "version": "1.0.0"}, "servers": [{"url": "http://host.docker.internal:8000/v1", "description": "InfoBlox MCP Server"}], "paths": {"/models": {"get": {"summary": "List available models", "operationId": "listModels", "responses": {"200": {"description": "List of available models", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "infoblox-assistant"}, "name": {"type": "string", "example": "InfoBlox Assistant"}, "created": {"type": "integer"}, "owned_by": {"type": "string"}}}}}}}}}}}}, "/chat/completions": {"post": {"summary": "Create chat completion", "operationId": "createChatCompletion", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["messages"], "properties": {"model": {"type": "string", "default": "infoblox-assistant"}, "messages": {"type": "array", "items": {"type": "object", "properties": {"role": {"type": "string", "enum": ["system", "user", "assistant"]}, "content": {"type": "string"}}}}, "temperature": {"type": "number", "default": 0.7}, "stream": {"type": "boolean", "default": false}}}}}}, "responses": {"200": {"description": "Chat completion response", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "object": {"type": "string", "example": "chat.completion"}, "created": {"type": "integer"}, "model": {"type": "string"}, "choices": {"type": "array", "items": {"type": "object", "properties": {"index": {"type": "integer"}, "message": {"type": "object", "properties": {"role": {"type": "string"}, "content": {"type": "string"}}}, "finish_reason": {"type": "string"}}}}}}}}}}}}}, "components": {"schemas": {"Model": {"type": "object", "properties": {"id": {"type": "string", "example": "infoblox-assistant"}, "name": {"type": "string", "example": "InfoBlox Assistant"}, "description": {"type": "string"}}}}, "securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization"}}}}