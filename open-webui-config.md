# Open WebUI Configuration for InfoBlox MCP

## Method 1: Using OpenAI-Compatible API

1. In Open WebUI, go to Settings → Connections
2. Add a new "OpenAI API" connection with these settings:
   - API Base URL: http://localhost:8000/v1
   - API Key: dummy-key (or leave empty)
   - Model: infoblox-assistant

## Method 2: Custom Model Configuration

If Method 1 doesn't work, you can add a custom model by editing the Open WebUI configuration:

```yaml
models:
  - id: infoblox-assistant
    name: InfoBlox Assistant
    type: openai
    base_url: http://localhost:8000/v1
    api_key: dummy
    description: InfoBlox WAPI Assistant with 1,300+ tools
```

## Method 3: Environment Variables

If running Open WebUI in Docker, you can set:

```bash
docker run -d \
  -p 3000:8080 \
  -e OPENAI_API_BASE_URL="http://host.docker.internal:8000/v1" \
  -e OPENAI_API_KEY="dummy" \
  -e DEFAULT_MODELS="infoblox-assistant" \
  --add-host=host.docker.internal:host-gateway \
  --name open-webui \
  ghcr.io/open-webui/open-webui:main
```

## Testing the Connection

Once configured, you should see "InfoBlox Assistant" in the model dropdown. Test with:
- "test connection"
- "list all networks json"
- "browse tools"

## Troubleshooting

If the model doesn't appear:
1. Check Open WebUI logs for connection errors
2. Ensure InfoBlox MCP is running: `curl http://localhost:8000/health`
3. Verify model endpoint: `curl http://localhost:8000/v1/models`
4. Check network connectivity between containers
