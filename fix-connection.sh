#!/bin/bash

# Fix InfoBlox MCP Connection Issues for Open WebUI
echo "🔧 Fixing InfoBlox MCP Connection Issues"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if services are running
log_info "Checking service status..."
if ! docker-compose ps | grep -q "Up"; then
    log_error "Services are not running. Starting them now..."
    docker-compose up -d
    sleep 10
fi

# Test different connection methods
log_info "Testing connection methods..."

# Method 1: Container name (recommended for Docker Compose)
log_info "Testing container-to-container communication..."
if docker-compose exec -T open-webui curl -s http://infoblox-mcp:8000/health > /dev/null 2>&1; then
    log_success "Container-to-container communication works!"
    RECOMMENDED_URL="http://infoblox-mcp:8000/v1"
    CONNECTION_METHOD="container"
else
    log_error "Container-to-container communication failed"
fi

# Method 2: Host network (for external access)
log_info "Testing host network access..."
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    log_success "Host network access works!"
    if [ -z "$RECOMMENDED_URL" ]; then
        RECOMMENDED_URL="http://localhost:8000/v1"
        CONNECTION_METHOD="localhost"
    fi
else
    log_error "Host network access failed"
fi

# Method 3: Docker bridge IP
log_info "Testing Docker bridge network..."
BRIDGE_IP=$(docker network inspect bridge | grep -A 3 '"Gateway"' | grep '"Gateway"' | cut -d'"' -f4)
if [ ! -z "$BRIDGE_IP" ] && curl -s http://$BRIDGE_IP:8000/health > /dev/null 2>&1; then
    log_success "Docker bridge network works! ($BRIDGE_IP)"
    if [ -z "$RECOMMENDED_URL" ]; then
        RECOMMENDED_URL="http://$BRIDGE_IP:8000/v1"
        CONNECTION_METHOD="bridge"
    fi
else
    log_warning "Docker bridge network not accessible"
fi

# Method 4: host.docker.internal (mainly for Docker Desktop)
log_info "Testing host.docker.internal..."
if docker-compose exec -T open-webui curl -s http://host.docker.internal:8000/health > /dev/null 2>&1; then
    log_success "host.docker.internal works!"
    if [ -z "$RECOMMENDED_URL" ]; then
        RECOMMENDED_URL="http://host.docker.internal:8000/v1"
        CONNECTION_METHOD="host_docker_internal"
    fi
else
    log_warning "host.docker.internal not accessible (normal on Linux)"
fi

# Provide solution based on what works
echo ""
echo "🎯 SOLUTION:"
echo "============"

if [ ! -z "$RECOMMENDED_URL" ]; then
    log_success "Found working connection method: $CONNECTION_METHOD"
    echo ""
    echo "Use this URL in Open WebUI Settings → Connections:"
    echo -e "${GREEN}$RECOMMENDED_URL${NC}"
    echo ""
    
    # Provide step-by-step instructions
    echo "📋 Step-by-step instructions:"
    echo "1. Open http://localhost:3000 in your browser"
    echo "2. Go to Settings → Connections"
    echo "3. Click 'Add Connection' or '+ New Connection'"
    echo "4. Select 'OpenAI API'"
    echo "5. Fill in these values:"
    echo "   - Name: InfoBlox Assistant"
    echo "   - API Base URL: $RECOMMENDED_URL"
    echo "   - API Key: dummy-key"
    echo "6. Click 'Save'"
    echo "7. Refresh your browser (F5 or Cmd+R)"
    echo "8. Select 'infoblox-assistant' from the model dropdown"
    echo ""
    
    # Test the recommended URL
    log_info "Testing recommended URL..."
    if [ "$CONNECTION_METHOD" = "container" ]; then
        # Test from within Open WebUI container
        if docker-compose exec -T open-webui curl -s "$RECOMMENDED_URL/models" | grep -q "infoblox-assistant"; then
            log_success "✅ URL verified working!"
        else
            log_warning "URL test inconclusive"
        fi
    else
        # Test from host
        if curl -s "$RECOMMENDED_URL/models" | grep -q "infoblox-assistant"; then
            log_success "✅ URL verified working!"
        else
            log_warning "URL test inconclusive"
        fi
    fi
    
else
    log_error "No working connection method found!"
    echo ""
    echo "🔧 Troubleshooting steps:"
    echo "1. Check if services are running: docker-compose ps"
    echo "2. Check logs: docker-compose logs infoblox-mcp"
    echo "3. Restart services: docker-compose restart"
    echo "4. Check firewall settings"
    echo ""
fi

# Additional troubleshooting info
echo "🔍 Additional Information:"
echo "========================="
echo "InfoBlox MCP Status:"
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    curl -s http://localhost:8000/health | python3 -m json.tool 2>/dev/null || curl -s http://localhost:8000/health
else
    echo "❌ InfoBlox MCP not accessible from host"
fi

echo ""
echo "Open WebUI Status:"
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Open WebUI accessible at http://localhost:3000"
else
    echo "❌ Open WebUI not accessible"
fi

echo ""
echo "Container Network Info:"
docker-compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "🆘 If you're still having issues:"
echo "1. Check the logs: docker-compose logs -f"
echo "2. Restart everything: docker-compose down && docker-compose up -d"
echo "3. Make sure no firewall is blocking the ports"
echo "4. Try accessing from the browser: http://localhost:8000/health"
